{"name": "hifi-frontend", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.12.1", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@mui/material-nextjs": "^7.1.1", "@mui/x-data-grid": "^8.5.2", "@mui/x-date-pickers": "^8.5.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.80.7", "@tanstack/react-table": "^8.21.3", "antd": "^5.26.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dnd-kit": "^0.0.2", "lodash": "^4.17.21", "lucide-react": "^0.515.0", "material-react-table": "^3.2.1", "next": "15.3.3", "primeicons": "^7.0.0", "primereact": "^10.9.6", "react": "19.1.0", "react-beautiful-dnd": "^13.1.1", "react-datetime-picker": "^6.0.1", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.3", "remixicon": "^4.6.0", "swiper": "^11.2.8", "tailwind-merge": "^3.3.1", "zod": "^3.25.64"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.78.0", "@types/lodash": "^4.17.18", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-router-dom": "^5.3.3", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}