# nixpack.toml - for Next.js + pnpm using latest Node.js LTS and pnpm

providers = []

# Use a modern Nixpacks base image
buildImage = 'ghcr.io/railwayapp/nixpacks:ubuntu-**********'

[variables]
CI = 'true'
NIXPACKS_METADATA = 'node'
NODE_ENV = 'production'
NPM_CONFIG_PRODUCTION = 'false'

[phases.setup]
# Use latest Node.js LTS and latest PNPM
nixPkgs = ['nodejs_22', 'pnpm']
nixLibs = ['gcc-unwrapped']
nixOverlays = ['https://github.com/railwayapp/nix-npm-overlay/archive/main.tar.gz']
# Optional: pin to a stable nixpkgs commit if needed
nixpkgsArchive = 'ffeebf0acf3ae8b29f8c7049cd911b9636efd7e7'
aptPkgs = ['curl', 'wget']

[phases.install]
dependsOn = ['setup']
cmds = ['pnpm install --frozen-lockfile']
cacheDirectories = ['/root/.local/share/pnpm/store/v3']
paths = ['/app/node_modules/.bin']

[phases.build]
dependsOn = ['install']
cmds = ['pnpm run build']
cacheDirectories = ['.next/cache', 'node_modules/.cache']

[start]
cmd = 'pnpm run start'

[ports]
http = 3000