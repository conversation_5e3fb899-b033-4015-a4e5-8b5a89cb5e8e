import {
  LayoutDashboard,
  Calendar<PERSON>heck,
  Clipboard<PERSON>ist,
  DollarSign,
  FileText,
  Syringe,
  Boxes,
  FlaskConical,
  Radiation,
  Bed,
  // Stethoscope,
  Shield,
  Users,
  // FileCog,
  Banknote,
  Hospital,
  CreditCard,
  HeartPulse,
  Heart,
  Droplets,
  FileSignature,
  BookOpenCheck,
  Settings,
  FileSearch,
  NotebookPen,
  // FileBarChart,
  Brain,
  Eye,
  ScanEye,
  ChefHat,
  Smile,
  AlarmClock,
  Star,
  PhoneCall,
  UserCircle,
} from "lucide-react";

export type SidebarSubItem = {
  title: string;
  url: string;
  icon: React.ElementType;
};

export type SidebarGroupItem = {
  title: string;
  icon: React.ElementType;
  key: string;
  items: SidebarSubItem[];
  url?: string;
};

export const sidebarGroups: SidebarGroupItem[] = [
  {
    title: "Registration",
    icon: Hospital,
    key: "registration",
    items: [
      { title: "Patient Registration", url: "/registration/patient", icon: Users },
      { title: "Registration Dashboard", url: "/registration/dashboard", icon: LayoutDashboard },
      { title: "Visit Checkout", url: "/registration/checkout", icon: FileText },
    ],
  },
  {
    title: "Appointments",
    icon: CalendarCheck,
    key: "appointments",
    items: [
      { title: "Offline Appointments", url: "/appointments/offline", icon: CalendarCheck },
      { title: "Online Appointments", url: "/appointments/online", icon: CalendarCheck },
      { title: "Appointment Masters", url: "/appointments/masters", icon: Settings },
    ],
  },
  {
    title: "Billing",
    icon: DollarSign,
    key: "billing",
    items: [
      { title: "OPD Billing", url: "/billing/opd", icon: DollarSign },
      { title: "OPD Receipts", url: "/billing/opd-receipts", icon: FileText },
      { title: "Patient Ledger", url: "/billing/patient-ledger", icon: FileText },
      { title: "OPD Billing Discounts", url: "/billing/opd-discounts", icon: FileText },
      { title: "OPD Bill Update", url: "/billing/opd-update", icon: FileText },
      { title: "OPD Bill Approvals", url: "/billing/opd-approvals", icon: FileText },
      { title: "IPD Billing", url: "/billing/ipd", icon: DollarSign },
      { title: "IPD Receipts", url: "/billing/ipd-receipts", icon: FileText },
      { title: "IPD Billing Discounts", url: "/billing/ipd-discounts", icon: FileText },
      { title: "IPD Bill Update", url: "/billing/ipd-update", icon: FileText },
      { title: "IPD Bill Approvals", url: "/billing/ipd-approvals", icon: FileText },
      { title: "Bill Ledger", url: "/billing/ledger", icon: FileText },
      { title: "Consolidated Payments", url: "/billing/consolidated", icon: Banknote },
      { title: "Billing Masters", url: "/billing/masters", icon: Settings },
    ],
  },
  {
    title: "Pharmacy",
    icon: Syringe,
    key: "pharmacy",
    items: [
      { title: "OP Pharmacy", url: "/pharmacy/op", icon: Syringe },
      { title: "OP Returns", url: "/pharmacy/op-returns", icon: FileText },
      { title: "OP - Multi Store Billing", url: "/pharmacy/op-multistore", icon: Boxes },
      { title: "IP Pharmacy", url: "/pharmacy/ip", icon: Syringe },
      { title: "IP Returns", url: "/pharmacy/ip-returns", icon: FileText },
      { title: "Bill Ledger", url: "/pharmacy/ledger", icon: FileText },
      { title: "Pharma Transfer", url: "/pharmacy/transfer", icon: ClipboardList },
    ],
  },
  {
    title: "Inventory",
    icon: Boxes,
    key: "inventory",
    items: [
      { title: "Purchase Entry", url: "/inventory/purchase-entries", icon: ClipboardList },
      { title: "Purchase Returns", url: "/inventory/purchase-returns", icon: ClipboardList },
      { title: "Inventory Masters", url: "/inventory/masters", icon: Settings },
      { title: "Vendor Payments", url: "/inventory/vendor-payments", icon: DollarSign },
      { title: "Vendor Credits", url: "/inventory/vendor-credits", icon: CreditCard },
      { title: "Opening Stock", url: "/inventory/opening-stock", icon: CreditCard},
    ],
  },
  {
    title: "LIS",
    icon: FlaskConical,
    key: "lis",
    items: [],
    url: "/lis",
  },
  {
    title: "RIS",
    icon: Radiation,
    key: "ris",
    items: [],
    url: "/ris",
  },
  {
    title: "Other Diagnosis",
    icon: Brain,
    key: "other-diagnosis",
    items: [],
    url: "/other-diagnosis",
  },
  {
    title: "IP - ATD",
    icon: Bed,
    key: "ip-atd",
    items: [],
    url: "/ip-atd",
  },
  {
    title: "Insurance Management",
    icon: Shield,
    key: "insurance",
    items: [],
    url: "/insurance",
  },
  {
    title: "OT Management",
    icon: Bed,
    key: "ot-management",
    items: [],
    url: "/ot-management",
  },
  {
    title: "CATH Lab Management",
    icon: HeartPulse,
    key: "cath-lab",
    items: [],
    url: "/cath-lab",
  },
  {
    title: "IVF Lab Management",
    icon: Heart,
    key: "ivf-lab",
    items: [],
    url: "/ivf-lab",
  },
  {
    title: "Blood Bank",
    icon: Droplets,
    key: "blood-bank",
    items: [],
    url: "/blood-bank",
  },
  {
    title: "MRD Management",
    icon: FileSearch,
    key: "mrd-management",
    items: [],
    url: "/mrd-management",
  },
  {
    title: "CSSD Management",
    icon: FileSignature,
    key: "cssd-management",
    items: [],
    url: "/cssd-management",
  },
  {
    title: "Manifold & Central Supply",
    icon: Boxes,
    key: "central-supply",
    items: [],
    url: "/central-supply",
  },
  {
    title: "EMR",
    icon: BookOpenCheck,
    key: "emr",
    items: [],
    url: "/emr",
  },
  {
    title: "Housekeeping",
    icon: NotebookPen,
    key: "housekeeping",
    items: [],
    url: "/housekeeping",
  },
  {
    title: "eMAR",
    icon: FileText,
    key: "emar",
    items: [],
    url: "/emar",
  },
  {
    title: "Accounts",
    icon: Banknote,
    key: "accounts",
    items: [
      { title: "Cash Counter", url: "/accounts/cash", icon: DollarSign },
      { title: "Cash Reconciliation", url: "/accounts/cash-recon", icon: FileText },
      { title: "Bank Statements", url: "/accounts/bank", icon: FileText },
      { title: "Bank Reconciliation", url: "/accounts/bank-recon", icon: FileText },
      { title: "RazorPay Reconciliation", url: "/accounts/razorpay", icon: CreditCard },
      { title: "PineLabs Reconciliation", url: "/accounts/pinelabs", icon: CreditCard },
      { title: "PhonePe Reconciliation", url: "/accounts/phonepe", icon: CreditCard },
    ],
  },
  {
    title: "Physiotherapy",
    icon: HeartPulse,
    key: "physiotherapy",
    items: [],
    url: "/physiotherapy",
  },
  {
    title: "Dental",
    icon: Eye,
    key: "dental",
    items: [],
    url: "/dental",
  },
  {
    title: "Ophthalmology",
    icon: Eye,
    key: "ophthalmology",
    items: [],
    url: "/ophthalmology",
  },
  {
    title: "Endoscope Management",
    icon: ScanEye,
    key: "endoscope",
    items: [],
    url: "/endoscope",
  },
  {
    title: "Quality Management",
    icon: Star,
    key: "quality",
    items: [],
    url: "/quality",
  },
  {
    title: "HR & Payroll",
    icon: UserCircle,
    key: "hr-payroll",
    items: [],
    url: "/hr-payroll",
  },
  {
    title: "Task Management",
    icon: ClipboardList,
    key: "task-management",
    items: [],
    url: "/task-management",
  },
  {
    title: "Accident & Emergency",
    icon: AlarmClock,
    key: "emergency",
    items: [],
    url: "/emergency",
  },
  {
    title: "Diet & Kitchen",
    icon: ChefHat,
    key: "diet-kitchen",
    items: [],
    url: "/diet-kitchen",
  },
  {
    title: "Patient Feedback",
    icon: Smile,
    key: "feedback",
    items: [],
    url: "/feedback",
  },
  {
    title: "Nurse Calling System",
    icon: Smile,
    key: "nurse-calling",
    items: [],
    url: "/nurse-calling",
  },
  {
    title: "CRM",
    icon: PhoneCall,
    key: "crm",
    items: [],
    url: "/crm",
  },
  {
    title: "Corporate Billing",
    icon: Banknote,
    key: "corporate-billing",
    items: [],
    url: "/corporate-billing",
  },
];

export const sideFoorbarGroups: SidebarGroupItem[] = [
  {
    title: "Settings",
    icon: Settings,
    key: "settings",
    items: [
      { title: "Logout", url: "/registration/patient", icon: Users },
    ],
  }
];
