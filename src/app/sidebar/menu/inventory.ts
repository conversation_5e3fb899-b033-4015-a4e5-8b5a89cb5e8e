import {
  Boxes,
  PackageSearch,
  Warehouse,
  Tags,
  Percent,
  ClipboardList,
  ListChecks,
  Building2,
  Landmark,
  Box,
  Layers3,
  Scale,
  FileText,
  ShoppingCart,
  CircleDollarSign,
  FileStack,
  Users,
} from "lucide-react";

export const inventoryItems = [
  { title: "Molecular", url: "/inventory/molecular", icon: Boxes },
  { title: "Vendors", url: "/inventory/vendors", icon: Building2 },
  { title: "UOMs", url: "/inventory/uoms", icon: Scale },
  { title: "Generic", url: "/inventory/generic", icon: PackageSearch },
  { title: "Route of Administration", url: "/inventory/route-of-administration", icon: Landmark },
  { title: "Store Types", url: "/inventory/store-types", icon: Warehouse },
  { title: "Category", url: "/inventory/category", icon: Tags },
  { title: "Combination", url: "/inventory/combination", icon: Layers3 },
  { title: "GST", url: "/inventory/gst", icon: Percent },
  { title: "Dosage Form", url: "/inventory/dosage-form", icon: ClipboardList },
  { title: "Offer Terms", url: "/inventory/offer-terms", icon: FileText },
  { title: "GST ITC", url: "/inventory/gst-itc", icon: Percent },
  { title: "Purchase Entries", url: "/inventory/purchase-entries", icon: ShoppingCart },
  { title: "Purchase Order Item", url: "/inventory/purchase-order-item", icon: ListChecks },
  { title: "Dosage Form ROA", url: "/inventory/dosage-form-roa", icon: FileStack },
  { title: "Items", url: "/inventory/items", icon: Box },
  { title: "Stores", url: "/inventory/stores", icon: Warehouse },
  { title: "Item Batch", url: "/inventory/item-batch", icon: Box },
  { title: "Item UOM", url: "/inventory/item-uom", icon: Scale },
  { title: "Payment Term", url: "/inventory/payment-term", icon: CircleDollarSign },
  { title: "Store-Types", url: "/inventory/store-types-alt", icon: Warehouse },
];


export const userItems = [
  { title: "Users", url: "/users", icon: Users },
  { title: "Roles", url: "/users/roles", icon: Users },
];
