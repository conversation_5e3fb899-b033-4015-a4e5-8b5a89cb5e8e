"use client";

import {
  <PERSON><PERSON>,
  <PERSON>barContent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
  SidebarMenuSub,
  SidebarTrigger,
} from "@/components/ui/sidebar";

import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";

import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { sidebarGroups, sideFoorbarGroups } from "./sidebarMenu.config";
import { usePathname } from "next/navigation";
import { TooltipProvider } from "@/components/ui/tooltip";

export function AppSidebar() {
  const [openGroup, setOpenGroup] = useState<string | null>(null);
  const { open: isSidebarOpen } = useSidebar();
  const pathname = usePathname();

  const activeClass = "text-[#1976d2] font-semibold bg-muted";
  const glassSelectedClass =
    "bg-[#3A59D1] backdrop-blur-sm rounded-md border border-blue-200/30 shadow-sm text-white hover:bg-[#2F4BC1] active:bg-[#2F4BC1]";

  const isActive = (url: string) => pathname === url;

  const isGroupActive = (group: (typeof sidebarGroups)[number]) => {
    if (group.items?.length > 0) {
      return group.items.some((item) => isActive(item.url));
    }
    return group.url ? isActive(group.url) : false;
  };

  const handleToggle = (key: string) => {
    setOpenGroup((prev) => (prev === key ? null : key));
  };

  useEffect(() => {
    const activeGroup = sidebarGroups.find((group) =>
      group.items?.some((item) => pathname === item.url)
    );

    if (activeGroup) {
      setOpenGroup(activeGroup.key);
    }
  }, [pathname]);

  return (
    <TooltipProvider>
      <Sidebar variant="sidebar" collapsible="icon">
        <SidebarHeader>
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                  <SidebarTrigger className="mr-2" >
                    <span className="flex-1">Dashboard</span>
                  </SidebarTrigger>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarHeader>

        <SidebarContent className="flex-1 min-h-0 overflow-y-auto">
          <SidebarMenu>
            {sidebarGroups.map((group) => {
              const hasChildren = group.items?.length > 0;
              const groupActive = isGroupActive(group);

              // COLLAPSED SIDEBAR
              if (!isSidebarOpen) {
                if (hasChildren) {
                  return (
                    <DropdownMenu key={group.key}>
                      <DropdownMenuTrigger asChild>
                        <SidebarMenuItem
                          title={group.title}
                          className="my-1 mx-2"
                        >
                          <SidebarMenuButton
                            className={cn(groupActive && glassSelectedClass)}
                          >
                            <group.icon size={18} />
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent side="right" align="start">
                        <DropdownMenuItem
                          disabled
                          className="text-[10px] text-muted-foreground"
                        >
                          {group.title}
                        </DropdownMenuItem>
                        {group.items.map((item) => (
                          <DropdownMenuItem key={item.url} asChild>
                            <Link
                              href={item.url}
                              className={cn(
                                "flex items-center gap-2 text-xs px-2 py-1.5 rounded hover:bg-muted transition-colors",
                                isActive(item.url) && activeClass
                              )}
                            >
                              <item.icon size={14} />
                              {item.title}
                            </Link>
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  );
                } else {
                  return (
                    <SidebarMenuItem key={group.key} className="my-1 mx-2">
                      <SidebarMenuButton asChild>
                        <Link
                          href={group.url || "#"}
                          className={cn(
                            "flex items-center justify-center p-2 rounded transition-colors",
                            groupActive ? glassSelectedClass : "hover:bg-muted"
                          )}
                          title={group.title}
                        >
                          <group.icon size={18} />
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                }
              }

              // EXPANDED SIDEBAR
              if (hasChildren) {
                return (
                  <div key={group?.key}>
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        onClick={() => handleToggle(group.key)}
                        className={cn(
                          "flex items-center w-full text-sm",
                          groupActive && glassSelectedClass
                        )}
                      >
                        <group.icon size={16} className="mr-2" />
                        <span className="flex-1">{group.title}</span>
                        <ChevronRight
                          size={16}
                          className={cn(
                            "ml-auto transition-transform",
                            openGroup === group.key && "rotate-90"
                          )}
                        />
                      </SidebarMenuButton>
                    </SidebarMenuItem>

                    {openGroup === group.key && (
                      <SidebarMenuSub>
                        {group.items.map((item) => (
                          <SidebarMenuItem key={item.url}>
                            <SidebarMenuButton asChild>
                              <Link
                                href={item.url}
                                className={cn(
                                  "flex items-center gap-2 text-xs px-2 py-1 rounded hover:bg-muted transition-colors",
                                  "text-muted-foreground",
                                  isActive(item.url) && activeClass
                                )}
                              >
                                <item.icon size={14} />
                                {item.title}
                              </Link>
                            </SidebarMenuButton>
                          </SidebarMenuItem>
                        ))}
                      </SidebarMenuSub>
                    )}
                  </div>
                );
              }

              // EXPANDED NO CHILDREN
              return (
                <SidebarMenuItem key={group.key}>
                  <SidebarMenuButton asChild>
                    <Link
                      href={group.url || "#"}
                      className={cn(
                        "flex items-center gap-2 text-sm px-2 py-1 rounded hover:bg-muted transition-colors",
                        groupActive ? glassSelectedClass : ""
                      )}
                    >
                      <group.icon size={16} className="mr-2" />
                      {group.title}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              );
            })}
          </SidebarMenu>
        </SidebarContent>

        {/* Footer */}
        <SidebarFooter>
          <SidebarMenu>
            {sideFoorbarGroups.map((group) => {
              const footerGroupActive = isGroupActive(group);
              return (
                <DropdownMenu key={group.key}>
                  <DropdownMenuTrigger asChild>
                    <SidebarMenuItem title={group.title} className="my-1 mx-2">
                      <SidebarMenuButton
                        className={cn(footerGroupActive && glassSelectedClass)}
                      >
                        <group.icon size={18} />
                        {group.title}
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent side="right" align="start">
                    <DropdownMenuItem
                      disabled
                      className="text-[10px] text-muted-foreground"
                    >
                      {group.title}
                    </DropdownMenuItem>
                    {group.items.map((item) => (
                      <DropdownMenuItem key={item.url} asChild>
                        <Link
                          href={item.url}
                          className={cn(
                            "flex items-center gap-2 text-xs px-2 py-1.5 rounded hover:bg-muted transition-colors",
                            isActive(item.url) && activeClass
                          )}
                        >
                          <item.icon size={14} />
                          {item.title}
                        </Link>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              );
            })}
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>
    </TooltipProvider>
  );
}
