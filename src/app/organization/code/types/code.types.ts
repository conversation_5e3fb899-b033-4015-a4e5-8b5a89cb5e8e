import { BaseResponse, PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";
import { MRT_ColumnDef } from "material-react-table";
import { z } from "zod";

// title.types.ts
export interface TitleResponse extends BaseResponse, Record<string, unknown> {
    code: string;
    title: string;
    status: 'active' | 'inactive';
    description: string | null;
    organization_id: number;
}

export const titleColumns: MRT_ColumnDef<TitleResponse>[] = [
    { accessorKey: 'id', header: 'ID' },
    { accessorKey: 'code', header: 'Code' },
    { accessorKey: 'title', header: 'Name' },
    { accessorKey: 'description', header: 'Description' },
    { accessorKey: 'organization', header: 'Organization' },
    { accessorKey: 'status', header: 'Status' },
    { accessorKey: 'created_at', header: 'Created At' },
    { accessorKey: 'updated_at', header: 'Updated At' },
    { accessorKey: 'created_by', header: 'Created By' },
    { accessorKey: 'updated_by', header: 'Updated By' },
];

export type TitlePaginatedResponse = PaginatedResponse<TitleResponse>;
export type TitleSuccessResponse = SuccessResponse<TitleResponse>;

export const titleRequestSchema = z.object({
    name: z.string().min(1, "Name is required"),
    description: z.string().nullable(),
    status: z.enum(["active", "inactive"]),
    organization_id: z.number().int().positive("Organization ID must be a positive number"),
});

export type TitleRequest = z.infer<typeof titleRequestSchema>;