'use client';

import { titleColumns, TitleResponse } from "./types/title.types";
import { useListTitles } from "./query/title.query";
import { CommonTable } from "@/app/inventory/common/table/common-table";
import { useRouter } from "next/navigation";
import { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteTitle } from "./query/title.query";

function Title() {
  const router = useRouter();
  const deleteMutation = useDeleteTitle();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<TitleResponse | null>(null);

  const handleOpenDelete = (row: TitleResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<TitleResponse>
        title="Titles"
        columns={titleColumns}
        useDataQuery={useListTitles}
        hiddenColumns={["created_at", "updated_at", "created_by", "updated_by"]}
        onCreate={() => router.push("/organization/title/create")}
        onExport={() => console.log("Export Titles")}
        onEdit={(row) => router.push(`/organization/title/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete title{" "}
            <strong>{selectedRow?.title}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default Title;