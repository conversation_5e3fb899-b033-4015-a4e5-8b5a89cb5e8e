import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createTitle,
  deleteTitle,
  getTitle,
  listTitles,
  updateTitle,
} from "../api/title.api";
import { TitleRequest, TitleSuccessResponse } from "../types/title.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListTitles = (page?: number, limit?: number) => {
  return useQuery({
    queryKey: ["titles", page, limit],
    queryFn: () => listTitles(page, limit),
  });
};

export const useGetTitle = (id: number | undefined) => {
  return useQuery({
    queryKey: ["titles", id],
    queryFn: () => getTitle(id!),
    enabled: !!id,
  });
};

export const useCreateTitle = () => {
  const queryClient = useQueryClient();

  return useMutation<
    TitleSuccessResponse,
    ApiErrorResponse,
    TitleRequest
  >({
    mutationFn: createTitle,
    mutationKey: ["createTitle"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["titles"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateTitle = () => {
  const queryClient = useQueryClient();

  return useMutation<
    TitleSuccessResponse,
    ApiErrorResponse,
    { id: number; titleRequest: TitleRequest }
  >({
    mutationKey: ["updateTitle"],
    mutationFn: ({ id, titleRequest }) => updateTitle(id, titleRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["titles"] });
      toast.success(data.message);
    },
    onError: (error: ApiErrorResponse) => {
      toast.error(
        `Edit failed: ${error.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteTitle = () => {
  const queryClient = useQueryClient();

  return useMutation<TitleSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteTitle"],
    mutationFn: (id: number) => deleteTitle(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["titles"] });
      toast.success(data.message || "Title deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error.response?.data?.message || error.message}`
      );
    },
  });
};
