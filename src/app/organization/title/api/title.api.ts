import apiClient from "@/app/api/api";
import { TitlePaginatedResponse, TitleResponse, TitleSuccessResponse, TitleRequest } from "../types/title.types";

export const listTitles = async (
  page?: number,
  limit?: number
): Promise<TitlePaginatedResponse> => {
  const res = await apiClient.get<TitlePaginatedResponse>("/titles", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
    },
  });
  return res.data;
};

export const getTitle = async (
  id: number
): Promise<TitleResponse> => {
  const res = await apiClient.get<TitleResponse>(`/titles/${id}`);
  return res.data;
};

export const createTitle = async (
  titleRequest: TitleRequest
): Promise<TitleSuccessResponse> => {
  const res = await apiClient.post<TitleSuccessResponse>("/titles", titleRequest);
  return res.data;
};

export const updateTitle = async (
  id: number,
  titleRequest: TitleRequest
): Promise<TitleSuccessResponse> => {
  const res = await apiClient.patch<TitleSuccessResponse>(`/titles/${id}`, titleRequest);
  return res.data;
};

export const deleteTitle = async (
  id: number
): Promise<TitleSuccessResponse> => {
  const res = await apiClient.delete<TitleSuccessResponse>(`/titles/${id}`);
  return res.data;
};