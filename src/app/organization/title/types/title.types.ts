import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";

export interface TitleResponse extends Record<string, unknown> {
  id: number;
  title: string;
  status: string;
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
}

export const titleColumns: MRT_ColumnDef<TitleResponse>[] = [
  { accessorKey: 'id', header: 'ID' },
  { accessorKey: 'title', header: 'Title' },
  { accessorKey: 'status', header: 'Status' },
  { accessorKey: 'created_at', header: 'Created At' },
  { accessorKey: 'updated_at', header: 'Updated At' },
  { accessorKey: 'created_by', header: 'Created By' },
  { accessorKey: 'updated_by', header: 'Updated By' },
];

export type TitlePaginatedResponse = PaginatedResponse<TitleResponse>;
export type TitleSuccessResponse = SuccessResponse<TitleResponse>;

export const titleRequestSchema = z.object({
  title: z.string().min(2, "Title must be at least 2 characters").max(50, "Title cannot exceed 50 characters"),
  status: z.string().max(20, "Status cannot exceed 20 characters"),
  created_by: z.number().int().positive("Created By must be a positive number"),
  updated_by: z.number().int().positive("Updated By must be a positive number"),
});

export type TitleRequest = z.infer<typeof titleRequestSchema>;