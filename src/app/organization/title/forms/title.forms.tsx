"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uI<PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  Card
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateTitle,
  useGetTitle,
  useUpdateTitle,
} from "../query/title.query";
import { useRef, useEffect } from "react";
import { titleRequestSchema, TitleRequest } from "../types/title.types";
import { useRouter } from "next/navigation";

type Props = {
  id?: number;
};

const statusOptions = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function TitleForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: titleData, isLoading } = useGetTitle(id);
  const createMutation = useCreateTitle();
  const updateMutation = useUpdateTitle();

  const titleRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<TitleRequest>({
    resolver: zodResolver(titleRequestSchema),
    defaultValues: {
      title: "",
      status: "active",
      created_by: 1, // This should be replaced with actual user ID from auth
      updated_by: 1, // This should be replaced with actual user ID from auth
    },
  });

  useEffect(() => {
    if (titleRef.current) {
      titleRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isEditMode && titleData) {
      reset({
        title: titleData.title,
        status: titleData.status,
        created_by: titleData.created_by,
        updated_by: titleData.updated_by,
      });
    }
  }, [isEditMode, titleData, reset]);

  const onSubmit = (data: TitleRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, titleRequest: data },
        {
          onSuccess: () => {
            router.push("/organization/title");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          router.push("/organization/title");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: 600, margin: "auto", my: 4, p: 2 }}>
      <Box
        component="form"
        onSubmit={handleSubmit(onSubmit)}
        sx={{ display: "flex", flexDirection: "column", gap: 2 }}
      >
        <Typography variant="h6">
          {isEditMode ? "Edit Title" : "Create Title"}
        </Typography>

        <Controller
          name="title"
          control={control}
          render={({ field }) => (
            <TextField
              inputRef={titleRef}
              label="Title"
              size="small"
              fullWidth
              {...field}
              error={!!errors.title}
              helperText={errors.title?.message}
              autoFocus
            />
          )}
        />

        <Controller
          name="status"
          control={control}
          render={({ field }) => (
            <TextField
              select
              label="Status"
              size="small"
              fullWidth
              {...field}
              error={!!errors.status}
              helperText={errors.status?.message}
            >
              {statusOptions.map((opt) => (
                <MenuItem key={opt.value} value={opt.value}>
                  {opt.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />

        <Box sx={{ display: "flex", gap: 2 }}>
          <Button variant="outlined" color="inherit" onClick={() => router.push("/organization/title")}>
            Cancel
          </Button>
          <Button type="submit" variant="contained">
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}