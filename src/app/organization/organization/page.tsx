// page.tsx
"use client";

import { CommonTable } from "@/app/inventory/common/table/common-table";
import {
  organizationColumns,
  OrganizationResponse,
} from "./types/organization.types";
import {
  useDeleteOrganization,
  useListOrganization,
} from "./query/organization.query";
import { useRouter } from "next/navigation";
import { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";

function OrganizationPage() {
  const router = useRouter();
  const deleteMutation = useDeleteOrganization();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<OrganizationResponse | null>(
    null
  );

  const handleOpenDelete = (row: OrganizationResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<OrganizationResponse>
        title="Organizations"
        columns={organizationColumns}
        useDataQuery={useListOrganization}
        hiddenColumns={["createdAt", "updatedAt", "createdBy", "updatedBy"]}
        onCreate={() => router.push("/organization/organization/create")}
        onExport={() => console.log("Export Organization")}
        onEdit={(row) =>
          router.push(`/organization/organization/edit/${row.id}`)
        }
        onDelete={(row) => handleOpenDelete(row)}
        isFilterable={true}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete organization item{" "}
            <strong>{selectedRow?.name}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default OrganizationPage;
