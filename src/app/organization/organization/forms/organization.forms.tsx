// organization.forms.ts
"use client";

import {
  Box,
  Button,
  MenuItem,
  TextField,
  Typography,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import {
  OrganizationPaginatedResponse,
  organizationRequestSchema,
  OrganizationResponse,
} from "@/app/organization/organization/types/organization.types"; // Adjust import path as needed
import { z } from "zod";
import React from "react";
import {
  useCreateOrganization,
  useGetOrganization,
  useListOrganization,
  useUpdateOrganization,
} from "../query/organization.query";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { useListCountry } from "../../country/query/country.query";
import {
  CountryPaginatedResponse,
  CountryResponse,
} from "../../country/types/country.types";
import { formatToTZ } from "@/app/common/utils/common.utils";

type OrganizationRequest = z.infer<typeof organizationRequestSchema>;

type Props = {
  id?: number;
  data?: OrganizationRequest;
  // You can add update and create hooks or callbacks here if needed
};

export default function OrganizationForm({ id }: Props) {
  const isEditMode = !!id;
  const router = useRouter();
  const { data: organizationData } = useGetOrganization(id);
  const createMutation = useCreateOrganization();
  const updateMutation = useUpdateOrganization();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<OrganizationRequest>({
    resolver: zodResolver(organizationRequestSchema),
    defaultValues: {
      name: "",
      abbreviation: "",
      currencyId: 0,
      description: "",
      type: "",
      incorporationDate: "",
      phone: "",
      dialCode: "",
      taxId: "",
      faxId: "",
      email: "",
      parentOrganizationId: undefined,
      line1: "",
      line2: "",
      zipcode: "",
      countryId: 0,
      latitude: undefined,
      longitude: undefined,
      streetName: "",
      streetNumber: "",
      apartmentNumber: "",
      apartmentName: "",
    },
  });

  // Reset with fetched data if editing
  React.useEffect(() => {
    if (isEditMode && organizationData) {
      const formattedData = {
        ...organizationData,
        incorporationDate: organizationData?.incorporationDate?.split("T")[0],
        status: organizationData?.status
      };
      reset(formattedData);
    }
  }, [isEditMode, organizationData, reset]);

  const onSubmit: SubmitHandler<OrganizationRequest> = (
    formData: OrganizationRequest
  ) => {
    const formattedDate = formData.incorporationDate
      ? formatToTZ(formData.incorporationDate)
      : "";

    const updatedFormData: OrganizationRequest = {
      ...formData,
      incorporationDate: formattedDate,
    };

    if (isEditMode && id) {
      updateMutation.mutate(
        { id, organizationRequest: updatedFormData },
        {
          onSuccess: () => {
            router.push("/organization/organization");
          },
        }
      );
    } else {
      createMutation.mutate(updatedFormData, {
        onSuccess: () => {
          router.push("/organization/organization");
        },
      });
    }
  };

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", mb: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Organization" : "Create Organization"}
      </Typography>
      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Name */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Name"
                  size="small"
                  fullWidth
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  required
                  autoFocus
                />
              )}
            />
          </Grid>

          {/* Type */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="type"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Type"
                  size="small"
                  fullWidth
                  error={!!errors.type}
                  helperText={errors.type?.message}
                  required
                />
              )}
            />
          </Grid>

          {/* Abbreviation */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="abbreviation"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Abbreviation"
                  size="small"
                  fullWidth
                  error={!!errors.abbreviation}
                  helperText={errors.abbreviation?.message}
                  required
                />
              )}
            />
          </Grid>

          {/* countryId */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="countryId"
              control={control}
              rules={{ required: "Country is required" }}
              render={({ field }) => (
                <CommonDropdown<
                  CountryResponse,
                  CountryPaginatedResponse,
                  number
                >
                  label="Country"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListCountry}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.countryId}
                  helperText={errors.countryId?.message}
                />
              )}
            />
          </Grid>

          {/* Incorporation Date */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="incorporationDate"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Incorporation Date"
                  type="date"
                  size="small"
                  slotProps={{ inputLabel: { shrink: true } }}
                  fullWidth
                  error={!!errors.incorporationDate}
                  helperText={errors.incorporationDate?.message}
                  required
                />
              )}
            />
          </Grid>

          {/* Description */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Description"
                  size="small"
                  multiline
                  fullWidth
                  error={!!errors.description}
                  helperText={errors.description?.message}
                />
              )}
            />
          </Grid>

          {/* Currency ID */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="currencyId"
              control={control}
              rules={{ required: "Country is required" }}
              render={({ field }) => (
                <CommonDropdown<
                  CountryResponse,
                  CountryPaginatedResponse,
                  number
                >
                  label="Currency"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListCountry}
                  labelKey="currency"
                  valueKey="id"
                  searchable
                  searchKey="currency"
                  error={!!errors.currencyId}
                  helperText={errors.currencyId?.message}
                />
              )}
            />
          </Grid>

          {/* Phone Number Group */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Box display="flex" alignItems="flex-start" gap={1}>
              {/* Dial Code - 1 unit width */}
              <Box flex={1}>
                <Controller
                  name="dialCode"
                  control={control}
                  rules={{ required: "dial code is required" }}
                  render={({ field }) => (
                    <CommonDropdown<
                      CountryResponse,
                      CountryPaginatedResponse,
                      string
                    >
                      label="code"
                      value={field.value ? field.value.toString() : ""}
                      onChange={field.onChange}
                      useDataQuery={useListCountry}
                      labelKey="dialCode"
                      valueKey="dialCode"
                      searchable
                      searchKey="dialCode"
                      error={!!errors.dialCode}
                      helperText={errors.dialCode?.message}
                    />
                  )}
                />
              </Box>

              {/* Phone - 3 units width */}
              <Box flex={3}>
                <Controller
                  name="phone"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Phone"
                      size="small"
                      fullWidth
                      error={!!errors.phone}
                      helperText={errors.phone?.message}
                    />
                  )}
                />
              </Box>
            </Box>
          </Grid>

          {/* Tax ID */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="taxId"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Tax Number"
                  size="small"
                  fullWidth
                  error={!!errors.taxId}
                  helperText={errors.taxId?.message}
                />
              )}
            />
          </Grid>

          {/* Fax ID */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="faxId"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Fax Number"
                  size="small"
                  fullWidth
                  error={!!errors.faxId}
                  helperText={errors.faxId?.message}
                />
              )}
            />
          </Grid>

          {/* Email */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Email"
                  type="email"
                  size="small"
                  fullWidth
                  error={!!errors.email}
                  helperText={errors.email?.message}
                />
              )}
            />
          </Grid>

          {/* Parent Organization ID */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="parentOrganizationId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<
                  OrganizationResponse,
                  OrganizationPaginatedResponse
                >
                  label="Parent Organization"
                  value={field.value ? field.value.toString() : ""}
                  onChange={field.onChange}
                  useDataQuery={useListOrganization}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.parentOrganizationId}
                  helperText={errors.parentOrganizationId?.message}
                />
              )}
            />
          </Grid>

          {/* Address Fields */}

          {/* Line1 */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="line1"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Address Line 1"
                  size="small"
                  fullWidth
                  error={!!errors.line1}
                  helperText={errors.line1?.message}
                  required
                />
              )}
            />
          </Grid>

          {/* Line2 */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="line2"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Address Line 2"
                  size="small"
                  fullWidth
                  error={!!errors.line2}
                  helperText={errors.line2?.message}
                />
              )}
            />
          </Grid>

          {/* Street Number */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="streetNumber"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Street Number"
                  size="small"
                  fullWidth
                  error={!!errors.streetNumber}
                  helperText={errors.streetNumber?.message}
                />
              )}
            />
          </Grid>

          {/* Street Name */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="streetName"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Street Name"
                  size="small"
                  fullWidth
                  error={!!errors.streetName}
                  helperText={errors.streetName?.message}
                />
              )}
            />
          </Grid>

          {/* Apartment Number */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="apartmentNumber"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Apartment Number"
                  size="small"
                  fullWidth
                  error={!!errors.apartmentNumber}
                  helperText={errors.apartmentNumber?.message}
                />
              )}
            />
          </Grid>

          {/* Apartment Name */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="apartmentName"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Apartment Name"
                  size="small"
                  fullWidth
                  error={!!errors.apartmentName}
                  helperText={errors.apartmentName?.message}
                />
              )}
            />
          </Grid>

          {/* Zipcode */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="zipcode"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Zipcode"
                  size="small"
                  fullWidth
                  error={!!errors.zipcode}
                  helperText={errors.zipcode?.message}
                />
              )}
            />
          </Grid>

          {/* Latitude */}
          {/* <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="latitude"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Latitude"
                  type="number"
                  size="small"
                  fullWidth
                  error={!!errors.latitude}
                  helperText={errors.latitude?.message}
                  onChange={(e) =>
                    field.onChange(
                      e.target.value ? Number(e.target.value) : undefined
                    )
                  }
                />
              )}
            />
          </Grid> */}

          {/* Longitude */}
          {/* <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="longitude"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Longitude"
                  type="number"
                  size="small"
                  fullWidth
                  error={!!errors.longitude}
                  helperText={errors.longitude?.message}
                  onChange={(e) =>
                    field.onChange(
                      e.target.value ? Number(e.target.value) : undefined
                    )
                  }
                />
              )}
            />
          </Grid> */}

          {isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    <MenuItem value="active">Active</MenuItem>
                    <MenuItem value="inactive">Inactive</MenuItem>
                  </TextField>
                )}
              />
            </Grid>
          )}
        </Grid>

        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button type="submit" variant="contained">
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}
