// organization.api.ts
import apiClient from "@/app/api/api";
import { 
    OrganizationPaginatedResponse, 
    OrganizationSuccessResponse,  
    OrganizationRequest,
    OrganizationFilters,
    OrganizationResponse
} from "../types/organization.types";

export const listOrganization = async (
    page?: number,
    limit?: number,
    filters?: OrganizationFilters
): Promise<OrganizationPaginatedResponse> => {
    const res = await apiClient.get<OrganizationPaginatedResponse>("/organizations", {
        params: {
            ...(page !== undefined && { page }),
            ...(limit !== undefined && { limit }),
            ...filters,
        },
    });
    return res.data;
};

export const getOrganization = async (
    id: number
): Promise<OrganizationResponse> => {
    const res = await apiClient.get<OrganizationResponse>(`/organizations/${id}`);
    return res.data;
}

export const createOrganization = async (
    data: OrganizationRequest
): Promise<OrganizationSuccessResponse> => {
    const res = await apiClient.post<OrganizationSuccessResponse>("/organizations", data);
    return res.data;
}

export const updateOrganization = async (
    id: number,
    data: OrganizationRequest
): Promise<OrganizationSuccessResponse> => {
    const res = await apiClient.patch<OrganizationSuccessResponse>(`/organizations/${id}`, data);
    return res.data;
}

export const deleteOrganization = async (
    id: number
): Promise<OrganizationSuccessResponse> => {
    const res = await apiClient.delete<OrganizationSuccessResponse>(`/organizations/${id}`);
    return res.data;
};