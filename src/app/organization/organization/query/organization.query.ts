// organization.query.ts
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createOrganization,
  deleteOrganization,
  getOrganization,
  listOrganization,
  updateOrganization,
} from "../api/organization.api";
import {
  OrganizationRequest,
  OrganizationFilters,
  OrganizationSuccessResponse,
} from "../types/organization.types";
import { toast } from "react-hot-toast";

import { serialize } from "@/app/common/utils/serialize.utils";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListOrganization = (
  page?: number,
  limit?: number,
  filters: OrganizationFilters = {}
) => {
  const filterKey = serialize(filters as Record<string, unknown>);
  return useQuery({
    queryKey: ["organizations", page, limit, filterKey],
    queryFn: () => listOrganization(page, limit, filters),
  });
};

export const useGetOrganization = (id: number | undefined) => {
  return useQuery({
    queryKey: ["organizations", id],
    queryFn: () => getOrganization(id!),
    enabled: !!id,
  });
};

export const useCreateOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation<
    OrganizationSuccessResponse,
    ApiErrorResponse,
    OrganizationRequest
  >({
    mutationKey: ["createOrganization"],
    mutationFn: createOrganization,
    onSuccess: (data) => {
      toast.success(data.message || "Organization created successfully!");
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
    },
    onError: (error) => {
      toast.error(
        `Creation failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation<
    OrganizationSuccessResponse,
    ApiErrorResponse,
    { id: number; organizationRequest: OrganizationRequest }
  >({
    mutationKey: ["updateOrganization"],
    mutationFn: ({ id, organizationRequest }) =>
      updateOrganization(id, organizationRequest),
    onSuccess: (data) => {
      toast.success(data.message || "Organization updated successfully!");
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation<
    OrganizationSuccessResponse,
    ApiErrorResponse,
    number
  >({
    mutationKey: ["deleteOrganization"],
    mutationFn: deleteOrganization,
    onSuccess: (data) => {
      toast.success(data.message || "Organization deleted successfully!");
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
    },
    onError: (error) => {
      toast.error(
        `Deletion failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
