// organization.types.ts
import { MRT_ColumnDef } from "material-react-table";
import { CountryResponse } from "../../country/types/country.types";
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
import { localTime } from "@/app/common/utils/serialize.utils"; 
import { BaseResponse, PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";

export interface OrganizationProps {
  itemId?: number;
}

export interface OrganizationResponse extends BaseResponse, Record<string, unknown> {
    name: string;
    abbreviation: string;
    currencyId: number;
    currency: CountryResponse | null;
    description: string;
    type: string;
    incorporationDate: string;
    phone: string;
    dialCode: string;
    taxId: string;
    faxId: string;
    email: string;
    status: "active" | "inactive";
}

export const organizationColumns: MRT_ColumnDef<OrganizationResponse>[] = [
    { accessorKey: 'id', header: 'ID', grow: false, size: 50 },
    { accessorKey: 'name', header: 'Name' },
    { accessorKey: 'abbreviation', header: 'Abbreviation' },
    { accessorKey: "currency.currency", header: 'Currency' },
    { accessorKey: 'type', header: 'Type' },
    { 
        accessorKey: 'incorporationDate', 
        header: 'Incorporation Date',
        Cell: ({ cell }) => {
            const status = cell.getValue<string>();
            return localTime(status);
        }
    },
    { accessorKey: 'phone', header: 'Phone' },
    { accessorKey: 'dialCode', header: 'Dial Code' },
    { accessorKey: 'taxId', header: 'Tax ID' },
    { accessorKey: 'faxId', header: 'Fax ID' },
    { accessorKey: 'email', header: 'Email' },
    { 
        accessorKey: 'status', 
        header: 'Status',
        filterVariant: 'select',
        filterSelectOptions: statusOptions.map(value => ({
            value,
            label: statusMap[value],
        })),
        Cell: ({ cell }) => {
            const status = cell.getValue<string>();
            return getStatusLabel(status);
        }
    },
    { 
        accessorKey: 'createdAt', 
        header: 'Created At',
        Cell: ({ cell }) => {
            const status = cell.getValue<string>();
            return localTime(status);
        }
    },
    { 
        accessorKey: 'updatedAt', 
        header: 'Updated At',
        Cell: ({ cell }) => {
            const status = cell.getValue<string>();
            return localTime(status);
        }
    },
    { accessorKey: 'createdBy', header: 'Created By', grow: false, size: 50 },
    { accessorKey: 'updatedBy', header: 'Updated By', grow: false, size: 50 },
];

export type OrganizationPaginatedResponse = PaginatedResponse<OrganizationResponse>;
export type OrganizationSuccessResponse = SuccessResponse<OrganizationResponse>;

export const organizationRequestSchema = z.object({
    name: z.string().nonempty("Name is required"),
    abbreviation: z.string().nonempty("Abbreviation is required"),
    currencyId: z.number().int().positive("CurrencyID is required"),
    description: z.string().optional(),
    type: z.string().nonempty("Type is required"),
    incorporationDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
        message: "Invalid incorporation date",
    }),
    phone: z.string().optional(),
    dialCode: z.string().optional(),
    taxId: z.string().optional(),
    faxId: z.string().optional(),
    email: z.string().email().optional(),
    parentOrganizationId: z.number().int().positive().optional(),
    status: z.enum(["active", "inactive"]),
    line1: z.string().nonempty("Line1 is required"),
    line2: z.string().optional(),
    zipcode: z.string().optional(),
    countryId: z.number().int().positive("CountryID is required"),
    latitude: z.number().optional(),
    longitude: z.number().optional(),
    streetName: z.string().optional(),
    streetNumber: z.string().optional(),
    apartmentNumber: z.string().optional(),
    apartmentName: z.string().optional(),
});

export type OrganizationRequest = z.infer<typeof organizationRequestSchema>;

export interface OrganizationFilters {
    name?: string;
    abbreviation?: string;
    type?: string;
    status?: "active" | "inactive";
    currencyId?: number;
    countryId?: number;
    parentOrganizationId?: number;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string;
    updatedAt?: string;
    search?: string;
}