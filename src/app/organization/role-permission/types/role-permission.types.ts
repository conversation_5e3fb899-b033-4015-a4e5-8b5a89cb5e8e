import { z } from "zod";
import { PermissionResponse } from "../../permission/types/permission.types";
import { BaseResponse, PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";

// rolePermission.types.ts
export interface RolePermissionResponse extends BaseResponse, Record<string, unknown> {
    permissionId: number;
    permission: PermissionResponse;
    create: boolean;
    update: boolean;
    read: boolean;
    delete: boolean;
    share: boolean;
    export: boolean;
    email: boolean;
    import: boolean;
    print: boolean;
    global: boolean;
    own: boolean;
    branch: boolean;
    self: boolean;
}

export type RolePermissionPaginatedResponse = PaginatedResponse<RolePermissionResponse>;
export type RolePermissionSuccessResponse = SuccessResponse<RolePermissionResponse>;

export const rolePermissionRequestSchema = z.object({
  roleId: z.number().int().positive("Role ID must be a positive integer").optional(),
  permissionId: z.number().int().positive("Permission ID must be a positive integer").optional(),
  create: z.boolean().optional(),
  delete: z.boolean().optional(),
  update: z.boolean().optional(),
  read: z.boolean().optional(),
  share: z.boolean().optional(),
  report: z.boolean().optional(),
  email: z.boolean().optional(),
  import: z.boolean().optional(),
  export: z.boolean().optional(),
  print: z.boolean().optional(),
  global: z.boolean().optional(),
  own: z.boolean().optional(),
  branch: z.boolean().optional(),
  self: z.boolean().optional(),
  status: z.enum(["active", "inactive"]).optional(),
});

export type RolePermissionRequest = z.infer<typeof rolePermissionRequestSchema>;

export interface RolePermissionFilters {
  create?: boolean;
  edit?: boolean;
  roleId?: number;
  permissionId?: number;
  status?: 'active' | 'inactive';
  moduleName?: string;
  featureName?: string;
  roleName?: string;
}