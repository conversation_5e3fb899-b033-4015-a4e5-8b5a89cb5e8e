"use client";

import { MRT_ColumnDef } from "material-react-table";
import { Checkbox, MenuItem, Select, SelectChangeEvent } from "@mui/material";
import { useUpdateRolePermission } from "../query/role-permission.query";
import { RolePermissionRequest, RolePermissionResponse } from "./role-permission.types";

type ScopeType = "global" | "own" | "branch" | "self";

export const RolePermissionColumns =
  (): MRT_ColumnDef<RolePermissionResponse>[] => {
    const updateMutation = useUpdateRolePermission();

    const handleBooleanUpdate = ({
      id,
      accessorKey,
      newValue,
    }: {
      id: number;
      accessorKey: string;
      newValue: boolean;
    }) => {
      const requestPayload: RolePermissionRequest = { [accessorKey]: newValue };

      updateMutation.mutate(
        { id, data: requestPayload },
        {
          onSuccess: (response) => {
            console.log("Updated successfully:", response);
          },
        }
      );
    };

    return [
      { accessorKey: "id", 
        header: "ID",
        grow: false,
        size: 50 
      },
      {
        accessorKey: "moduleName",
        accessorFn: (row) => row.permission.moduleName,
        header: "Module",
        enableEditing: false,
      },
      {
        accessorKey: "featureName",
        accessorFn: (row) => row.permission.featureName,
        header: "Feature",
        enableEditing: false,
      },
      {
        accessorKey: "accessScope",
        header: "Access Scope",
        enableColumnFilter: false,
        Cell: ({ row }) => {
          const scopes: ScopeType[] = ["global", "own", "branch", "self"];
          const currentScope = scopes.find((scope) => row.original[scope]);
          return (
            <span>
              {currentScope
                ? currentScope.charAt(0).toUpperCase() + currentScope.slice(1)
                : "None"}
            </span>
          );
        },
        Edit: ({ row }) => {
          const id = row.original.id;
          const scopes: ScopeType[] = ["global", "own", "branch", "self"];
          const currentScope =
            scopes.find((scope) => row.original[scope]) ?? "";

          const handleChange = (e: SelectChangeEvent<ScopeType>) => {
            const newScope = e.target.value as ScopeType;

            const requestPayload: RolePermissionRequest = {
              global: false,
              own: false,
              branch: false,
              self: false,
              [newScope]: true,
            };

            updateMutation.mutate({ id, data: requestPayload });
          };

          return (
            <Select
              value={currentScope}
              onChange={handleChange}
              fullWidth
              autoFocus
              displayEmpty
              size="small"
            >
              <MenuItem value="" disabled>
                Select Scope
              </MenuItem>
              {scopes.map((scope) => (
                <MenuItem key={scope} value={scope}>
                  {scope.charAt(0).toUpperCase() + scope.slice(1)}
                </MenuItem>
              ))}
            </Select>
          );
        },
        editVariant: "select",
      },
      // Generate one boolean column for each permission flag
      ...[
        "create",
        "update",
        "read",
        "delete",
        "share",
        "report",
        "email",
        "import",
        "export",
        "print"
      ].map(
        (key): MRT_ColumnDef<RolePermissionResponse> => ({
          accessorKey: key,
          header: key.charAt(0).toUpperCase() + key.slice(1),
          grow: false,
          size: 50,
          // Display mode — just a read-only checkbox
          Cell: ({ cell }) => (
            <Checkbox checked={Boolean(cell.getValue<boolean>())} autoFocus />
          ),

          // Edit mode — user can toggle the checkbox
          Edit: ({ cell, row }) => {
            const id = row.original.id;
            const accessorKey = cell.column.id;
            const currentValue = Boolean(cell.getValue<boolean>());

            const handleKeyDown = (e: React.KeyboardEvent) => {
              if (e.key === "Enter" || e.key === " ") {
                e.preventDefault();
                handleBooleanUpdate({
                  id,
                  accessorKey,
                  newValue: !currentValue,
                });
              }
            };

            return (
              <Checkbox
                checked={currentValue}
                onChange={(e) =>
                  handleBooleanUpdate({
                    id,
                    accessorKey,
                    newValue: e.target.checked,
                  })
                }
                onKeyDown={handleKeyDown}
                autoFocus
              />
            );
          },

          // Tell MRT we’re editing with a checkbox so it doesn’t wrap it
          editVariant: "select",
          enableColumnFilter: false,
        })
      ),
      { accessorKey: "createdAt", header: "Created At", enableEditing: false },
      { accessorKey: "updatedAt", header: "Updated At", enableEditing: false },
      { accessorKey: "createdBy", header: "Created By", enableEditing: false },
      { accessorKey: "updatedBy", header: "Updated By", enableEditing: false },
    ];
  };
