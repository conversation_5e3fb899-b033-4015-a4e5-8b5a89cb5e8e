// role-permission.api.ts
import apiClient from "@/app/api/api";
import { RolePermissionPaginatedResponse, RolePermissionSuccessResponse, RolePermissionRequest, RolePermissionFilters } from "../types/role-permission.types";

export const listRolePermission = async (
    page?: number,
    limit?: number,
    filters?: RolePermissionFilters
): Promise<RolePermissionPaginatedResponse> => {
    const res = await apiClient.get<RolePermissionPaginatedResponse>("/role-permissions", {
        params: {
            ...(page !== undefined && { page }),
            ...(limit !== undefined && { limit }),
            ...filters
        },
    });

    return res.data;
};

export const getRolePermission = async (
    id: number
): Promise<RolePermissionSuccessResponse> => {
    const res = await apiClient.get<RolePermissionSuccessResponse>(`/role-permissions/${id}`);
    return res.data;
}

export const createRolePermission = async (
    data: RolePermissionRequest
): Promise<RolePermissionSuccessResponse> => {
    const res = await apiClient.post<RolePermissionSuccessResponse>("/role-permissions", data);
    return res.data;
}

export const updateRolePermission = async (
    id: number,
    data: RolePermissionRequest
): Promise<RolePermissionSuccessResponse> => {
    const res = await apiClient.patch<RolePermissionSuccessResponse>(`/role-permissions/${id}`, data);
    return res.data;
}

export const deleteRolePermission = async (
    id: number
): Promise<RolePermissionSuccessResponse> => {
    const res = await apiClient.delete<RolePermissionSuccessResponse>(`/role-permissions/${id}`);
    return res.data;
};
