// rolePermission.query.ts
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createRolePermission, deleteRolePermission, getRolePermission, listRolePermission, updateRolePermission } from "../api/role-permission.api";
import { RolePermissionFilters, RolePermissionRequest } from "../types/role-permission.types";
import toast from "react-hot-toast";

export const useListRolePermission = (page?: number, limit?: number, filters: RolePermissionFilters={}) => {
    return useQuery({
        queryKey: ["rolePermissions", page, limit, filters],
        queryFn: () => listRolePermission(page, limit, filters),
        staleTime: 1000 * 60 * 5, // Data is fresh for 5 minutes
    });
}

export const useGetRolePermission = (id: number | undefined) => {
    return useQuery({
        queryKey: ["rolePermission", id],
        queryFn: () => getRolePermission(id!),
        enabled: !!id, // Only run if id is defined
        staleTime: 1000 * 60 * 5, // Data is fresh for 5 minutes
    });
};

export const useCreateRolePermission = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationKey: ["createRolePermission"],
        mutationFn: (data: RolePermissionRequest) => createRolePermission(data),
        onSuccess: () => {
            console.log("RolePermission created successfully");
            queryClient.invalidateQueries({ queryKey: ["rolePermissions"] }); // Invalidate the list query to refresh data
        },
        onError: (error) => {
            console.error("Error creating rolePermission:", error);
        }
    });
}

export const useUpdateRolePermission = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationKey: ["updateRolePermission"],
        mutationFn: ({ id, data }: { id: number; data: RolePermissionRequest }) => 
            updateRolePermission(id, data), // Assuming update uses the same API as create
        onSuccess: () => {
            toast.success("RolePermission updated successfully");
            queryClient.invalidateQueries({ queryKey: ["rolePermissions"] }); // Invalidate the list query to refresh data
        },
        onError: (error) => {
            console.error("Error updating rolePermission:", error);
        }
    });
};

export const useDeleteRolePermission = () => {
    const queryClient = useQueryClient();
    return useMutation({
        mutationKey: ["deleteRolePermission"],
        mutationFn: (id: number) => deleteRolePermission(id), // Assuming delete uses the same API as create
        onSuccess: () => {
            console.log("RolePermission deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["rolePermissions"] }); // Invalidate the list query to refresh data
        },
        onError: (error) => {
            console.error("Error deleting rolePermission:", error);
        }
    });
};
