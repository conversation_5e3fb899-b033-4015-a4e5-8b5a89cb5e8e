"use client";

import { CommonTable } from "@/app/inventory/common/table/common-table";
import { RolePermissionResponse } from "./types/role-permission.types";
import {
  useDeleteRolePermission,
  useListRolePermission,
} from "./query/role-permission.query";
import { useRouter } from "next/navigation";
import { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { RolePermissionColumns } from "./types/role-permission.columns";

type RolePermissionProps = {
  roleId?: number;
};

// Page for rolePermission
function RolePermissionPage({ roleId }: RolePermissionProps) {
  const router = useRouter();
  const deleteMutation = useDeleteRolePermission();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<RolePermissionResponse | null>(
    null
  );

  // Open modal and set row to delete
  const handleOpenDelete = (row: RolePermissionResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  // Close modal and clear selected row
  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          // Optionally, refetch list or show success message
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          // Optionally show error notification
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<RolePermissionResponse>
        title="Associated Permissions"
        columns={RolePermissionColumns()}
        useDataQuery={useListRolePermission}
        hiddenColumns={[
          "organizationId",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/role-permission/create")}
        onExport={() => console.log("Export RolePermission")}
        onEdit={(row) =>
          router.push(`/inventory/role-permission/edit/${row.id}`)
        }
        onDelete={(row) => handleOpenDelete(row)}
        isCellAction={true}
        isFilterable={true}
        initialFilters={[
          { id: "roleId", value: roleId }, // simple equality filter
        ]}
        isAction={true}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete rolePermission item{" "}
            <strong>{selectedRow?.permission.moduleName}</strong>? This action
            cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default RolePermissionPage;
