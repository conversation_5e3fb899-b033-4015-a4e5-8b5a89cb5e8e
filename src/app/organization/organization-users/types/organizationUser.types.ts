import { BaseResponse, PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";
import { OrganizationResponse } from "../../organization/types/organization.types";
import { RoleResponse } from "../../role/types/role.types";
import { UserResponse } from "../../users/types/user.types";

export interface OrganizationUserResponse extends BaseResponse{
  userId: number;
  organizationId: number;
  uhid: string;
  uhidType: string;
  parentOrganizationUserId: number | null;
  status: string;
  roleId: number;
  role: RoleResponse;
  user: UserResponse;
  organization: OrganizationResponse;
}

export type OrganizationUserPaginatedResponse = PaginatedResponse<OrganizationUserResponse>;
export type OrganizationUserSuccessResponse = SuccessResponse<OrganizationUserResponse>;

export interface OrganizationUserFilters {
  userId?: number;
  organizationId?: number;
  roleId?: number;
}
