import apiClient from "@/app/api/api";
import { OrganizationUserFilters, OrganizationUserPaginatedResponse } from "../types/organizationUser.types";

export const getOrganizationUsers = async (
    page?: number,
    limit?: number,
    filters?: OrganizationUserFilters
): Promise<OrganizationUserPaginatedResponse> => {
    const res = await apiClient.get<OrganizationUserPaginatedResponse>("/organization-users", {
        params: {
            ...(page !== undefined && { page }),
            ...(limit !== undefined && { limit }),
            ...filters,
        }
    });
    return res.data;
} 