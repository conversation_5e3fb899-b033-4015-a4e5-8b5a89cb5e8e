import { serialize } from "@/app/common/utils/serialize.utils";
import { useQuery } from "@tanstack/react-query";
import { getOrganizationUsers } from "../api/organizationUser.api";
import {
  OrganizationUserFilters,
  OrganizationUserPaginatedResponse,
} from "../types/organizationUser.types";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListOrganizationUsers = (
  page?: number,
  limit?: number,
  filters: OrganizationUserFilters = {},
  enabled: boolean = false
) => {
  const filterKey = serialize(filters as Record<string, unknown>);
  return useQuery<OrganizationUserPaginatedResponse, ApiErrorResponse>({
    queryKey: ["organization-users", page, limit, filterKey],
    queryFn: () => getOrganizationUsers(page, limit, filters),
    enabled: enabled,
  });
};
