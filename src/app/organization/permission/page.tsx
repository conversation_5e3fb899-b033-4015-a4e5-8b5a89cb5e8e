'use client';

import { permissionColumns, PermissionResponse } from "./types/permission.types";
import { useListPermissions } from "./query/permission.query";
import { CommonTable } from "@/app/inventory/common/table/common-table";

function Permissions() {
  return (
    <CommonTable<PermissionResponse>
      title="Permissions"
      columns={permissionColumns}
      useDataQuery={useListPermissions}
      hiddenColumns={["status", "createdAt", "updatedAt", "createdBy", "updatedBy"]}
      onExport={() => console.log("Export Permissions")}
      isAction={false}
    />
  );
}

export default Permissions;
