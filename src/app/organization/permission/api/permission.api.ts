import apiClient from "@/app/api/api";
import { PermissionPaginatedResponse, PermissionResponse } from "../types/permission.types";

export const listPermissions = async (
  page?: number,
  limit?: number
): Promise<PermissionPaginatedResponse> => {
  const res = await apiClient.get<PermissionPaginatedResponse>("/permissions", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
    },
  });
  return res.data;
};

export const getPermission = async (id: number): Promise<PermissionResponse> => {
  const res = await apiClient.get<PermissionResponse>(`/permissions/${id}`);
  return res.data;
};
