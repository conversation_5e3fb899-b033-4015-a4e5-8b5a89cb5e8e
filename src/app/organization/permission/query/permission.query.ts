import { useQuery } from "@tanstack/react-query";
import { listPermissions, getPermission } from "../api/permission.api";

export const useListPermissions = (page?: number, limit?: number) => {
  return useQuery({
    queryKey: ["permissions", page, limit],
    queryFn: () => listPermissions(page, limit),
  });
};

export const useGetPermission = (id: number | undefined) => {
  return useQuery({
    queryKey: ["permissions", id],
    queryFn: () => getPermission(id!),
    enabled: !!id,
  });
};
