import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { MRT_ColumnDef } from 'material-react-table';

export interface PermissionResponse extends Record<string, unknown> {
  id: number;
  moduleName: string;
  featureName: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
}

export const permissionColumns: MRT_ColumnDef<PermissionResponse>[] = [
  { accessorKey: 'id', header: 'ID' },
  { accessorKey: 'moduleName', header: 'Module Name' },
  { accessorKey: 'featureName', header: 'Feature Name' },
  { accessorKey: 'status', header: 'Status' },
  { accessorKey: 'createdAt', header: 'Created At' },
  { accessorKey: 'updatedAt', header: 'Updated At' },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type PermissionPaginatedResponse = PaginatedResponse<PermissionResponse>
export type PermissionSuccessResponse = SuccessResponse<PermissionResponse>