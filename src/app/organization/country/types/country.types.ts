import { BaseResponse, PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";

export interface CountryResponse extends BaseResponse {
  name: string;
  currency: string;
  iso2: string;
  iso3: string;
  dialCode: string;
}

export type CountryPaginatedResponse = PaginatedResponse<CountryResponse>;
export type CountrySuccessResponse = SuccessResponse<CountryResponse>;

export interface CountryFilters{
  name?: string;
  currency?: string;
  iso2?: string;
  iso3?: string;
  dialCode?: string;
}