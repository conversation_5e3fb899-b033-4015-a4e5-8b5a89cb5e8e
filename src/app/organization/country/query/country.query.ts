import { useQuery } from "@tanstack/react-query";
import { listCountry } from "../api/country.api";
import { CountryFilters } from "../types/country.types";
import { serialize } from "@/app/common/utils/serialize.utils";

export const useListCountry = (
  page?: number,
  limit?: number,
  filters: CountryFilters = {}
) => {
  const filterKey = serialize(filters as Record<string, unknown>);
  return useQuery({
    queryKey: ["moleculars", page, limit, filterKey], // still include them for caching
    queryFn: () => listCountry(page, limit, filters),
  });
};
