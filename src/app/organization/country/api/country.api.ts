import apiClient from "@/app/api/api";
import { CountryFilters, CountryPaginatedResponse } from "../types/country.types";

export const listCountry = async (
  page?: number,
  limit?: number,
  filters?: CountryFilters,
): Promise<CountryPaginatedResponse> => {
  const res = await apiClient.get<CountryPaginatedResponse>("/countries", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...filters, // Spread filters directly
    },
  });

  return res.data;
};