import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createUser,
  deleteUser,
  getCurrentUser,
  getUser,
  listUsers,
  updateUser,
} from "../api/user.api";
import {
  UserRequest,
  UserSuccessResponse,
} from "../types/user.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListUsers = (page?: number, limit?: number) => {
  return useQuery({
    queryKey: ["users", page, limit],
    queryFn: () => listUsers(page, limit),
  });
};

export const useGetUser = (id: number | undefined) => {
  return useQuery({
    queryKey: ["users", id],
    queryFn: () => getUser(id!),
    enabled: !!id,
  });
};

export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation<
    UserSuccessResponse,
    ApiErrorResponse,
    UserRequest
  >({
    mutationFn: createUser,
    mutationKey: ["createUser"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation<
    UserSuccessResponse,
    ApiErrorResponse,
    { id: number; userRequest: UserRequest }
  >({
    mutationKey: ["updateUser"],
    mutationFn: ({
      id,
      userRequest,
    }: {
      id: number;
      userRequest: UserRequest;
    }) => updateUser(id, userRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation<UserSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteUser"],
    mutationFn: (id: number) => deleteUser(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success(data.message || "User deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error.response?.data?.message || error.message}`
      );
    },
  });
};

export const useGetCurrentUser = (enabled: boolean = false) => {
  return useQuery<UserSuccessResponse, ApiErrorResponse>({
    queryKey: ["currentUSer"],
    queryFn: () => getCurrentUser(),
    enabled: enabled,
  });
};
