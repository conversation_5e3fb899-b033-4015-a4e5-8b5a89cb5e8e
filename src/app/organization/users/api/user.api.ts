import apiClient from "@/app/api/api";
import { UserPaginatedResponse, UserResponse, UserSuccessResponse, UserRequest } from "../types/user.types";

export const listUsers = async (
  page?: number,
  limit?: number
): Promise<UserPaginatedResponse> => {
  const res = await apiClient.get<UserPaginatedResponse>("/users", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
    },
  });
  return res.data;
};

export const getUser = async (
  id: number
): Promise<UserResponse> => {
  const res = await apiClient.get<UserResponse>(`/users/${id}`);
  return res.data;
};

export const createUser = async (
  userRequest: UserRequest
): Promise<UserSuccessResponse> => {
  const res = await apiClient.post<UserSuccessResponse>("/users", userRequest);
  return res.data;
};

export const updateUser = async (
  id: number,
  userRequest: UserRequest
): Promise<UserSuccessResponse> => {
  const res = await apiClient.patch<UserSuccessResponse>(`/users/${id}`, userRequest);
  return res.data;
};

export const deleteUser = async (
  id: number
): Promise<UserSuccessResponse> => {
  const res = await apiClient.delete<UserSuccessResponse>(`/users/${id}`);
  return res.data;
};

export const getCurrentUser = async (): Promise<UserSuccessResponse> => {
  const res = await apiClient.get<UserSuccessResponse>('/user/current');
  return res.data;
}