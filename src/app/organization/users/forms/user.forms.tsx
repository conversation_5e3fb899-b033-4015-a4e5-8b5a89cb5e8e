"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uI<PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  Card
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateUser,
  useGetUser,
  useUpdateUser,
} from "../query/user.query";
import { useRef, useEffect } from "react";
import { userRequestSchema, UserRequest } from "../types/user.types";
import { useRouter } from "next/navigation";

type Props = {
  id?: number;
};

const statusOptions = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

const roleOptions = [
  { label: "Admin", value: "admin" },
  { label: "Manager", value: "manager" },
  { label: "Staff", value: "staff" },
];

export default function UserForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: userData, isLoading } = useGetUser(id);
  const createMutation = useCreateUser();
  const updateMutation = useUpdateUser();

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<UserRequest>({
    resolver: zodResolver(userRequestSchema),
    defaultValues: {
      first_name: "",
      email: "",
      phone: null,
      status: "active",
      role: "staff",
      created_by: 1, // Should come from auth context
      updated_by: 1, // Should come from auth context
    },
  });

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isEditMode && userData) {
      reset({
        first_name: userData.firstName,
        email: userData.email,
        phone: userData.phone,
        status: userData.status as "active" | "inactive",
        created_by: userData.createdBy,
        updated_by: userData.updatedBy,
      });
    }
  }, [isEditMode, userData, reset]);

  const onSubmit = (data: UserRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, userRequest: data },
        {
          onSuccess: () => {
            router.push("/organization/users");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          router.push("/organization/users");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: 600, margin: "auto", my: 4, p: 2 }}>
      <Box
        component="form"
        onSubmit={handleSubmit(onSubmit)}
        sx={{ display: "flex", flexDirection: "column", gap: 2 }}
      >
        <Typography variant="h6">
          {isEditMode ? "Edit User" : "Create User"}
        </Typography>

        <Controller
          name="first_name"
          control={control}
          render={({ field }) => (
            <TextField
              inputRef={nameRef}
              label="Name"
              size="small"
              fullWidth
              {...field}
              error={!!errors.first_name}
              helperText={errors.first_name?.message}
              autoFocus
            />
          )}
        />

        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <TextField
              label="Email"
              type="email"
              size="small"
              fullWidth
              {...field}
              error={!!errors.email}
              helperText={errors.email?.message}
            />
          )}
        />

        <Controller
          name="phone"
          control={control}
          render={({ field }) => (
            <TextField
              label="Phone"
              size="small"
              fullWidth
              {...field}
              value={field.value || ""}
              error={!!errors.phone}
              helperText={errors.phone?.message}
            />
          )}
        />

        <Controller
          name="status"
          control={control}
          render={({ field }) => (
            <TextField
              select
              label="Status"
              size="small"
              fullWidth
              {...field}
              error={!!errors.status}
              helperText={errors.status?.message}
            >
              {statusOptions.map((opt) => (
                <MenuItem key={opt.value} value={opt.value}>
                  {opt.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />

        <Controller
          name="role"
          control={control}
          render={({ field }) => (
            <TextField
              select
              label="Role"
              size="small"
              fullWidth
              {...field}
              error={!!errors.role}
              helperText={errors.role?.message}
            >
              {roleOptions.map((opt) => (
                <MenuItem key={opt.value} value={opt.value}>
                  {opt.label}
                </MenuItem>
              ))}
            </TextField>
          )}
        />

        <Box sx={{ display: "flex", gap: 2 }}>
          <Button variant="outlined" color="inherit" onClick={() => router.push("/organization/users")}>
            Cancel
          </Button>
          <Button type="submit" variant="contained">
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}