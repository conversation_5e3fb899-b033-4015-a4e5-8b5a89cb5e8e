'use client';

import { userColumns, UserResponse } from "./types/user.types";
import { useListUsers } from "./query/user.query";
import { CommonTable } from "@/app/inventory/common/table/common-table";
import { useRouter } from "next/navigation";
import { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteUser } from "./query/user.query";

function Users() {
  const router = useRouter();
  const deleteMutation = useDeleteUser();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<UserResponse | null>(null);

  const handleOpenDelete = (row: UserResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<UserResponse>
        title="Users"
        columns={userColumns}
        useDataQuery={useListUsers}
        hiddenColumns={[ "createdAt", "updatedAt", "createdBy", "updatedBy"]}
        onCreate={() => router.push("/organization/users/create")}
        onExport={() => console.log("Export Users")}
        onEdit={(row) => router.push(`/organization/users/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete user{" "}
            <strong>{selectedRow?.firstName}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default Users;