import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { TitleResponse } from "@/app/organization/title/types/title.types";
import { BaseResponse } from '@/app/common/types/common.types';

export interface UserResponse extends BaseResponse, Record<string, unknown> {
  firstName: string;
  lastName: string;
  middleName: string;
  email: string;
  dialCode: string;
  phone: string;
  alternatePhone: string;
  alternateEmail: string;
  supaBaseUserId: string;
  titleId: number;
  title: TitleResponse; // or replace with a specific type if available (e.g., `Title | null`)
  dob: string; // ISO date string (or consider `Date` if parsing)
  dobVerified: boolean;
  status: string; // could also be `"active" | "inactive"` etc., if known
  defaultOrganizationId: number;
}

export const userColumns: MRT_ColumnDef<UserResponse>[] = [
  { accessorKey: 'id', header: 'ID' },
  { accessorKey: 'firstName', header: 'First Name' },
  { accessorKey: 'middleName', header: 'Middle Name' },
  { accessorKey: 'lastName', header: 'Last Name' },
  { accessorKey: 'email', header: 'Email' },
  { accessorKey: 'phone', header: 'Phone' },
  { accessorKey: 'role.name', header: 'Role' },
  { accessorKey: 'status', header: 'Status' },
  { accessorKey: 'createdAt', header: 'Created At' },
  { accessorKey: 'updatedAt', header: 'Updated At' },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type UserPaginatedResponse = {
  data: UserResponse[];
  total: number;
  page: number;
  limit: number;
  length: number;
  totalPages: number;
};

export type UserSuccessResponse = {
  success: boolean;
  data: UserResponse;
  message: string;
};

export const userRequestSchema = z.object({
  first_name: z.string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name cannot exceed 100 characters"),
  email: z.string().email("Invalid email format"),
  phone: z.string()
    .regex(/^[0-9]{10,15}$/, "Phone must be 10-15 digits")
    .nullable()
    .optional(),
  status: z.enum(['active', 'inactive']),
  role: z.string().min(1, "Role is required"),
  created_by: z.number().int().positive("Created By must be a positive number"),
  updated_by: z.number().int().positive("Updated By must be a positive number"),
});

export type UserRequest = z.infer<typeof userRequestSchema>;