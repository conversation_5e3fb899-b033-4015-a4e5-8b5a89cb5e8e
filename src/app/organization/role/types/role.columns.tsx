'use client';

import { MRT_ColumnDef } from "material-react-table";
import { RoleResponse } from "./role.types";
import { Typography } from "@mui/material";
import { useRouter } from "next/navigation";

export const RoleColumns = (): MRT_ColumnDef<RoleResponse>[] => {
    const router = useRouter();

    return [
        { accessorKey: 'id', header: 'ID' },
        {
            accessorKey: 'name',
            header: 'Name',
            Cell: ({ cell, row }) => (
                <Typography
                    color="primary"
                    sx={{ cursor: 'pointer', textDecoration: 'underline' }}
                    onClick={() => router.push(`/stock-items/${row.original.id}`)}
                >
                    {cell.getValue<string>()}
                </Typography>
            ),
        },
        { accessorKey: 'description', header: 'Description' },
        { accessorKey: 'status', header: 'Status' },
        { accessorKey: 'organizationId', header: 'Organization' },
        { accessorKey: 'createdAt', header: 'Created At' },
        { accessorKey: 'updatedAt', header: 'Updated At' },
        { accessorKey: 'createdBy', header: 'Created By' },
        { accessorKey: 'updatedBy', header: 'Updated By' },
    ];
}