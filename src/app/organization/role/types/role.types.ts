import { BaseResponse, PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";
import { z } from "zod";

// role.types.ts
export interface RoleResponse extends BaseResponse, Record<string, unknown> {
    name: string;
    description: string | null;
    organizationId: number;
    status: 'active' | 'inactive';
}

export type RolePaginatedResponse = PaginatedResponse<RoleResponse>;
export type RoleSuccessResponse = SuccessResponse<RoleResponse>;

export const roleRequestSchema = z.object({
    name: z.string().min(1, "Name is required"),
    description: z.string().nullable(),
    status: z.enum(["active", "inactive"]),
    organizationId: z.number().int().positive("Organization ID must be a positive number"),
});

export type RoleRequest = z.infer<typeof roleRequestSchema>;