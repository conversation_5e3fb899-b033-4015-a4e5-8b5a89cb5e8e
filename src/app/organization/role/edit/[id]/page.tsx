"use client";

import { useParams } from "next/navigation";
import RoleForm from "@/app/organization/role/forms/role.forms";
import RolePermissionPage from "@/app/organization/role-permission/role-permissions";

export default function EditRolePage() {
  const params = useParams();
  const id = Number(params?.id);

  return (
    <>
      <RoleForm id={id} />
      <RolePermissionPage roleId={id} />
    </>
  );
}
