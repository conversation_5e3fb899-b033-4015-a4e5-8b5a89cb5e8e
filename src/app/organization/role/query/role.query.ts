import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createRole,
  deleteRole,
  getRole,
  listRole,
  updateRole,
} from "../api/role.api";
import { RoleRequest, RoleSuccessResponse } from "../types/role.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListRole = (
  page?: number,
  limit?: number,
  filters: Record<string, unknown> = {}
) => {
  return useQuery({
    queryKey: ["roles", page, limit, filters],
    queryFn: () => listRole(page, limit, filters),
    staleTime: 1000 * 60 * 5, // Data is fresh for 5 minutes
  });
};

export const useGetRole = (id: number | undefined) => {
  return useQuery({
    queryKey: ["roles", id],
    queryFn: () => getRole(id!),
    enabled: !!id, // Only run if id is defined
    staleTime: 1000 * 60 * 5, // Data is fresh for 5 minutes
  });
};

export const useCreateRole = () => {
  const queryClient = useQueryClient();
  return useMutation<
    RoleSuccessResponse,
    ApiErrorResponse,
    RoleRequest
  >({
    mutationKey: ["createRole"],
    mutationFn: createRole,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateRole = () => {
  const queryClient = useQueryClient();
  return useMutation<
    RoleSuccessResponse, // The wrapped response from backend
    ApiErrorResponse,
    { id: number; roleRequest: RoleRequest }
  >({
    mutationKey: ["updateRole"],
    mutationFn: ({ id, roleRequest }) => updateRole(id, roleRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteRole = () => {
  const queryClient = useQueryClient();
  return useMutation<
    RoleSuccessResponse, // Actual API response type
    ApiErrorResponse,
    number // Mutation input is just the ID
  >({
    mutationKey: ["deleteRole"],
    mutationFn: (id) => deleteRole(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      toast.success(data.message || "Role deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
