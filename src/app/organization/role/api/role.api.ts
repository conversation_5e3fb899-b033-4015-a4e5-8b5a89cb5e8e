// role.api.ts
import apiClient from "@/app/api/api";
import {
  RolePaginatedResponse,
  RoleSuccessResponse,
  RoleRequest,
  RoleResponse,
} from "../types/role.types";

export const listRole = async (
  page?: number,
  limit?: number,
  filters?: Record<string, unknown>
): Promise<RolePaginatedResponse> => {
  const res = await apiClient.get<RolePaginatedResponse>("/roles", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...filters,
    },
  });

  return res.data;
};

export const getRole = async (id: number): Promise<RoleResponse> => {
  const res = await apiClient.get<RoleResponse>(`/roles/${id}`);
  return res.data;
};

export const createRole = async (
  data: RoleRequest
): Promise<RoleSuccessResponse> => {
  const res = await apiClient.post<RoleSuccessResponse>("/roles", data);
  return res.data;
};

export const updateRole = async (
  id: number,
  data: RoleRequest
): Promise<RoleSuccessResponse> => {
  const res = await apiClient.patch<RoleSuccessResponse>(`/roles/${id}`, data);
  return res.data;
};

export const deleteRole = async (id: number): Promise<RoleSuccessResponse> => {
  const res = await apiClient.delete<RoleSuccessResponse>(`/roles/${id}`);
  return res.data;
};
