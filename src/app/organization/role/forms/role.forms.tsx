"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uItem,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useCreateRole, useGetRole, useUpdateRole } from "../query/role.query";
import { useRef, useEffect } from "react";
import {
  roleRequestSchema,
  RoleRequest,
  RoleSuccessResponse,
} from "../types/role.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import {
  OrganizationPaginatedResponse,
  OrganizationResponse,
} from "../../organization/types/organization.types";
import { useListOrganization } from "../../organization/query/organization.query";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function RoleForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: roleData, isLoading } = useGetRole(id);
  const createMutation = useCreateRole();
  const updateMutation = useUpdateRole();

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    // watch,
    formState: { errors },
  } = useForm<RoleRequest>({
    resolver: zodResolver(roleRequestSchema),
    defaultValues: {
      name: "",
      description: "",
      status: "active",
      organizationId: 0,
    },
  });

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isEditMode && roleData) {
      const role = roleData; // Type assertion to bypass TypeScript error
      reset({
        name: role.name,
        description: role.description || "",
        status:
          role.status === "active" || role.status === "inactive"
            ? role.status
            : "active",
        organizationId: role.organizationId,
      });
    }
  }, [isEditMode, roleData, reset]);

  const onSubmit = (data: RoleRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, roleRequest: data },
        {
          onSuccess: () => {
            // After successful edit, navigate back to the list page
            router.push("/organization/role");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: (data: RoleSuccessResponse) => {
          // After successful create, navigate back to the list page
          router.push(`/organization/role/edit/${data.data?.id}`);
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <>
      <Card sx={{ maxWidth: "100%", margin: "auto", mb: 4, p: 2 }}>
        <Box
          component="form"
          onSubmit={handleSubmit(onSubmit)}
          sx={{ display: "flex", flexDirection: "column", gap: 3 }}
        >
          <Typography variant="h6">
            {isEditMode ? "Edit Role" : "Create Role"}
          </Typography>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <TextField
                    inputRef={nameRef}
                    label="Name"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.name}
                    helperText={errors.name?.message}
                    autoFocus
                  />
                )}
              />
            </Grid>

            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    label="Description"
                    size="small"
                    multiline
                    fullWidth
                    {...field}
                    value={field.value || ""}
                    error={!!errors.description}
                    helperText={errors.description?.message}
                  />
                )}
              />
            </Grid>

            {isEditMode && (
              <Grid size={{ xs: 12, md: 6, lg: 4 }}>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      select
                      label="Status"
                      size="small"
                      fullWidth
                      {...field}
                      error={!!errors.status}
                      helperText={errors.status?.message}
                    >
                      {statuses.map((opt) => (
                        <MenuItem key={opt.value} value={opt.value}>
                          {opt.label}
                        </MenuItem>
                      ))}
                    </TextField>
                  )}
                />
              </Grid>
            )}

            {!isEditMode && (
              <Grid size={{ xs: 12, md: 6, lg: 4 }}>
                <Controller
                  name="organizationId"
                  control={control}
                  render={({ field }) => (
                    <CommonDropdown<
                      OrganizationResponse,
                      OrganizationPaginatedResponse,
                      number
                    >
                      label="Organization"
                      value={field.value}
                      onChange={field.onChange}
                      useDataQuery={useListOrganization}
                      labelKey="name"
                      valueKey="id"
                      searchable
                      searchKey="name"
                      error={!!errors.organizationId}
                      helperText={errors.organizationId?.message}
                    />
                  )}
                />
              </Grid>
            )}
          </Grid>

          <Box sx={{ display: "flex", gap: 2, mt: 2 }}>
            <Button
              variant="outlined"
              color="inherit"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button type="submit" variant="contained">
              {isEditMode ? "Update" : "Create"}
            </Button>
          </Box>
        </Box>
      </Card>
    </>
  );
}
