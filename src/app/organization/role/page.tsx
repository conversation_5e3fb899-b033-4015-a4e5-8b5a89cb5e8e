"use client";

import { CommonTable } from "@/app/inventory/common/table/common-table";
import { RoleResponse } from "./types/role.types";
import { useDeleteRole, useListRole } from "./query/role.query";
import { useRouter } from "next/navigation";
import { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { RoleColumns } from "./types/role.columns";

// Page for role
function RolePage() {
  const router = useRouter();
  const deleteMutation = useDeleteRole();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<RoleResponse | null>(null);

  // Open modal and set row to delete
  const handleOpenDelete = (row: RoleResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  // Close modal and clear selected row
  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          // Optionally, refetch list or show success message
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          // Optionally show error notification
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<RoleResponse>
        title="Roles"
        columns={RoleColumns()}
        useDataQuery={useListRole}
        hiddenColumns={[
          "organizationId",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/organization/role/create")}
        onExport={() => console.log("Export Role")}
        onEdit={(row) => router.push(`/organization/role/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete role item{" "}
            <strong>{selectedRow?.name}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default RolePage;
