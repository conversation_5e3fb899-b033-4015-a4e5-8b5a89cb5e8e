"use client";

import React, { useMemo, useState } from "react";
import {
  CircularProgress,
  FormControl,
  FormHelperText,
  IconButton,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
} from "@mui/material";
import { Clear } from "@mui/icons-material";
import { PaginatedResponse } from "@/app/common/types/common.types";

interface CommonDropdownProps<
  TData extends object,
  TPaginated extends PaginatedResponse<TData>,
  TValue extends string | number = string
> {
  label?: string;
  value?: TValue;
  onChange: (value: TValue | "") => void;
  useDataQuery: (
    page?: number,
    limit?: number,
    filters?: object
  ) => {
    data?: TPaginated;
    isLoading: boolean;
    isError: boolean;
  };
  labelKey: keyof TPaginated["data"][number];
  valueKey: keyof TPaginated["data"][number];
  filters?: object;
  pageSize?: number;
  error?: boolean;
  helperText?: string;
  searchable?: boolean;
  searchKey?: string;
  size?: "small" | "medium";
  inputRef?: React.RefObject<HTMLInputElement | null> | null;
  selectRef?: ((instance: HTMLDivElement | null) => void) | null;
}

export function CommonDropdown<
  TData extends object,
  TPaginated extends PaginatedResponse<TData>,
  TValue extends string | number = string
>({
  label = "Select",
  value,
  onChange,
  useDataQuery,
  labelKey,
  valueKey,
  filters = {},
  pageSize = 50,
  error,
  helperText,
  searchable = false,
  searchKey,
  inputRef,
  selectRef,
  size = "small",
}: CommonDropdownProps<TData, TPaginated, TValue>) {
  const [pageIndex] = useState(1);
  const [search, setSearch] = useState("");

  const mergedFilters = useMemo(() => {
    if (searchable && searchKey) {
      return { ...filters, [searchKey]: search };
    }
    return filters;
  }, [filters, search, searchable, searchKey]);

  const { data, isLoading, isError } = useDataQuery(
    pageIndex,
    pageSize,
    mergedFilters
  );

  const selectedItem =
    data?.data?.find((item) => String(item[valueKey]) === String(value)) ?? null;

  const items = useMemo(() => {
    if (selectedItem) {
      const alreadyIncluded = data?.data?.some(
        (item) => item[valueKey] === selectedItem[valueKey]
      );
      if (!alreadyIncluded) {
        return [selectedItem, ...(data?.data ?? [])];
      }
    }
    return data?.data ?? [];
  }, [data?.data, selectedItem, valueKey]);

  const handleClearSearch = () => {
    setSearch("");
  };

  return (
    <FormControl size={size} variant="outlined" fullWidth error={error}>
      <InputLabel id="common-dropdown-label">{label}</InputLabel>
      <Select
        labelId="common-dropdown-label"
        id="common-dropdown-select"
        label={label}
        value={value !== undefined && value !== null ? String(value) : ""}
        onChange={(e: SelectChangeEvent<string>) => {
          const selected = e.target.value;
          const exampleItem = items[0];
          const isNumber = typeof exampleItem?.[valueKey] === "number";
          const finalValue = (isNumber ? Number(selected) : selected) as TValue;
          onChange(finalValue);
        }}
        disabled={isLoading || isError}
        renderValue={(selected) => {
          if (!selected) return " ";
          const selectedItem = items.find(
            (item) => String(item[valueKey]) === selected
          );
          return selectedItem ? String(selectedItem[labelKey]) : selected;
        }}
        MenuProps={{
          PaperProps: { style: { maxHeight: 300 } },
          onKeyDown: (e) => e.stopPropagation(),
        }}
        inputRef={selectRef ? (node) => selectRef(node) : undefined}
      >
        {searchable && (
          <MenuItem disableRipple divider disableGutters>
            <TextField
              sx={{ px: 1, width: "100%" }}
              fullWidth
              size="small"
              placeholder={`Search ${label.toLowerCase()}...`}
              value={search}
              onClick={(e) => e.stopPropagation()}
              inputRef={inputRef ?? undefined}
              onChange={(e) => {
                e.stopPropagation();
                setSearch(e.target.value);
              }}
              onKeyDown={(e) => e.stopPropagation()}
              InputProps={{
                endAdornment: search && (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleClearSearch();
                      }}
                    >
                      <Clear fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              autoFocus
            />
          </MenuItem>
        )}

        {isLoading && (
          <MenuItem disabled>
            <CircularProgress size={20} />
          </MenuItem>
        )}

        {!isLoading &&
          items.map((item) => (
            <MenuItem key={String(item[valueKey])} value={String(item[valueKey])}>
              {String(item[labelKey])}
            </MenuItem>
          ))}

        {!isLoading && items.length === 0 && (
          <MenuItem disabled>No options available</MenuItem>
        )}

        {isError && <MenuItem disabled>Error loading data</MenuItem>}
      </Select>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
}
