"use client";

import React, { useState, useMemo, useEffect, useRef } from "react";
import { AutoComplete, Input, Spin } from "antd";
import { CloseCircleOutlined } from "@ant-design/icons";
import { debounce } from "lodash";

// ✅ Helper to get nested object values like "uom.name"
function getNestedValue<T>(obj: T, path: string): unknown {
  return path.split('.').reduce<unknown>((acc, key) => {
    if (acc && typeof acc === 'object' && key in acc) {
      return (acc as Record<string, unknown>)[key];
    }
    return undefined;
  }, obj);
}


interface CommonAutoCompleteProps<
  TData extends object,
  TValue extends string | number = string
> {
  label?: string;
  value?: TValue;
  searchKey?: string;
  onChange: (value: TValue | "") => void;
  useDataQuery: (
    page?: number,
    limit?: number,
    filters?: object
  ) => {
    data?: { data: TData[] };
    isLoading: boolean;
    isError: boolean;
  };
  labelKey: string; // changed from keyof TData to string for nested access
  valueKey: string;
  filters?: object;
  pageSize?: number;
  placeholder?: string;
  freeSolo?: boolean;
  tabIndex?: number;
}

export function CommonAutoComplete<
  TData extends object,
  TValue extends string | number = string
>({
  label,
  value,
  onChange,
  useDataQuery,
  labelKey,
  valueKey,
  searchKey = "name",
  filters = {},
  pageSize = 50,
  placeholder,
  freeSolo = true,
  tabIndex
}: CommonAutoCompleteProps<TData, TValue>) {
  const [inputValue, setInputValue] = useState("");
  const [searchText, setSearchText] = useState("");
  const isTyping = useRef(false);

  // Debounce input
  useEffect(() => {
    const handler = debounce(() => {
      setSearchText(inputValue);
    }, 300);
    handler();
    return () => handler.cancel();
  }, [inputValue]);

  const mergedFilters = useMemo(() => {
    return searchText.trim().length > 0
      ? { ...filters, [searchKey]: searchText }
      : filters;
  }, [filters, searchText, searchKey]);

  const { data, isLoading, isError } = useDataQuery(1, pageSize, mergedFilters);

  const options = useMemo(
    () =>
      data?.data.map((item) => ({
        value: String(getNestedValue(item, valueKey)),
        label: String(getNestedValue(item, labelKey)),
      })) ?? [],
    [data, valueKey, labelKey]
  );

  useEffect(() => {
    if (isTyping.current) return;

    if (value === "" || value === undefined || value === null) {
      setInputValue("");
    } else if (freeSolo && typeof value === "string") {
      setInputValue(value);
    } else {
      const selected = data?.data.find(
        (item) => String(getNestedValue(item, valueKey)) === String(value)
      );
      if (selected) {
        setInputValue(String(getNestedValue(selected, labelKey)));
      }
    }
  }, [value, data, labelKey, valueKey, freeSolo]);

  const onSelect = (selectedValue: string) => {
    isTyping.current = false;
    const found = data?.data.find(
      (item) => String(getNestedValue(item, valueKey)) === selectedValue
    );
    if (found) {
      onChange(getNestedValue(found, valueKey) as TValue);
      setInputValue(String(getNestedValue(found, labelKey)));
    } else if (freeSolo) {
      onChange(selectedValue as unknown as TValue);
      setInputValue(selectedValue);
    } else {
      onChange("" as unknown as TValue);
      setInputValue("");
    }
  };

  const onSearch = (text: string) => {
    isTyping.current = true;
    setInputValue(text);
    if (freeSolo) {
      onChange(text as unknown as TValue);
    }
  };

  const handleClear = () => {
    isTyping.current = false;
    setInputValue("");
    setSearchText("");
    onChange("" as unknown as TValue);
  };

  return (
    <div className="w-full max-w-xs">
      {label && <label className="block mb-1 text-sm font-medium">{label}</label>}

      <AutoComplete
        options={options}
        value={inputValue}
        onSelect={onSelect}
        onSearch={onSearch}
        allowClear={false}
        notFoundContent={
          isLoading ? (
            <Spin size="small" />
          ) : isError ? (
            "Error loading options"
          ) : (
            "No options"
          )
        }
        style={{ width: "100%" }}
        popupMatchSelectWidth={false}
        filterOption={false}
      >
        <Input
          tabIndex={tabIndex}
          placeholder={placeholder}
          size="middle"
          suffix={
            <CloseCircleOutlined
              onClick={handleClear}
              style={{
                cursor: inputValue ? "pointer" : "default",
                opacity: inputValue ? 1 : 0,
                pointerEvents: inputValue ? "auto" : "none",
                transition: "opacity 0.2s",
              }}
              aria-hidden={!inputValue}
            />
          }
        />
      </AutoComplete>
    </div>
  );
}
