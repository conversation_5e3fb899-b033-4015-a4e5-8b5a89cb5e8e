import dayjs from "dayjs";

export const serialize = (obj: Record<string, unknown>) =>
  Object.entries(obj)
    .sort()
    .map(([k, v]) => `${k}=${v}`)
    .join('&');

export function serializeFilters(filters?: Record<string, unknown>): Record<string, unknown> {
  if (!filters) return {};

  const result: Record<string, unknown> = {};

  Object.entries(filters).forEach(([key, value]) => {
    if (key === "createdAt") {
      if (Array.isArray(value) && value.length === 2) {
        result["createdAtFrom"] = value[0] ? new Date(value[0]).toISOString() : "";
        result["createdAtTo"] = value[1] ? new Date(value[1]).toISOString() : "";
      }
    }
    else if (key === "updatedAt") {
      if (Array.isArray(value) && value.length === 2) {
        result["updatedAtFrom"] = value[0] ? new Date(value[0]).toISOString() : "";
        result["updatedAtTo"] = value[1] ? new Date(value[1]).toISOString() : "";
      } 
    } else if (value !== undefined && value !== null && value !== "") {
      result[key] = value;
    }
  });

  return result;
}

export function localTime(value: string): string {
  return value ? dayjs(value).format("YYYY-MM-DD HH:mm A") : "";
}