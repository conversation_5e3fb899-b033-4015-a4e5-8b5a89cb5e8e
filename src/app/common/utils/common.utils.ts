export const formatToTZ = (dateString: string): string => {
  const date = new Date(`${dateString}T00:00:00`);
  const offset = -date.getTimezoneOffset(); // In minutes
  const sign = offset >= 0 ? '+' : '-';
  const absOffset = Math.abs(offset);
  const hours = String(Math.floor(absOffset / 60)).padStart(2, '0');
  const minutes = String(absOffset % 60).padStart(2, '0');
  return `${dateString}T00:00:00${sign}${hours}:${minutes}`;
}
