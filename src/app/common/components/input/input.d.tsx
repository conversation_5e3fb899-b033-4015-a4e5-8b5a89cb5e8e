import {
    Input,
    InputNumber,
    Select,
    DatePicker,
    Checkbox,
    Form,
} from "antd";
import { Controller, ControllerRenderProps, type Control, type FieldErrors, type FieldValues } from "react-hook-form";
import { JSX } from "react";
import React from "react";
import { FormFieldConfig } from "./input.d.types";

interface Props<T extends FieldValues> {
    field: FormFieldConfig<T>;
    control: Control<T>;
    errors: FieldErrors<T>;
}

export function FormFieldRenderer<T extends FieldValues>({
    field,
    control,
    errors,
}: Props<T>) {
    const {
        name,
        label,
        kind,
        placeholder,
        rules,
        options,
        inputProps,
        renderCustom,
    } = field;

    const error = errors?.[name];
    const validateStatus = error ? "error" : "";
    const help = error?.message?.toString();

    const getInputNode = (
        fieldCtrl: Record<string, unknown> // Let Controller inject correct props
    ): JSX.Element => {
        switch (kind) {
            case "number":
                return <InputNumber {...fieldCtrl} {...inputProps} />;
            case "password":
                return <Input.Password {...fieldCtrl} {...inputProps} />;
            case "textarea":
                return (
                    <Input.TextArea
                        rows={3}
                        placeholder={placeholder}
                        {...fieldCtrl}
                        {...inputProps}
                    />
                );
            case "select":
                return (
                    <Select
                        options={options}
                        placeholder={placeholder}
                        {...fieldCtrl}
                        {...inputProps}
                    />
                );
            case "date":
                return <DatePicker {...fieldCtrl} {...inputProps} />;
            case "checkbox":
                const { value, onChange, ...restField } = fieldCtrl;
                return (
                    <Checkbox
                        checked={!!value}
                        onChange={(e) => (typeof onChange === "function" ? onChange(e.target.checked) : undefined)}
                        {...restField}
                        {...inputProps}
                    >
                        {label}
                    </Checkbox>
                );
            case "custom":
                const customNode = renderCustom?.({ field: fieldCtrl as ControllerRenderProps });

                return React.isValidElement(customNode)
                    ? customNode
                    : <Input placeholder={placeholder} {...fieldCtrl} {...inputProps} />;
                  
            default:
                return <Input placeholder={placeholder} {...fieldCtrl} {...inputProps} />;
        }
    };

    return (
        <Form.Item label={label} validateStatus={validateStatus} help={help}>
            <Controller
                name={name}
                control={control}
                rules={rules}
                render={({ field: fieldCtrl }) => getInputNode(fieldCtrl)}
            />
        </Form.Item>
    );
}
  