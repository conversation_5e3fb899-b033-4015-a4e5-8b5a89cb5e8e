export type FieldKind =
  | "text"
  | "number"
  | "password"
  | "textarea"
  | "select"
  | "date"
  | "checkbox"
  | "read"
  | "custom";

export type FieldMode = "read" | "edit" | "both";

export interface FormFieldConfig<TForm extends FieldValues = FieldValues> {
  name: Path<TForm>;
  label: string;
  kind: FieldKind;
  placeholder?: string;
  mode?: FieldMode;
  options?: { label: string; value: string | number }[];
  rules?: RegisterOptions<TForm, Path<TForm>>;
  inputProps?: Record<string, undefined>;
  renderCustom?: ({
    field,
  }: {
    field: Parameters<Parameters<typeof Controller>[0]["render"]>[0]["field"];
  }) => React.ReactNode;
}
