"use client";

import React from "react";
import { Controller, FieldValues, Path, UseFormReturn } from "react-hook-form";
import { InputText } from "primereact/inputtext";
import {
  InputNumber,
  InputNumberValueChangeEvent,
} from "primereact/inputnumber";
import { Calendar } from "primereact/calendar";
import { Dropdown, DropdownChangeEvent } from "primereact/dropdown";
import { Checkbox, CheckboxChangeEvent } from "primereact/checkbox";
import { TableCrudColumnDef } from "../../types/table-crud.types";

type FormFieldProps<TFieldValues extends FieldValues, TRow = unknown> = {
  name: Path<TFieldValues>;
  column: TableCrudColumnDef<TRow>;
  form: UseFormReturn<TFieldValues>;
  row?: TRow; // Optional to support both single forms and table row forms
  className?: string;
};

export function FormFieldRenderer<
  TFieldValues extends FieldValues,
  TRow = unknown
>({
  name,
  column,
  form,
  row,
  className = "",
}: FormFieldProps<TFieldValues, TRow>) {
  const {
    control,
    formState: { errors },
  } = form;

  const currentRow = row ?? (form.getValues() as unknown as TRow);

  return (
    <div className={`field w-full ${className}`}>
      <label htmlFor={name} className="block mb-1 text-xs font-medium">
        {column.label ?? ""}
      </label>

      <Controller
        control={control}
        name={name}
        render={({ field }) => {
          const commonProps = {
            id: name,
            className: `w-full p-inputtext-sm ${
              errors[name] ? "p-invalid" : ""
            }`,
          };

          let inputElement: React.ReactElement;

          switch (column.type) {
            case "text":
              inputElement = <InputText {...field} {...commonProps} />;
              break;
            case "number":
              inputElement = (
                <InputNumber
                  {...commonProps}
                  value={field.value}
                  onValueChange={(e: InputNumberValueChangeEvent) =>
                    field.onChange(e.value)
                  }
                  useGrouping={false}
                />
              );
              break;
            case "date":
              inputElement = (
                <Calendar
                  {...commonProps}
                  value={field.value}
                  onChange={(e) => field.onChange(e.value)}
                  dateFormat="yy-mm-dd"
                  className={`${commonProps.className} p-inputtext-sm`}
                />
              );
              break;
            case "select":
              inputElement = (
                <Dropdown
                  {...commonProps}
                  value={field.value}
                  onChange={(e: DropdownChangeEvent) => field.onChange(e.value)}
                  options={column.options || []}
                  optionLabel="label"
                  optionValue="value"
                  className={`${commonProps.className} p-dropdown-sm`}
                />
              );
              break;
            case "checkbox":
              inputElement = (
                <Checkbox
                  inputId={name}
                  checked={field.value}
                  onChange={(e: CheckboxChangeEvent) =>
                    field.onChange(e.checked)
                  }
                />
              );
              break;
            case "custom": {
              const rendered = column.renderEdit?.(currentRow, field.onChange);
              inputElement = <>{rendered ?? <span>Unsupported</span>}</>;
              break;
            }
            default:
              inputElement = <span>Unsupported type</span>;
              break;
          }

          return inputElement;
        }}
      />

      {errors[name] && (
        <small className="p-error block mt-1 text-xs">
          {(errors[name]?.message as string) || "Invalid"}
        </small>
      )}
    </div>
  );
}
