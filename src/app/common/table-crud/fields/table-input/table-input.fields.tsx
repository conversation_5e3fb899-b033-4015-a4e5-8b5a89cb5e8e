import React from "react";
import { InputText } from "primereact/inputtext";
import { Controller, FieldValues, Path, UseFormReturn } from "react-hook-form";

type TableCrudColumnDef<T extends FieldValues> = {
  name: Path<T>;
  label?: string;
  placeholder?: string;
  form: UseFormReturn<T>;
};

export function TableInputText<T extends FieldValues>({
  name,
  label,
  placeholder,
  form,
}: TableCrudColumnDef<T>) {
  const {
    control,
    formState: { errors },
  } = form;

  return (
    <div className="field">
      {label && <label htmlFor={name}>{label}</label>}
      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <InputText
            id={name}
            size="small"
            {...field}
            placeholder={placeholder}
            className={
              errors[name] ? "p-inputtext-sm p-invalid" : "p-inputtext-sm"
            }
          />
        )}
      />
      {errors[name] && (
        <small className="p-error">
          {(errors[name]?.message as string) || "Invalid"}
        </small>
      )}
    </div>
  );
}
