import { ReactNode } from "react";

export type ColumnType =
  | "text"
  | "number"
  | "date"
  | "select"
  | "checkbox"
  | "custom";

export interface TableCrudColumnDef<T> {
  accessorKey: keyof T & string;
  header: string;
  label?: string;
  type: ColumnType;
  options?: { label: string; value: unknown }[];
  width?: string;
  renderEdit?: (row: T, onChange: (v: unknown) => void) => ReactNode;
  renderFilter?: (filterVal: unknown, onFilter: (v: unknown) => void) => ReactNode;
}
