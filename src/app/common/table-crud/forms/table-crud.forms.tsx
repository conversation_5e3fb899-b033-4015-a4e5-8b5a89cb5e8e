"use client";

import React, { useEffect, useState, useCallback } from "react";
import { Input, Select, Checkbox, DatePicker } from "antd";
import { Edit2, Save, Trash2, Plus, Filter } from "lucide-react";
import dayjs from "dayjs";
import { ZodSchema } from "zod";
import type { TableCrudColumnDef } from "../types/table-crud.types";

type Props<T extends { id?: number } & Record<string, unknown>> = {
  columns: TableCrudColumnDef<T>[];
  data: T[];
  setData: React.Dispatch<React.SetStateAction<T[]>>;
  schema: ZodSchema<unknown>;
  onCreate?: (r: T) => void;
  onUpdate?: (r: T, i: number) => void;
  onDelete?: (r: T, i: number) => void;
  onFilter?: (filters: Record<string, unknown>) => void;
};

export default function CommonCrudTable<
  T extends { id?: number } & Record<string, unknown>
>({
  columns,
  data,
  setData,
  schema,
  onCreate,
  onUpdate,
  onDelete,
  onFilter,
}: Props<T>) {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [draft, setDraft] = useState<T | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showFilter, setShowFilter] = useState(false);
  const [filters, setFilters] = useState<Record<string, unknown>>({});

  

  const addEmptyRow = useCallback(() => {
    const empty = {} as T;
    columns.forEach((c) => {
      (empty as Record<string, unknown>)[c.accessorKey] =
        c.type === "checkbox" ? false : "";
    });
    const newData = [...data, empty];
    setData(newData);
    setEditingIndex(newData.length - 1);
    setDraft(empty);
  }, [columns, data, setData, setEditingIndex, setDraft]);
  

  useEffect(() => {
    if (data.length === 0) addEmptyRow();
  }, [data.length, addEmptyRow]);

  const validate = (row: T) => {
    const res = schema.safeParse(row);
    if (res.success) return {};
    return Object.fromEntries(
      res.error.errors.map((e) => [String(e.path[0]), e.message])
    );
  };

  const handleSave = (idx: number) => {
    if (!draft) return;

    const errs = validate(draft);
    if (Object.keys(errs).length > 0) {
      return setErrors(errs);
    }
    setErrors({});

    const newData = [...data];
    newData[idx] = draft;
    setData(newData);

    if (draft.id == null) {
      onCreate?.(draft);
    } else {
      onUpdate?.(draft, idx);
    }

    setEditingIndex(null);
    setDraft(null);
    if (idx === data.length - 1) addEmptyRow();
  };

  const handleCancel = () => {
    if (editingIndex === data.length - 1 && draft?.id == null) {
      setData((prev) => prev.slice(0, -1));
    }
    setEditingIndex(null);
    setDraft(null);
    setErrors({});
  };

  const handleDelete = (idx: number) => {
    const row = data[idx];
    if (row.id != null) onDelete?.(row, idx);
    setData((prev) => prev.filter((_, i) => i !== idx));
    setEditingIndex(null);
    setDraft(null);
    setErrors({});
  };

  const handleFilterChange = (key: string, val: unknown) => {
    const newFilters = { ...filters, [key]: val };
    setFilters(newFilters);
    onFilter?.(newFilters);
  };

  const updateDraft = (key: string, val: unknown) => {
    setDraft((prev) => (prev ? { ...prev, [key]: val } : prev));
  };

  return (
    <div className="overflow-x-auto border rounded-lg text-sm shadow-sm bg-white">
      <table className="w-full table-fixed border-collapse">
        <colgroup>
          {columns.map((c, i) => (
            <col key={i} style={{ width: c.width || "auto" }} />
          ))}
          <col style={{ width: "120px" }} />
        </colgroup>

        <thead className="bg-gray-50">
          <tr className="border-b">
            {columns.map((c, i) => (
              <th
                key={i}
                className="p-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                <div className="flex items-center justify-between">
                  <span>{c.header}</span>
                  <button
                    onClick={() => setShowFilter((prev) => !prev)}
                    className="ml-2 text-gray-400 hover:text-gray-600"
                  >
                    <Filter size={14} />
                  </button>
                </div>
              </th>
            ))}
            <th className="p-3 text-right">
              <button
                onClick={addEmptyRow}
                className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none"
              >
                <Plus size={14} className="mr-1" />
                Add
              </button>
            </th>
          </tr>

          {showFilter && (
            <tr className="bg-gray-50 border-b">
              {columns.map((c, i) => (
                <th key={i} className="p-2">
                  {c.renderFilter ? (
                    c.renderFilter(filters[c.accessorKey], (v) =>
                      handleFilterChange(c.accessorKey, v)
                    )
                  ) : c.type === "select" && c.options ? (
                    <Select
                      allowClear
                      size="small"
                      className="w-full"
                      placeholder={`Filter ${c.header}`}
                      value={filters[c.accessorKey]}
                      options={c.options}
                      onChange={(v) => handleFilterChange(c.accessorKey, v)}
                    />
                  ) : c.type === "checkbox" ? (
                    <Select
                      size="small"
                      className="w-full"
                      placeholder={`Filter ${c.header}`}
                      value={
                        filters[c.accessorKey] === undefined
                          ? undefined
                          : filters[c.accessorKey]
                          ? "true"
                          : "false"
                      }
                      onChange={(v) =>
                        handleFilterChange(
                          c.accessorKey,
                          v === undefined ? undefined : v === "true"
                        )
                      }
                      options={[
                        { value: "true", label: "Yes" },
                        { value: "false", label: "No" },
                      ]}
                    />
                  ) : (
                    <Input
                      size="small"
                      placeholder={`Filter ${c.header}`}
                      value={
                        typeof filters[c.accessorKey] === "string" || typeof filters[c.accessorKey] === "number"
                          ? (filters[c.accessorKey] as string | number)
                          : ""
                      }
                      onChange={(e) =>
                        handleFilterChange(c.accessorKey, e.target.value)
                      }
                    />
                  )}
                </th>
              ))}
              <th className="p-2"></th>
            </tr>
          )}
        </thead>

        <tbody className="divide-y divide-gray-200">
          {data.map((row, idx) => {
            const isEdit = editingIndex === idx;

            return (
              <tr
                key={idx}
                className={`${
                  isEdit ? "bg-blue-50" : "bg-white"
                } hover:bg-gray-50`}
              >
                {columns.map((c) => {
                  const val =
                    isEdit && draft ? draft[c.accessorKey] : row[c.accessorKey];
                  const errMsg = isEdit ? errors[c.accessorKey] : "";

                  return (
                    <td key={c.accessorKey} className="p-3 align-middle">
                      <div className="flex flex-col">
                        {isEdit ? (
                          <>
                            {c.renderEdit ? (
                              c.renderEdit(draft!, (v) =>
                                updateDraft(c.accessorKey, v)
                              )
                            ) : c.type === "text" ? (
                              <Input
                                size="small"
                                status={errMsg ? "error" : undefined}
                                value={val as string}
                                onChange={(e) =>
                                  updateDraft(c.accessorKey, e.target.value)
                                }
                              />
                            ) : c.type === "number" ? (
                              <Input
                                size="small"
                                type="number"
                                status={errMsg ? "error" : undefined}
                                value={val as number}
                                onChange={(e) =>
                                  updateDraft(
                                    c.accessorKey,
                                    e.target.valueAsNumber
                                  )
                                }
                              />
                            ) : c.type === "date" ? (
                              <DatePicker
                                size="small"
                                status={errMsg ? "error" : undefined}
                                style={{ width: "100%" }}
                                value={val ? dayjs(val as string | number | Date) : null}
                                onChange={(_, d) =>
                                  updateDraft(c.accessorKey, d)
                                }
                              />
                            ) : c.type === "select" ? (
                              <Select
                                size="small"
                                status={errMsg ? "error" : undefined}
                                className="w-full"
                                options={c.options}
                                value={val}
                                onChange={(v) => updateDraft(c.accessorKey, v)}
                              />
                            ) : c.type === "checkbox" ? (
                              <Checkbox
                                checked={val as boolean}
                                onChange={(e) =>
                                  updateDraft(c.accessorKey, e.target.checked)
                                }
                              />
                            ) : (
                              <span>{String(val ?? "")}</span>
                            )}
                          </>
                        ) : c.type === "checkbox" ? (
                          <Checkbox checked={val as boolean} disabled />
                        ) : c.type === "date" ? (
                          val ? dayjs(val as string | number | Date).format("YYYY-MM-DD") : ""
                        ) : (
                          <span className="truncate">{String(val ?? "")}</span>
                        )}
                        {errMsg && (
                          <div className="text-red-500 text-xs mt-1">
                            {errMsg}
                          </div>
                        )}
                      </div>
                    </td>
                  );
                })}

                <td className="p-3 align-middle">
                  <div className="flex justify-end space-x-2">
                    {isEdit ? (
                      <>
                        <button
                          onClick={() => handleSave(idx)}
                          className="text-green-600 hover:text-green-800 p-1 rounded hover:bg-green-50"
                          title="Save"
                        >
                          <Save size={16} />
                        </button>
                        <button
                          onClick={handleCancel}
                          className="text-gray-600 hover:text-gray-800 p-1 rounded hover:bg-gray-50"
                          title="Cancel"
                        >
                          <Trash2 size={16} />
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          onClick={() => {
                            setEditingIndex(idx);
                            setDraft({ ...row });
                          }}
                          className="text-blue-600 hover:text-blue-800 p-1 rounded hover:bg-blue-50"
                          title="Edit"
                        >
                          <Edit2 size={16} />
                        </button>
                        <button
                          onClick={() => handleDelete(idx)}
                          className="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50"
                          title="Delete"
                        >
                          <Trash2 size={16} />
                        </button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}
