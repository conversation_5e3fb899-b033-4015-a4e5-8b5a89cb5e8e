import { AxiosError } from "axios";
import { JSX } from "react";

interface ApiError {
  error: boolean;
  message: string;
}
export interface DynamicColumn {
  accessorKey: string;
  header: string;
  isEditable?: boolean;
  Edit?: (index: number) => JSX.Element | null;
}

export interface DynamicColumn {
  accessorKey: string;
  header: string;
  isEditable?: boolean;
  Edit?: (index: number) => JSX.Element | null;
  Filter?: () => JSX.Element | null;
}

export interface PaginatedResponse<T> {
  length: number;
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  data: T[];
}

export interface SuccessResponse<T> {
  data: T | null;
  message: string;
}

export interface ErrorResponse {
  error: boolean;
  message: string;
}

export interface IdProps {
  id: number | null;
}

export interface BaseResponse {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
}

export type ApiErrorResponse = AxiosError<ApiError>;