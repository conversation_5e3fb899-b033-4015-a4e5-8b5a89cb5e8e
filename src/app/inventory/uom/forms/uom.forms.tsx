"use client";

import {
  <PERSON>,
  But<PERSON>,
  MenuItem,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateUom,
  useGetUom,
  useUpdateUom,
} from "../query/uom.query";
import { useRef, useEffect } from "react";
import {
  uomRequestSchema,
  UomRequest,
} from "@/app/inventory/uom/types/uom.types";
import { useRouter } from "next/navigation";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Draft", value: "draft" },
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function UomForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: uomData, isLoading } = useGetUom(id);
  const createMutation = useCreateUom();
  const updateMutation = useUpdateUom();

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, []);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<UomRequest>({
    resolver: zodResolver(uomRequestSchema),
    defaultValues: {
      UomName: "",
      uomHimsCode: "",
      uomNameDisplay: "",
      uomSnomedCode: "",
      uomDescription: "",
      uomStatus: "draft",
    },
  });

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isEditMode && uomData) {
      reset(uomData);
    }
  }, [isEditMode, uomData, reset]);

  const onSubmit = (data: UomRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, uomRequest: data },
        {
          onSuccess: () => {
            // After successful edit, navigate back to the list page
            router.push("/inventory/uom");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          // After successful create, navigate back to the list page
          reset();
          router.push("/inventory/uom");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", mb: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit UOM" : "Create UOM"}
      </Typography>
      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="UomName"
              control={control}
              render={({ field }) => (
                <TextField
                  inputRef={nameRef}
                  label="Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.UomName}
                  helperText={errors.UomName?.message}
                  autoFocus
                  required
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="uomNameDisplay"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Display Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.uomNameDisplay}
                  helperText={errors.uomNameDisplay?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="uomSnomedCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="SNOMED Code"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.uomSnomedCode}
                  helperText={errors.uomSnomedCode?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="uomDescription"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Description"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.uomDescription}
                  helperText={errors.uomDescription?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="uomStatus"
              control={control}
              render={({ field }) => (
                <TextField
                  select
                  label="Status"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.uomStatus}
                  helperText={errors.uomStatus?.message}
                >
                  {statuses.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
        </Grid>
        <Box mt={3} display="flex" gap={2}>
          <Button type="submit" variant="contained" color="primary" disabled={createMutation.isPending || updateMutation.isPending}>
            {isEditMode ? "Update" : "Create"}
          </Button>
          <Button variant="outlined" color="secondary" onClick={() => router.push("/inventory/uom")}>Cancel</Button>
        </Box>
      </Box>
    </Card>
  );
}
