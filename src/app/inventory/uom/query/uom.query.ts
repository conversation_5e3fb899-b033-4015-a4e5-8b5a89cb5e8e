import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createUom,
  deleteUom,
  getUom,
  listUom,
  updateUom,
} from "../api/uom.api";
import {
  UomFilters,
  UomPaginatedResponse,
  UomRequest,
  UomResponse,
  UomSuccessResponse,
} from "../types/uom.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListUom =(
  page?: number,
  limit?: number,
  filters: UomFilters = {}
) => {
  const filterKey = JSON.stringify(filters);
  return useQuery<UomPaginatedResponse, ApiErrorResponse>({
    queryKey: ["uoms", page, limit, filterKey],
    queryFn: () => listUom(page, limit, filters),
  });
};

export const useGetUom = (id: number | undefined) => {
  return useQuery<UomResponse, ApiErrorResponse>({
    queryKey: ["uom", id],
    queryFn: () => getUom(id!),
    enabled: !!id,
  });
};

export const useCreateUom = () => {
  const queryClient = useQueryClient();
  return useMutation<
    UomSuccessResponse,
    ApiErrorResponse,
    UomRequest
  >({
    mutationKey: ["createUom"],
    mutationFn: (uomRequest: UomRequest) => createUom(uomRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create UOM");
    },
  });
};

export const useUpdateUom = () => {
  const queryClient = useQueryClient();
  return useMutation<
    UomSuccessResponse,
    ApiErrorResponse,
    { id: number; uomRequest: UomRequest }
  >({
    mutationKey: ["updateUom"],
    mutationFn: ({ id, uomRequest }) => updateUom(id, uomRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update UOM");
    },
  });
};

export const useDeleteUom = () => {
  const queryClient = useQueryClient();
  return useMutation<UomSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteUom"],
    mutationFn: (id: number) => deleteUom(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["uoms"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete UOM");
    },
  });
};
