import { MRT_ColumnDef } from "material-react-table";
import {  z } from "zod";
import { localTime } from "@/app/common/utils/serialize.utils";
import { PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";

export interface UomProps {
  uomId?: number;
}

export interface UomResponse extends Record<string, unknown> {
  uomId: number;
  uomHimsCode?: string | null;
  uomName: string;
  uomNameDisplay?: string | null;
  uomSnomedCode?: string | null;
  uomDescription?: string | null;
  uomStatus: "draft" | "active" | "inactive";
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
}

const uomStatusOptions = ["draft", "active", "inactive"];
const uomStatusMap: Record<string, string> = {
  draft: "Draft",
  active: "Active",
  inactive: "Inactive",
};

export const getUomStatusLabel = (status: string): string => {
  return uomStatusMap[status] || "Unknown Status";
};

export const uomColumns: MRT_ColumnDef<UomResponse>[] = [
  { accessorKey: "uomId", header: "ID", grow: false, size: 50 },
  { accessorKey: "uomHimsCode", header: "HIMS Code" },
  { accessorKey: "uomName", header: "Name" },
  { accessorKey: "uomNameDisplay", header: "Display Name" },
  { accessorKey: "uomSnomedCode", header: "SNOMED Code" },
  { accessorKey: "uomDescription", header: "Description" },
  {
    accessorKey: "uomStatus",
    header: "Status",
    filterVariant: "select",
    filterSelectOptions: uomStatusOptions.map((value) => ({
      value,
      label: uomStatusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getUomStatusLabel(status);
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
];

export type UomPaginatedResponse = PaginatedResponse<UomResponse>;
export type UomSuccessResponse = SuccessResponse<UomResponse>;

export const uomRequestSchema = z.object({
  UomName: z.string()
    .min(1, "Name is required")
    .transform((val) => val.trim()),
  uomHimsCode: z.string().optional().nullable().transform((val) => (val != null ? val.trim() : val)),
  uomNameDisplay: z.string().optional().nullable().transform((val) => (val != null ? val.trim() : val)),
  uomSnomedCode: z.string().optional().nullable().transform((val) => (val != null ? val.trim() : val)),
  uomDescription: z.string().optional().nullable().transform((val) => (val != null ? val.trim() : val)),
  uomStatus: z.enum(["draft", "active", "inactive"]),
  createdBy: z.number().int().optional(),
  updatedBy: z.number().int().optional(),
});

export type UomRequest = z.infer<typeof uomRequestSchema>;
export interface UomFilters {
  uomHimsCode?: string;
  uomName?: string;
  createdAt?: string;
  updatedAt?: string;
}

// (Removed duplicate/conflicting interface UomResponse)
