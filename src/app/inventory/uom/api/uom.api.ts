import apiClient from "@/app/api/api";
import {
  UomPaginatedResponse,
  UomResponse,
  UomSuccessResponse,
  UomRequest,
  UomFilters,
} from "../types/uom.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listUom = async (
  page?: number,
  limit?: number,
  filters?: UomFilters
): Promise<UomPaginatedResponse> => {
  const res = await apiClient.get<UomPaginatedResponse>("/uoms", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });
  if (res.status !== 200) {
    throw new Error("Failed to fetch UOMs");
  }
  return res.data;
};

export const getUom = async (
  id: number
): Promise<UomResponse> => {
  const res = await apiClient.get<UomResponse>(`/uoms/${id}`);
  return res.data;
};

export const createUom = async (
  uomRequest: UomRequest
): Promise<UomSuccessResponse> => {
  const res = await apiClient.post<UomSuccessResponse>("/uoms", uomRequest);
  return res.data;
};

export const updateUom = async (
  id: number,
  uomRequest: UomRequest
): Promise<UomSuccessResponse> => {
  const res = await apiClient.patch<UomSuccessResponse>(`/uoms/${id}`, uomRequest);
  return res.data;
};

export const deleteUom = async (
  id: number
): Promise<UomSuccessResponse> => {
  const res = await apiClient.delete<UomSuccessResponse>(`/uoms/${id}`);
  return res.data;
};
