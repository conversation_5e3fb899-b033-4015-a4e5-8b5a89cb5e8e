import apiClient from "@/app/api/api";
import { OpeningStockItemPaginatedResponse, OpeningStockItemResponse, OpeningStockItemSuccessResponse, OpeningStockItemRequest, OpeningStockItemFilters } from "../types/opening-stock-items.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listOpeningStockItem = async (
  page?: number,
  limit?: number,
  filters?: OpeningStockItemFilters
): Promise<OpeningStockItemPaginatedResponse> => {
  const res = await apiClient.get<OpeningStockItemPaginatedResponse>("/opening-stock-items", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};

export const getOpeningStockItem = async (
  id: number
): Promise<OpeningStockItemResponse> => {
  const res = await apiClient.get<OpeningStockItemResponse>(`/opening-stock-items/${id}`);
  return res.data;
}

export const createOpeningStockItem = async (
  openingStockRequest: OpeningStockItemRequest
): Promise<OpeningStockItemSuccessResponse> => {
  const res = await apiClient.post<OpeningStockItemSuccessResponse>("/opening-stock-items", openingStockRequest);
  return res.data;
}

export const updateOpeningStockItem = async (
  id: number,
  openingStockRequest: OpeningStockItemRequest
): Promise<OpeningStockItemSuccessResponse> => {
  const res = await apiClient.patch<OpeningStockItemSuccessResponse>(`/opening-stock-items/${id}`, openingStockRequest);
  return res.data;
}

export const deleteOpeningStockItem = async (
  id: number
): Promise<OpeningStockItemSuccessResponse> => {
  const res = await apiClient.delete<OpeningStockItemSuccessResponse>(`/opening-stock-items/${id}`);
  return res.data;
};