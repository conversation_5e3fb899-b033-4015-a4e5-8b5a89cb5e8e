import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createOpeningStockItem,
  deleteOpeningStockItem,
  getOpeningStockItem,
  listOpeningStockItem,
  updateOpeningStockItem,
} from "../api/opening-stock-items.api";
import {
  OpeningStockItemFilters,
  OpeningStockItemPaginatedResponse,
  OpeningStockItemRequest,
  OpeningStockItemResponse,
  OpeningStockItemSuccessResponse,
} from "../types/opening-stock-items.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";
import { serialize } from "@/app/common/utils/serialize.utils";

export const useListOpeningStockItem = (
  page?: number,
  limit?: number,
  filters: OpeningStockItemFilters = {}
) => {
  const filterKey = serialize(filters as Record<string, unknown>);
  return useQuery<OpeningStockItemPaginatedResponse, ApiErrorResponse>({
    queryKey: ["opening-stock-items", page, limit, filterKey],
    queryFn: () => listOpeningStockItem(page, limit, filters),
  });
};

export const useGetOpeningStockItem = (id: number | undefined) => {
  return useQuery<OpeningStockItemResponse, ApiErrorResponse>({
    queryKey: ["opening-stock-items", id],
    queryFn: () => getOpeningStockItem(id!),
    enabled: !!id,
  });
};

export const useCreateOpeningStockItem = () => {
  const queryClient = useQueryClient();
  return useMutation<
    OpeningStockItemSuccessResponse,
    ApiErrorResponse,
    OpeningStockItemRequest
  >({
    mutationKey: ["createOpeningStockItem"],
    mutationFn: (openingStockRequest: OpeningStockItemRequest) =>
      createOpeningStockItem(openingStockRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["opening-stock-items"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create opening-stock-items");
    },
  });
};

export const useUpdateOpeningStockItem = () => {
  const queryClient = useQueryClient();
  return useMutation<
    OpeningStockItemSuccessResponse,
    ApiErrorResponse,
    { id: number; openingStockRequest: OpeningStockItemRequest }
  >({
    mutationKey: ["updateOpeningStockItem"],
    mutationFn: ({
      id,
      openingStockRequest,
    }: {
      id: number;
      openingStockRequest: OpeningStockItemRequest;
    }) => updateOpeningStockItem(id, openingStockRequest), // Assuming update uses the same API as create
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["opening-stock-items"] }); // Invalidate the list query to refresh data
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update opening-stock-items");
    },
  });
};

export const useDeleteOpeningStockItem = () => {
  const queryClient = useQueryClient();
  return useMutation<OpeningStockItemSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteOpeningStockItem"],
    mutationFn: (id: number) => deleteOpeningStockItem(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["opening-stock-items"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete opening-stock-items");
    },
  });
};
