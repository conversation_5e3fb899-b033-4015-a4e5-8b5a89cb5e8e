"use client";
import { CommonTable } from "../common/table/common-table";
import { useListOpeningStockItem } from "./query/opening-stock-items.query";
import { OpeningStockItemResponse } from "./types/opening-stock-items.types";
import { openingStockItemColumns } from "./types/opening-stock-items.fields";

function OpeningStockItemPage() {
  return (
    <>
      <CommonTable<OpeningStockItemResponse>
        title="Opening Stock Items"
        columns={openingStockItemColumns}
        useDataQuery={useListOpeningStockItem}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => {}}
        onExport={() => console.log("Export Molecular")}
        onEdit={() => {}}
        onDelete={() => {}}
        // isCreate={rolePermissionData?.data[0].create}
        // isEdit={rolePermissionData?.data[0].update}
        // isDelete={rolePermissionData?.data[0].delete}
      />
    </>
  );
}

export default OpeningStockItemPage;
