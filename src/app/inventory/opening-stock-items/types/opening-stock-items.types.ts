import {
  BaseResponse,
  PaginatedResponse,
  SuccessResponse,
} from "@/app/common/types/common.types";
import { z } from "zod";
import { StoreResponse } from "../../stores/types/stores.types";
import { ItemResponse } from "../../items/types/items.types";
import { ItemBatchResponse } from "../../item-batch/types/item-batch.types";
import { ItemUOMResponse } from "../../item-uom/types/item-uom.types";

export const openingStockItemSchema = z.object({
  storeId: z
    .number({ invalid_type_error: "Sub Location is required" })
    .int()
    .positive({ message: "Sub Location must be a valid store ID" }),

  itemId: z
    .number({ invalid_type_error: "Item Name is required" })
    .int()
    .positive({ message: "Item Name must be a valid item ID" }),

  itemBatchId: z
    .number({ invalid_type_error: "Batch No is required" })
    .int()
    .positive({ message: "Batch No must be a valid batch ID" }),

  expiryDate: z.string().regex(/^(0[1-9]|1[0-2])\/\d{2}$/, {
    message: "Expiry Date must be in MM/YY format",
  }),

  qty: z
    .number({ invalid_type_error: "Qty is required" })
    .positive({ message: "Qty must be greater than 0" }),

  itemUomId: z
    .number({ invalid_type_error: "UOM is required" })
    .int()
    .positive({ message: "UOM must be a valid unit ID" }),

  ucp: z
    .number({ invalid_type_error: "UCP(SU) is required" })
    .nonnegative({ message: "UCP must be zero or greater" }),

  purchasePrice: z
    .number({ invalid_type_error: "Purchase Price(SU) is required" })
    .nonnegative({ message: "Purchase Price must be zero or greater" }),

  mrp: z
    .number({ invalid_type_error: "MRP(SU) is required" })
    .nonnegative({ message: "MRP must be zero or greater" }),
});

export type OpeningStockItemRequest = z.infer<typeof openingStockItemSchema>;
export interface OpeningStockItemResponse extends BaseResponse, OpeningStockItemRequest, Record<string, unknown>{
  store?: StoreResponse;
  item?: ItemResponse;
  itemBatch?: ItemBatchResponse;
  itemUom?: ItemUOMResponse;
}
export type OpeningStockItemPaginatedResponse =
  PaginatedResponse<OpeningStockItemResponse>;
export type OpeningStockItemSuccessResponse =
  SuccessResponse<OpeningStockItemPaginatedResponse>;

export interface OpeningStockItemFilters {
  id?: number;
}
