"use client";

import { useState, useEffect } from "react";
import { OpeningStockItemColumns } from "../types/opening-stock-items.fields";
import { OpeningStockItemResponse } from "../types/opening-stock-items.types";
import { Trash } from "lucide-react";
import { Box } from "@mui/material";

function getValueByAccessor<T>(row: T, accessor: string): unknown {
  return accessor.split('.').reduce<unknown>((acc, key) => {
    if (acc && typeof acc === 'object' && key in acc) {
      return (acc as Record<string, unknown>)[key];
    }
    return undefined;
  }, row);
}


function OpeningStockItemsForm() {
  const [rows, setRows] = useState<OpeningStockItemResponse[]>([]);

  useEffect(() => {
    const openingStockItems: OpeningStockItemResponse = {
      id: 0,
      storeId: 0,
      itemId: 0,
      itemBatchId: 0,
      itemUomId: 0,
      expiryDate: new Date().toISOString(),
      qty: 0,
      purchasePrice: 0,
      mrp: 0,
      ucp: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 0,
      updatedBy: 0,
    };
    setRows([openingStockItems]);
  }, []);

  return (
    <>
      <Box
        sx={{
          backgroundColor: "#f9f9f9",
          borderRadius: 2,
          boxShadow: 2,
          maxWidth: "100%",
          margin: "auto",
          my: 2,
        }}
      >
        <table className="w-full border text-xs">
          <thead>
            <tr className="bg-[#3A59D1] text-white">
              {OpeningStockItemColumns().map((column, index) => (
                <th key={index} className="border px-2 py-1 text-left">
                  {column.header}
                </th>
              ))}
              <th className="border px-2 py-1 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {rows.map((row, rowIndex) => (
              <tr key={rowIndex}>
                {OpeningStockItemColumns(rows, setRows).map(
                  (column, colIndex) => (
                    <td key={colIndex} className="border px-2 py-1">
                      {column.Edit
                        ? column.Edit(rowIndex)
                        : String(
                            getValueByAccessor(row, column.accessorKey) ?? ""
                          )}
                    </td>
                  )
                )}
                <td className="border px-2 py-1">
                  {rows.length > 1 && rowIndex !== rows.length - 1 && (
                    <button
                      onClick={() => {
                        setRows((prev) =>
                          prev.filter((_, i) => i !== rowIndex)
                        );
                      }}
                      className="text-red-500 hover:text-red-700 p-1"
                      title="Delete"
                    >
                      <Trash size={16} />
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </Box>
    </>
  );
}
export default OpeningStockItemsForm;
