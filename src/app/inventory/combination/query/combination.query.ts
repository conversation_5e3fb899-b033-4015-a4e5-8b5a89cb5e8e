import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createCombination,
  deleteCombination,
  getCombination,
  listCombination,
  updateCombination,
} from "../api/combination.api";
import {
  CombinationPaginatedResponse,
  CombinationRequest,
  CombinationResponse,
  CombinationFilters,
  CombinationSuccessResponse,
} from "../types/combination.types";
import { toast } from "react-hot-toast";
import { AxiosError } from "axios";
import { ApiError } from "next/dist/server/api-utils";

export const useListCombination = (
  page?: number,
  limit?: number,
  filters: CombinationFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery<CombinationPaginatedResponse, AxiosError<ApiError>>({
    queryKey: ["combinations", page, limit, filterKey],
    queryFn: () => listCombination(page, limit, filters),
  });
};

export const useGetCombination = (id: number | undefined | null) => {
  return useQuery<CombinationResponse, AxiosError<ApiError>>({
    queryKey: ["combinations", id],
    queryFn: () => getCombination(id!),
    enabled: !!id,
  });
};

export const useCreateCombination = () => {
  const queryClient = useQueryClient();

  return useMutation<
    CombinationSuccessResponse,
    AxiosError<ApiError>,
    CombinationRequest
  >({
    mutationKey: ["createCombination"],
    mutationFn: (combinationRequest) => createCombination(combinationRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["combinations"] });
      toast.success(response.message || "Combination created successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create combination");
    },
  });
};

export const useUpdateCombination = () => {
  const queryClient = useQueryClient();

  return useMutation<
    CombinationSuccessResponse,
    AxiosError<ApiError>,
    { id: number; combinationRequest: CombinationRequest }
  >({
    mutationKey: ["updateCombination"],
    mutationFn: ({ id, combinationRequest }) =>
      updateCombination(id, combinationRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["combinations"] });
      toast.success(response.message || "Combination updated successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update combination");
    },
  });
};

export const useDeleteCombination = () => {
  const queryClient = useQueryClient();

  return useMutation<
    CombinationSuccessResponse,
    AxiosError<ApiError>,
    number
  >({
    mutationKey: ["deleteCombination"],
    mutationFn: (id) => deleteCombination(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["combinations"] });
      toast.success(response.message || "Combination deleted successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete combination");
    },
  });
};
