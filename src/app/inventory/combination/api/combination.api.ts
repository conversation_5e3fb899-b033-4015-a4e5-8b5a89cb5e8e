import apiClient from "@/app/api/api";
import { 
  CombinationPaginatedResponse, 
  CombinationResponse, 
  CombinationSuccessResponse, 
  CombinationRequest,
  CombinationFilters 
} from "../types/combination.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listCombination = async (
  page?: number,
  limit?: number,
  filters?: CombinationFilters
): Promise<CombinationPaginatedResponse> => {
  const res = await apiClient.get<CombinationPaginatedResponse>("/combinations", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>)
    },
  });
  return res.data;
};

export const getCombination = async (
  id: number
): Promise<CombinationResponse> => {
  const res = await apiClient.get<CombinationResponse>(`/combinations/${id}`);
  return res.data;
}

export const createCombination = async (
  combinationRequest: CombinationRequest
): Promise<CombinationSuccessResponse> => {
  const res = await apiClient.post<CombinationSuccessResponse>("/combinations", combinationRequest);
  return res.data;
}

export const updateCombination = async (
  id: number,
  combinationRequest: CombinationRequest
): Promise<CombinationSuccessResponse> => {
  const res = await apiClient.patch<CombinationSuccessResponse>(`/combinations/${id}`, combinationRequest);
  return res.data;
}

export const deleteCombination = async (
  id: number
): Promise<CombinationSuccessResponse> => {
  const res = await apiClient.delete<CombinationSuccessResponse>(`/combinations/${id}`);
  return res.data;
};