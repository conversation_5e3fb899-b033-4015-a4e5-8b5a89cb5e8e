"use client";

import {
  combinationColumns,
  CombinationResponse,
} from "./types/combination.types";
import { useListCombination } from "./query/combination.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteCombination } from "./query/combination.query";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import CombinationsForm from "./forms/combinations.forms";

function Combination() {
  const router = useRouter();
  const deleteMutation = useDeleteCombination();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<CombinationResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "Combination",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  function Page() {
    return <CombinationsForm />;
  }
  
  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);

  const handleOpenDelete = (row: CombinationResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
    Page();
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<CombinationResponse>
        title="Combinations"
        columns={combinationColumns}
        useDataQuery={useListCombination}
        hiddenColumns={["organizationId", "createdAt", "updatedAt"]}
        onCreate={() => router.push("/inventory/combination/create")}
        onExport={() => console.log("Export Combination")}
        onEdit={(row) => router.push(`/inventory/combination/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete combination item{" "}
            <strong>{selectedRow?.name}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default Combination;