"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  MenuItem,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateCombination,
  useGetCombination,
  useUpdateCombination,
} from "../query/combination.query";
import { useEffect } from "react";
import {
  combinationRequestSchema,
  CombinationRequest,
} from "@/app/inventory/combination/types/combination.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import {
  OrganizationPaginatedResponse,
  OrganizationResponse,
} from "@/app/organization/organization/types/organization.types";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function CombinationForm({ id }: Props) {
  const isEditMode = !!id;
  const router = useRouter();
  const { data: combinationData, isLoading } = useGetCombination(id ?? null);
  const createMutation = useCreateCombination();
  const updateMutation = useUpdateCombination();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CombinationRequest>({
    resolver: zodResolver(combinationRequestSchema),
    defaultValues: {
      name: "",
      description: "",
      status: "active",
      organizationId: undefined,
    },
  });

  // Reset form with fetched data if editing
  useEffect(() => {
    if (isEditMode && combinationData) {
      const formattedData = {
        ...combinationData,
        organizationId:
          combinationData.organizationId === null
            ? undefined
            : combinationData.organizationId,
      };
      reset(formattedData);
    }
  }, [isEditMode, combinationData, reset]);

  const onSubmit = (formData: CombinationRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, combinationRequest: formData },
        {
          onSuccess: () => {
            router.push("/inventory/combination");
          },
        }
      );
    } else {
      createMutation.mutate(formData, {
        onSuccess: () => {
          reset();
          router.push("/inventory/combination");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Combination" : "Create Combination"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Name */}
          <Grid size={{ lg: 4, md: 6, xs: 12 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Name"
                  size="small"
                  fullWidth
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  required
                  autoFocus
                />
              )}
            />
          </Grid>

          {/* Status - show only in edit mode */}
          {isEditMode && (
            <Grid size={{ lg: 4, md: 6, xs: 12 }}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    {statuses.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
          )}

          {/* Organization dropdown - only show when NOT editing */}
          {!isEditMode && (
            <Grid size={{ lg: 4, md: 6, xs: 12 }}>
              <Controller
                name="organizationId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<
                    OrganizationResponse,
                    OrganizationPaginatedResponse,
                    number
                  >
                    label="Organization"
                    value={field.value === null ? undefined : field.value}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
                )}
              />
            </Grid>
          )}

          {/* Description */}
          <Grid size={{ lg: 4, md: 6, xs: 12 }}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Description"
                  size="small"
                  multiline
                  rows={4}
                  fullWidth
                  error={!!errors.description}
                  helperText={errors.description?.message}
                />
              )}
            />
          </Grid>
        </Grid>

        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button variant="outlined" color="inherit" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}
