"use client";

import {
    Dnd<PERSON>ontext,
    closest<PERSON>enter,
    PointerSensor,
    useSensor,
    useSensors,
} from "@dnd-kit/core";
import {
    SortableContext,
    useSortable,
    verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";

import {
    Controller,
    useFieldArray,
    useForm,
    SubmitHandler,
} from "react-hook-form";
import {
    Box,
    Grid,
    TextField,
    Button,
    IconButton,
    Typography,
    Paper,
} from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import DeleteIcon from "@mui/icons-material/Delete";

import {
    CombinationsRequest,
    CombinationGroupRequest,
} from "../types/combination.types";
import {
    GenericPaginatedResponse,
    GenericResponse,
} from "../../generic/types/generic.types";
import { useListGeneric } from "../../generic/query/generic.query";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import {
  useCreateCombinationGeneric,
  useGetCombinationGeneric,
  useUpdateCombinationGeneric,
} from "@/app/inventory/combination-generic/query/combination-generic.query";
//import { useCreateCombination, useUpdateCombination, useGetCombination } from "../query/combination.query";

/* ---------- helpers ---------- */

type Props = {
  id?: number;
};

const emptyGroup = (): CombinationGroupRequest => ({
  genericId: 1,
  displayOrder: 0,
});

/* ---------- sortable item ---------- */

function SortableItem({
  id,
  children,
}: {
  id: string;
  children: React.ReactNode;
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <Paper ref={setNodeRef} style={style} sx={{ p: 2, mb: 2 }}>
      <Box display="flex" alignItems="center" gap={1}>
        <span
          ref={setActivatorNodeRef}
          {...listeners}
          {...attributes}
          style={{ display: "flex" }}
        >
          <DragIndicatorIcon
            fontSize="small"
            color="action"
            sx={{ cursor: "grab" }}
          />
        </span>
        <Box flexGrow={1}>{children}</Box>
      </Box>
    </Paper>
  );
}

/* ---------- main form ---------- */

function CombinationsForm({ id }: Props) {
  const isEditMode = !!id;
  const router = useRouter();
  const { data: combinationGenericData/*, isLoading */} = useGetCombinationGeneric(id);
  const createMutation = useCreateCombinationGeneric();
  const updateMutation = useUpdateCombinationGeneric();

  const {
    control,
    reset,
    register,
    handleSubmit,
    setValue,
    getValues,
    formState: { errors },
  } = useForm<CombinationsRequest>({
    defaultValues: {
      description: "",
      combinationGroups: [emptyGroup()],
    },
  });

  const { fields, append, remove, move } = useFieldArray({
    control,
    name: "combinationGroups",
  });

  const sensors = useSensors(
    useSensor(PointerSensor, { activationConstraint: { distance: 6 } })
  );

  useEffect(() => {
    if (isEditMode && combinationGenericData) {
      reset(combinationGenericData);
    }
  }, [isEditMode, combinationGenericData, reset]);

  const onSubmit: SubmitHandler<CombinationsRequest> = (formData) => {
    const combinationGroups = formData.combinationGroups ?? [];

    if (isEditMode && id) {
        combinationGroups.forEach((group) => {
        updateMutation.mutate(
            {
            id,
            CombinationGenericRequest: {
                description: formData.description || null,
                status: "active",
                combinationId: id,
                displayOrder: group.displayOrder,
                genericId: group.genericId,
                generics: [{ genericId: group.genericId }],
                // optionally: organizationId: formData.organizationId
            },
            },
            {
            onSuccess: () => {
                router.push("/inventory/combination");
            },
            }
        );
        });
    } else {
        combinationGroups.forEach((group) => {
        createMutation.mutate(
            {
              description: formData.description || null,
              status: "active",
              combinationId: 0, // Replace with actual combinationId if you have one
              displayOrder: group.displayOrder,
              genericId: group.genericId,
              generics: [{ genericId: group.genericId }],
              // optionally: organizationId: formData.organizationId
            },
            {
            onSuccess: () => {
                reset();
                router.push("/inventory/combination");
            },
            }
        );
        });
    }
    };




  const updateDisplayOrders = () => {
    const groups = getValues("combinationGroups") || [];
    groups.forEach((_, idx) =>
      setValue(`combinationGroups.${idx}.displayOrder`, idx + 1)
    );
  };

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
      <TextField
        fullWidth
        label="Description"
        {...register("description")}
        sx={{ mb: 3 }}
      />

      <Typography variant="h6" sx={{ mb: 2 }}>
        Combination Groups (Drag to Reorder)
      </Typography>

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={({ active, over }) => {
          if (active.id !== over?.id) {
            const oldIndex = fields.findIndex((f) => f.id === active.id);
            const newIndex = fields.findIndex((f) => f.id === over?.id);
            move(oldIndex, newIndex);
            updateDisplayOrders();
          }
        }}
      >
        <SortableContext
          items={fields.map((f) => f.id)}
          strategy={verticalListSortingStrategy}
        >
          {fields.map((field, index) => (
            <SortableItem key={field.id} id={field.id}>
              <Grid container spacing={2} alignItems="center">
                <Grid size={{ xs: 12, md: 6 }}>
                  <Controller
                    name={`combinationGroups.${index}.genericId`}
                    control={control}
                    rules={{ required: "Generic is required" }}
                    render={({ field }) => (
                      <CommonDropdown<
                        GenericResponse,
                        GenericPaginatedResponse,
                        number
                      >
                        label="Generic"
                        value={field.value}
                        onChange={field.onChange}
                        useDataQuery={useListGeneric}
                        labelKey="medicineName"
                        valueKey="id"
                        searchable
                        searchKey="medicineName"
                        error={!!errors.combinationGroups?.[index]?.genericId}
                        helperText={
                          errors.combinationGroups?.[index]?.genericId?.message
                        }
                      />
                    )}
                  />
                </Grid>

                <Grid size={{ xs: 12, md: 2 }}>
                  <IconButton
                    onClick={(e) => {
                      e.stopPropagation();
                      remove(index);
                      updateDisplayOrders();
                    }}
                    disabled={fields.length === 1}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Grid>
              </Grid>
            </SortableItem>
          ))}
        </SortableContext>
      </DndContext>

      <Button
        variant="outlined"
        startIcon={<AddIcon />}
        onClick={() => {
          append(emptyGroup());
          updateDisplayOrders();
        }}
        sx={{ mt: 2 }}
      >
        Add Generic
      </Button>

      <Box mt={4}>
        <Button type="submit" variant="contained">
          Save Combination
        </Button>
      </Box>
    </Box>
  );
}

export default CombinationsForm;
