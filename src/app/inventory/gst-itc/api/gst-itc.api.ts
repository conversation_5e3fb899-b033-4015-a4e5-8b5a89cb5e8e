import apiClient from "@/app/api/api";
import { 
  GstItcEligibilityFilters,
  GstItcEligibilityPaginatedResponse, 
  GstItcEligibilityResponse, 
  GstItcEligibilitySuccessResponse, 
  GstItcEligibilityRequest 
} from "../types/gst-itc.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listGstItcEligibility = async (
  page?: number,
  limit?: number,
  filters?: GstItcEligibilityFilters,
): Promise<GstItcEligibilityPaginatedResponse> => {
  const res = await apiClient.get<GstItcEligibilityPaginatedResponse>("/gst-itc-eligibles", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });
  return res.data;
};

export const getGstItcEligibility = async (
  id: number
): Promise<GstItcEligibilityResponse> => {
  const res = await apiClient.get<GstItcEligibilityResponse>(`/gst-itc-eligibles/${id}`);
  return res.data;
}

export const createGstItcEligibility = async (
  gstItcEligibilityRequest: GstItcEligibilityRequest
): Promise<GstItcEligibilitySuccessResponse> => {
  const res = await apiClient.post<GstItcEligibilitySuccessResponse>("/gst-itc-eligibles", gstItcEligibilityRequest);
  return res.data;
}

export const updateGstItcEligibility = async (
  id: number,
  gstItcEligibilityRequest: GstItcEligibilityRequest
): Promise<GstItcEligibilitySuccessResponse> => {
  const res = await apiClient.patch<GstItcEligibilitySuccessResponse>(`/gst-itc-eligibles/${id}`, gstItcEligibilityRequest);
  return res.data;
}

export const deleteGstItcEligibility = async (
  id: number
): Promise<GstItcEligibilitySuccessResponse> => {
  const res = await apiClient.delete<GstItcEligibilitySuccessResponse>(`/gst-itc-eligibles/${id}`);
  return res.data;
};