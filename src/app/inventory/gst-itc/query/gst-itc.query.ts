import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createGstItcEligibility,
  deleteGstItcEligibility,
  getGstItcEligibility,
  listGstItcEligibility,
  updateGstItcEligibility,
} from "../api/gst-itc.api";
import {
  GstItcEligibilityFilters,
  GstItcEligibilityRequest,
  GstItcEligibilitySuccessResponse,
} from "../types/gst-itc.types";

import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListGstItcEligibility = (
  page?: number,
  limit?: number,
  filters: GstItcEligibilityFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery({
    queryKey: ["gstItcEligibilities", page, limit, filterKey],
    queryFn: () => listGstItcEligibility(page, limit, filters),
  });
};
export const useGetGstItcEligibility = (id: number | undefined) => {
  return useQuery({
    queryKey: ["gstItcEligibilitie", id],
    queryFn: () => getGstItcEligibility(id!),
    enabled: !!id,
  });
};

export const useCreateGstItcEligibility = () => {
  const queryClient = useQueryClient();
  return useMutation<
    GstItcEligibilitySuccessResponse,
    ApiErrorResponse,
    GstItcEligibilityRequest
  >({
    mutationFn: createGstItcEligibility,
    mutationKey: ["createGstItcEligibility"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["gstItcEligibilities"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateGstItcEligibility = () => {
  const queryClient = useQueryClient();
  return useMutation<
    GstItcEligibilitySuccessResponse,
    ApiErrorResponse,
    { id: number; gstItcEligibilityRequest: GstItcEligibilityRequest }
  >({
    mutationKey: ["updateGstItcEligibility"],
    mutationFn: ({ id, gstItcEligibilityRequest }) =>
      updateGstItcEligibility(id, gstItcEligibilityRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["gstItcEligibilities"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteGstItcEligibility = () => {
  const queryClient = useQueryClient();
  return useMutation<
    GstItcEligibilitySuccessResponse,
    ApiErrorResponse,
    number
  >({
    mutationKey: ["deleteGstItcEligibility"],
    mutationFn: (id) => deleteGstItcEligibility(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["gstItcEligibilities"] });
      toast.success(
        data.message || "GST ITC Eligibility deleted successfully!"
      );
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
