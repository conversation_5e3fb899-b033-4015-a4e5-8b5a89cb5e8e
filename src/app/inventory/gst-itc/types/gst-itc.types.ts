import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from '@/app/common/utils/serialize.utils';

export interface GstItcEligibilityResponse extends Record<string, unknown> {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  code: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
  organizationId: number;
}

export const gstItcEligibilityColumns: MRT_ColumnDef<GstItcEligibilityResponse>[] = [
  { accessorKey: 'id', header: 'ID'  ,grow: false, size: 50},
  { accessorKey: 'code', header: 'Code' },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'description', header: 'Description' },
  { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    } 
  },
  {
        accessorKey: "createdAt",
        header: "Created At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
      {
        accessorKey: "updatedAt",
        header: "Updated At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
  { accessorKey: 'organization.name', header: 'Organization' },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type GstItcEligibilityPaginatedResponse = PaginatedResponse<GstItcEligibilityResponse>;
export type GstItcEligibilitySuccessResponse = SuccessResponse<GstItcEligibilityResponse>;

export const gstItcEligibilityRequestSchema = z.object({
  name: z.string()
    .min(1, "Name is required")
    .transform(val => val.trim()),
  description: z.string()
    .nullable()
    .optional()
    .transform(val => val?.trim() ?? val),
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number")
    .optional(),
  status: z.enum(['active', 'inactive']),
});

export type GstItcEligibilityRequest = z.infer<typeof gstItcEligibilityRequestSchema>;

export interface GstItcEligibilityFilters {
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  code?: string;
  name?: string;
  organizationId?: number;
  status?: 'active' | 'inactive';
};