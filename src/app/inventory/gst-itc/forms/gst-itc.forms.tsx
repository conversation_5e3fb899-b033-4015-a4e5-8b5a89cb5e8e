"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uI<PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  Card,
  Grid
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateGstItcEligibility,
  useGetGstItcEligibility,
  useUpdateGstItcEligibility,
} from "../query/gst-itc.query";
import { useEffect } from "react";
import {
  gstItcEligibilityRequestSchema,
  GstItcEligibilityRequest,
} from "@/app/inventory/gst-itc/types/gst-itc.types";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { useRouter } from "next/navigation";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function GstItcEligibilityForm({ id }: Props) {
  const isEditMode = !!id;
  const router = useRouter();
  const { data: gstItcEligibilityData, isLoading } = useGetGstItcEligibility(id);
  const createMutation = useCreateGstItcEligibility();
  const updateMutation = useUpdateGstItcEligibility();


  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<GstItcEligibilityRequest>({
    resolver: zodResolver(gstItcEligibilityRequestSchema),
    defaultValues: {
      name: "", 
      description: "",
      organizationId: undefined,
      status: "active",
    },
  });

   useEffect(() => {
    if (isEditMode && gstItcEligibilityData) {
      reset(gstItcEligibilityData);
    }
  }, [isEditMode, gstItcEligibilityData, reset]);

  const onSubmit = (data: GstItcEligibilityRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate({ id, gstItcEligibilityRequest: data },
        {
          onSuccess: () => {
            // After successful edit, navigate back to the list page
            router.push("/inventory/gst-itc");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          // After successful create, navigate back to the list page
          reset();
          router.push("/inventory/gst-itc");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

return (
    <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit GST ITC Eligibility" : "Create GST ITC Eligibility"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>

          {/* Name Field */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  required
                />
              )}
            />
          </Grid>

          {/* Organization Field (Create Mode Only) */}
          {!isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="organizationId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                    label="Organization"
                    value={field.value ?? undefined}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
                )}
              />
            </Grid>
          )}

          {/* Status Field (Edit Mode Only) */}
          {isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    {statuses.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
          )}
          {/* Description Field */}
           <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                label="Description"
                size="small"
                fullWidth
                multiline
                rows={4}
                {...field}
                value={field.value || ""}
                error={!!errors.description}
                helperText={errors.description?.message}
                />
              )}
              />
          </Grid>
      </Grid>
        {/* Action Buttons */}
        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
);
}