import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import {statusOptions, statusMap, getStatusLabel} from '@/app/common/types/status.types';
import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from '@/app/common/utils/serialize.utils';

export interface StoreResponse extends Record<string, unknown> {
  id: number;
  code: string;
  name: string;
  storeTypeId: number;
  isChild: boolean;
  parentLocationId: number;
  organizationId: number;
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
}

export const isChildOptions = ['true','false'] as const;
export const isChildMap = {
  true: 'Yes',
  false: 'No'
} as const;

export function getisChildLabel(isChild: string) {
  return isChildMap[isChild as keyof typeof isChildMap] || isChild;
}
export const storeColumns: MRT_ColumnDef<StoreResponse>[] = [
  { accessorKey: 'id', header: 'ID',grow: false, size: 50 },
  { accessorKey: 'code', header: 'Code' },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'storeType.name', header: 'Store Type' },

  {
    accessorKey: 'isChild',
    header: 'Is Child',
    filterVariant: 'select',
    filterSelectOptions: isChildOptions.map(value => ({
      value,
      label: isChildMap[value],
    })),
    Cell: ({ cell }) => getisChildLabel(cell.getValue<string>()),
  },
  { accessorKey: 'parentLocationId', header: 'Parent Location ID' },
  { accessorKey: 'organization.name', header: 'Organization' },
  {
    accessorKey: 'status',
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => getStatusLabel(cell.getValue<string>()),
  },
  { accessorKey: 'organization.name', header: 'Organization' },
      {
          accessorKey: "createdAt",
          header: "Created At",
          filterVariant: "date-range",
          Cell: ({ cell }) => {
            const value = cell.getValue<string>();
            return localTime(value);
          },
        },
        {
          accessorKey: "updatedAt",
          header: "Updated At",
          filterVariant: "date-range",
          Cell: ({ cell }) => {
            const value = cell.getValue<string>();
            return localTime(value);
          },
        },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type StorePaginatedResponse = PaginatedResponse<StoreResponse>
export type StoreSuccessResponse = SuccessResponse<StoreResponse>

export const storeRequestSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(100, "Name cannot exceed 100 characters").transform((val) => val.trim()),
  storeTypeId: z.number().int().positive("Store Type ID must be a positive number"),
  isChild: z.boolean(),
  parentLocationId: z.number().int().positive("Parent Location ID must be a positive number").optional(),
  organizationId: z.number().int().positive("Organization ID must be a positive number"),
  status: z.enum(["active", "inactive"]).default("active").optional(),
}).superRefine((data, ctx) => {
  if (data.isChild && !data.parentLocationId) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Parent Location ID is required when Is Child is true",
      path: ["parentLocationId"]
    });
  }
});

export type StoreRequest = z.infer<typeof storeRequestSchema>;

export interface StoreFilters {
  name?: string;
  code?: string;
  storeTypeId?: number;
  isChild?: boolean;
  parentLocationId?: number;
  organizationId?: number;
  status?: 'active' | 'inactive';
  createdAt?: string; // ISO format or date range depending on backend
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
}
