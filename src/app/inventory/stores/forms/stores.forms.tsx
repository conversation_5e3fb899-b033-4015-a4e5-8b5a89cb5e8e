"use client";

import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  MenuItem,
  TextField,
  Typography,
  Card,
  Grid
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateStore,
  useGetStore,
  useListStores,
  useUpdateStore,
} from "../query/stores.query";
import { useRef, useEffect } from "react";
import {
  storeRequestSchema,
  StoreRequest,
  StorePaginatedResponse,
  StoreResponse
} from "@/app/inventory/stores/types/stores.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { StoreTypePaginatedResponse, StoreTypeResponse } from "../../store-types/types/store-types.types";
import { useListStoreTypes } from "../../store-types/query/store-types.query";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function StoreForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: storeData, isLoading } = useGetStore(id);
  const createMutation = useCreateStore();
  const updateMutation = useUpdateStore();

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<StoreRequest>({
    resolver: zodResolver(storeRequestSchema),
    defaultValues: {
      name: "",
      storeTypeId: undefined,
      isChild: false,
      parentLocationId: undefined,
      organizationId: undefined,
      status: "active",
    },
  });

  const isChild = watch("isChild");

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isEditMode && storeData) {
      reset({
        name: storeData.name,
        storeTypeId: storeData.storeTypeId,
        isChild: storeData.isChild,
        parentLocationId: storeData.parentLocationId,
        organizationId: storeData.organizationId,
        status: storeData.status,
      });
      setTimeout(() => {
        nameRef.current?.focus();
      }, 0);
    }
  }, [isEditMode, storeData, reset]);

  const onSubmit = (data: StoreRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate({ id, storeRequest: data },
        {
          onSuccess: () => {
            // After successful edit, navigate back to the list page
            router.push("/inventory/stores");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          // After successful create, navigate back to the list page
          reset();
          router.push("/inventory/stores");
        },
      });
    }
  };
  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
  <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
    <Typography variant="h6" mb={3}>
      {isEditMode ? "Edit Store" : "Create Store"}
    </Typography>

    <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
      <Grid container spacing={2}>
        {/* Code (Edit Mode Only) */}
        {isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <TextField
              label="Code"
              size="small"
              fullWidth
              value={storeData?.code || ""}
              disabled
            />
          </Grid>
        )}

        {/* Name */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                inputRef={nameRef}
                label="Name"
                size="small"
                fullWidth
                {...field}
                error={!!errors.name}
                helperText={errors.name?.message}
                autoFocus
                required
              />
            )}
          />
        </Grid>

        {/* Store Type */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="storeTypeId"
            control={control}
            render={({ field }) => (
              <CommonDropdown<StoreTypeResponse, StoreTypePaginatedResponse, number>
                label="Store Type"
                value={field.value}
                onChange={field.onChange}
                useDataQuery={useListStoreTypes}
                labelKey="name"
                valueKey="id"
                searchable
                searchKey="name"
                error={!!errors.storeTypeId}
                helperText={errors.storeTypeId?.message}
                
              />
            )}
          />
        </Grid>

        {/* Is Child Store Checkbox */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="isChild"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Checkbox
                    checked={field.value}
                    onChange={(e) => field.onChange(e.target.checked)}
                  />
                }
                label="Is Child Store"
              />
            )}
          />
        </Grid>

        {/* Parent Location (Conditional) */}
         <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              {isChild && (
                <Controller
                  name="parentLocationId"
                  control={control}
                  render={({ field }) => (
                    <CommonDropdown<StoreResponse, StorePaginatedResponse, number>
                      label="parent location"
                      value={field.value}
                      onChange={field.onChange}
                      useDataQuery={useListStores}
                      labelKey="name"
                      valueKey="id"
                      searchable
                      searchKey="name"
                      error={!!errors.parentLocationId}
                      helperText={errors.parentLocationId?.message}
                    />
                  )}
                />
              )}
            </Grid>

        {/* Organization (Create Mode Only) */}
        {!isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="organizationId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                  label="Organization"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListOrganization}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.organizationId}
                  helperText={errors.organizationId?.message}
                />
              )}
            />
          </Grid>
        )}

        {/* Status (Edit Mode Only) */}
        {isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <TextField
                  select
                  label="Status"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.status}
                  helperText={errors.status?.message}
                >
                  {statuses.map((opt) => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
        )}
      </Grid>

      {/* Action Buttons */}
      <Box
        sx={{
          display: "flex",
          gap: 2,
          justifyContent: "flex-end",
          mt: 3,
        }}
      >
        <Button
          variant="outlined"
          color="inherit"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={createMutation.isPending || updateMutation.isPending}
        >
          {isEditMode ? "Update" : "Create"}
        </Button>
      </Box>
    </Box>
  </Card>
);
}