"use client";

import { useParams } from "next/navigation";
import StoreForm from "@/app/inventory/stores/forms/stores.forms";
import StoreInventory from "@/app/inventory/store-inventory/store-inventory";

export default function EditStorePage() {
  const params = useParams();
  const id = Number(params?.id);

  return (
      <>
      <StoreForm id={id} />
      <StoreInventory storeId={id}/>
      </>
    );
}