import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createStore,
  deleteStore,
  getStore,
  listStores,
  updateStore,
} from "../api/stores.api";
import {
  StoreFilters,
  StorePaginatedResponse,
  StoreRequest,
  StoreSuccessResponse,
} from "../types/stores.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";


export const useListStores = (
  page?: number,
  limit?: number,
  filters: StoreFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<StorePaginatedResponse, ApiErrorResponse>({
    queryKey: ["stores", page, limit, filterKey],
    queryFn: () => listStores(page, limit, filters),
  });
};

export const useGetStore = (id: number | undefined) => {
  return useQuery({
    queryKey: ["store", id],
    queryFn: () => getStore(id!),
    enabled: !!id, // only run if id is defined
  });
};

export const useCreateStore = () => {
  const queryClient = useQueryClient();

  return useMutation<
    StoreSuccessResponse,
    ApiErrorResponse,
    StoreRequest
  >({
    mutationFn: createStore,
    mutationKey: ["createStore"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["stores"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateStore = () => {
  const queryClient = useQueryClient();

  return useMutation<
    StoreSuccessResponse, // The wrapped response from backend
    ApiErrorResponse,
    { id: number; storeRequest: StoreRequest }
  >({
    mutationKey: ["updateStore"],
    mutationFn: ({ id, storeRequest }) => updateStore(id, storeRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["stores"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteStore = () => {
  const queryClient = useQueryClient();

  return useMutation<
    StoreSuccessResponse, // Actual API response type
    ApiErrorResponse,
    number // Mutation input is just the ID
  >({
    mutationKey: ["deleteStore"],
    mutationFn: (id) => deleteStore(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["stores"] });
      toast.success(data.message || "Store deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
