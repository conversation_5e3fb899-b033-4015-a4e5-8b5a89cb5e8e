import apiClient from "@/app/api/api";
import { 
  StorePaginatedResponse, 
  StoreResponse, 
  StoreSuccessResponse, 
  StoreRequest, 
  StoreFilters
} from "../types/stores.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listStores = async (
  page?: number,
  limit?: number,
  filters?: StoreFilters
): Promise<StorePaginatedResponse> => {
  const res = await apiClient.get<StorePaginatedResponse>("/stores", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string,unknown>), // spread filters directly
    },
  });
  return res.data;
};

export const getStore = async (
  id: number
): Promise<StoreResponse> => {
  const res = await apiClient.get<StoreResponse>(`/stores/${id}`);
  return res.data;
};

export const createStore = async (
  storeRequest: StoreRequest
): Promise<StoreSuccessResponse> => {
  const res = await apiClient.post<StoreSuccessResponse>("/stores", storeRequest);
  return res.data;
};

export const updateStore = async (
  id: number,
  storeRequest: StoreRequest
): Promise<StoreSuccessResponse> => {
  const res = await apiClient.patch<StoreSuccessResponse>(`/stores/${id}`, storeRequest);
  return res.data;
};

export const deleteStore = async (
  id: number
): Promise<StoreSuccessResponse> => {
  const res = await apiClient.delete<StoreSuccessResponse>(`/stores/${id}`);
  return res.data;
};