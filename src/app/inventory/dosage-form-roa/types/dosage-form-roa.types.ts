import { z } from "zod";
import { MRT_ColumnDef } from 'material-react-table';
import { BaseResponse, PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from "@/app/common/utils/serialize.utils";
import { statusOptions, statusMap, getStatusLabel } from "./dosage-form-roa.fields";

export interface DosageFormROAProps {
  dosageFormId?: number;
}

export interface DosageFormROAResponse extends BaseResponse, Record<string, unknown> {
  dosageFormId: number;
  roaId: number;
  displayOrder: number;
  organizationId: number;
  status: 'active' | 'inactive';
}

export const dosageFormROAColumns: MRT_ColumnDef<DosageFormROAResponse>[] = [
  { accessorKey: 'id', header: 'ID' },
  { accessorKey: 'roa.name', header: 'ROA ' },
  { accessorKey: 'displayOrder', header: 'Display Order' },
  { 
      accessorKey: 'status', 
      header: 'Status',
      filterVariant: 'select',
      filterSelectOptions: statusOptions.map(value => ({
        value,
        label: statusMap[value], // Capitalize first letter
      })),
      Cell: ({ cell }) => {
        const status = cell.getValue<string>();
        return getStatusLabel(status);
      } 
    },
  {
      accessorKey: "createdAt",
      header: "Created At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },  {
      accessorKey: "updatedAt",
      header: "Updated At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
    { accessorKey: 'organization.name', header: 'Organization'},
];

export type DosageFormROAPaginatedResponse = PaginatedResponse<DosageFormROAResponse>;
export type DosageFormROASuccessResponse = SuccessResponse<DosageFormROAResponse>;

export const dosageFormROARequestSchema = z.object({
  dosageFormId: z.number().int().positive("Dosage Form ID must be a positive number"),
  roaId: z.number().int().positive("ROA ID must be a positive number"),
  displayOrder: z.number().int().nonnegative("Display order must be a non-negative integer"),
  organizationId: z.number().int().positive("Organization ID must be a positive number").optional(),
  status: z.enum(['active', 'inactive']),
});

export type DosageFormROARequest = z.infer<typeof dosageFormROARequestSchema>;

export interface DosageFormROAFilters{
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  dosageFormId?: number;
  roaId?: number;
  organizationId?: number;
};