"use client";

import { DosageFormROAResponse } from "@/app/inventory/dosage-form-roa/types/dosage-form-roa.types";
import { useListDosageFormROA } from "../query/dosage-form-roa.query";
import { CommonAutoComplete } from "@/app/common/dropdown/CommonAutoComplete";
import { InputNumber, Switch } from "antd";
import { MRT_ColumnDef } from "material-react-table";
import { DynamicColumn } from "@/app/common/types/common.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { useCreateDosageFormROA } from "../query/dosage-form-roa.query";
import { OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";

export const statusOptions = ['active', 'inactive'] as const;
export const statusMap = {
  active: 'Active',
  inactive: 'Inactive'
} as const;

export function getStatusLabel(status: string) {
  return statusMap[status as keyof typeof statusMap] || status;
}

export const DosageFormROAColumns = (
  rows: DosageFormROAResponse[] = [],
  setRows: React.Dispatch<React.SetStateAction<DosageFormROAResponse[]>> = () => {},
  createMutation: ReturnType<typeof useCreateDosageFormROA>
): DynamicColumn[] => {
  const handleChange = (
    index: number,
    field: keyof DosageFormROAResponse,
    value: unknown
  ) => {
    if (rows) {
      const updated = [...rows];
      updated[index][field] = value;
      setRows(updated);
    }
  };

  function addRows() {
    const dosageFormROA: DosageFormROAResponse = {
      id: 0,
      dosageFormId: 0,
      roaId: 0,
      displayOrder: 0,
      status: 'active',
      organizationId: 0,
      createdAt: "",
      updatedAt: "",
      createdBy: 0,
      updatedBy: 0,
    };
    setRows([...rows, dosageFormROA]);
  }

  return [
    { accessorKey: "id", header: "ID", isEditable: false },
    {
      accessorKey: "roa.name",
      header: "Route of Administration",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <CommonAutoComplete<DosageFormROAResponse, number>
            value={rows[rowIndex].roaId}
            onChange={(val: number | "") =>
              handleChange(rowIndex, "roaId", val || 0)
            }
            useDataQuery={useListDosageFormROA}
            labelKey="name"
            valueKey="id"
          />
        ) : null,
    },
    {
      accessorKey: "status",
      header: "Status",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <Switch
            checked={rows[rowIndex].status === 'active'}
            onChange={(checked) => handleChange(rowIndex, "status", checked ? 'active' : 'inactive')}
          />
        ) : null,
    },
    {
      accessorKey: "organization.name",
      header: "Organization",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <CommonAutoComplete<OrganizationResponse, number>
            value={rows[rowIndex].organizationId}
            onChange={(val: number | "") =>
              handleChange(rowIndex, "organizationId", val || 0)
            }
            useDataQuery={useListOrganization}
            labelKey="name"
            valueKey="id"
          />
        ) : null,
    },
    {
      accessorKey: "displayOrder",
      header: "Display Order",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <InputNumber
            min={0}
            value={rows[rowIndex].displayOrder}
            onChange={(val) => handleChange(rowIndex, "displayOrder", val)}
            onKeyDown={async (e) => {
              const isLastRow = rowIndex === rows.length - 1;
              if (e.key === "Tab" && !e.shiftKey && isLastRow) {
                const row = rows[rowIndex];

                // Validate minimum required fields before create
                if (row.dosageFormId > 0 && row.roaId > 0) {
                  const payload = {
                    dosageFormId: row.dosageFormId,
                    roaId: row.roaId,
                    displayOrder: row.displayOrder,
                    status: row.status,
                    organizationId: row.organizationId,
                  };

                  // Await creation then add new row
                  createMutation.mutate(payload, {
                    onSuccess: () => {
                      setTimeout(() => addRows(), 0);
                    },
                  });
                }
              }
            }}
          />
        ) : null,
    },
  ];
};

export const dosageFormROAColumns: MRT_ColumnDef<DosageFormROAResponse>[] = [
  { accessorKey: "id", header: "ID", grow: false, size: 50 },
  { accessorKey: 'dosageForm.name', header: 'Dosage Form' },
  { accessorKey: 'roa.name', header: 'ROA' },
  { accessorKey: 'displayOrder', header: 'Display Order' },
  { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    } 
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: 'organization.name', header: 'Organization' },
  { accessorKey: "createdBy", header: "Created By" },
  { accessorKey: "updatedBy", header: "Updated By" },
];