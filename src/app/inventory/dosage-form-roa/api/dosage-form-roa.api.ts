import apiClient from "@/app/api/api";
import { 
  DosageFormROAFilters,
  DosageFormROAPaginatedResponse, 
  DosageFormROAResponse, 
  DosageFormROASuccessResponse, 
  DosageFormROARequest 
} from "../types/dosage-form-roa.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listDosageFormROA = async (
  page?: number,
  limit?: number,
  filters?: DosageFormROAFilters,
): Promise<DosageFormROAPaginatedResponse> => {
  const res = await apiClient.get<DosageFormROAPaginatedResponse>("/dosage-form-roas", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string,unknown>)
    },
  });
  return res.data;
};

export const getDosageFormROA = async (
  id: number
): Promise<DosageFormROAResponse> => {
  const res = await apiClient.get<DosageFormROAResponse>(`/dosage-form-roas/${id}`);
  return res.data;
}

export const createDosageFormROA = async (
  dosageFormROARequest: DosageFormROARequest
): Promise<DosageFormROASuccessResponse> => {
  const res = await apiClient.post<DosageFormROASuccessResponse>("/dosage-form-roas", dosageFormROARequest);
  return res.data;
}

export const updateDosageFormROA = async (
  id: number,
  dosageFormROARequest: DosageFormROARequest
): Promise<DosageFormROASuccessResponse> => {
  const res = await apiClient.patch<DosageFormROASuccessResponse>(`/dosage-form-roas/${id}`, dosageFormROARequest);
  return res.data;
}

export const deleteDosageFormROA = async (
  id: number
): Promise<DosageFormROASuccessResponse> => {
  const res = await apiClient.delete<DosageFormROASuccessResponse>(`/dosage-form-roas/${id}`);
  return res.data;
};