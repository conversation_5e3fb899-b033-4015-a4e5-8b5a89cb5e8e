import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createDosageFormROA,
  deleteDosageFormROA,
  getDosageFormROA,
  listDosageFormROA,
  updateDosageFormROA,
} from "../api/dosage-form-roa.api";
import { toast } from "react-hot-toast";
import {
  DosageFormROARequest,
  DosageFormROAFilters,
  DosageFormROAResponse,
  DosageFormROAPaginatedResponse,
} from "../types/dosage-form-roa.types";
import { SuccessResponse } from "@/app/common/types/common.types";
import { AxiosError } from "axios";
import { ApiError } from "next/dist/server/api-utils";

export const useListDosageFormROA = (
  page?: number,
  limit?: number,
  filters: DosageFormROAFilters = {}
) => {
  const filterKey = JSON.stringify(filters);

  return useQuery<DosageFormROAPaginatedResponse, AxiosError<ApiError>>({
    queryKey: ["dosage-form-roas", page, limit, filterKey],
    queryFn: () => listDosageFormROA(page, limit, filters),
  });
};

// 🔍 Fetch single DosageFormROA by ID
export const useGetDosageFormROA = (id: number | undefined) => {
  return useQuery<DosageFormROAResponse, AxiosError<ApiError>>({
    queryKey: ["dosage-form-roas", id],
    queryFn: () => getDosageFormROA(id!),
    enabled: !!id,
  });
};

// ➕ Create DosageFormROA
export const useCreateDosageFormROA = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<DosageFormROAResponse>,
    AxiosError<ApiError>,
    DosageFormROARequest
  >({
    mutationKey: ["createDosageFormROA"],
    mutationFn: (data) => createDosageFormROA(data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["dosage-form-roas"] });
      toast.success(response.message || "Dosage Form ROA created successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create Dosage Form ROA");
    },
  });
};

// ✏️ Update DosageFormROA
export const useUpdateDosageFormROA = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<DosageFormROAResponse>,
    AxiosError<ApiError>,
    { id: number; dosageFormROARequest: DosageFormROARequest }
  >({
    mutationKey: ["updateDosageFormROA"],
    mutationFn: ({ id, dosageFormROARequest }) =>
      updateDosageFormROA(id, dosageFormROARequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["dosage-form-roas"] });
      toast.success(response.message || "Dosage Form ROA updated successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update Dosage Form ROA");
    },
  });
};

// ❌ Delete DosageFormROA
export const useDeleteDosageFormROA = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<DosageFormROAResponse>,
    AxiosError<ApiError>,
    number
  >({
    mutationKey: ["deleteDosageFormROA"],
    mutationFn: (id) => deleteDosageFormROA(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["dosage-form-roas"] });
      toast.success(response.message || "Dosage Form ROA deleted successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete Dosage Form ROA");
    },
  });
};