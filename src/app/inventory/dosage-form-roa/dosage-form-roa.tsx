"use client";


import { dosageFormROAColumns } from "./types/dosage-form-roa.fields"
import {
  
  DosageFormROAProps,
  DosageFormROAResponse,
} from "./types/dosage-form-roa.types";
import { useListDosageFormROA } from "./query/dosage-form-roa.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import {  useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteDosageFormROA } from "./query/dosage-form-roa.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";

function DosageFormROA({ dosageFormId }: DosageFormROAProps) {
  const router = useRouter();
  const deleteMutation = useDeleteDosageFormROA();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<DosageFormROAResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "DosageFormROA",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);
  const handleOpenDelete = (row: DosageFormROAResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  
  return (
    <>
      <CommonTable<DosageFormROAResponse>
        title="Dosage Form ROA"
        columns={dosageFormROAColumns}
        useDataQuery={useListDosageFormROA}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/dosage-form-roa/create")}
        onExport={() => console.log("Export Dosage Form ROA")}
        onEdit={(row) => router.push(`/inventory/dosage-form-roa/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}
        isFilterable={true}
        initialFilters={[{ id: "dosageFormId", value: dosageFormId }]}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this Dosage Form ROA mapping?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default DosageFormROA;