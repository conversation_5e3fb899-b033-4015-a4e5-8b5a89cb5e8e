"use client";

import { useEffect, useState } from "react";
import { Box } from "@mui/material";
import { Trash } from "lucide-react";
import { DosageFormROAResponse } from "../types/dosage-form-roa.types";
import { DosageFormROAColumns } from "../types/dosage-form-roa.fields";
import { 
  useCreateDosageFormROA, 
  useDeleteDosageFormROA, 
  useListDosageFormROA 
} from "../query/dosage-form-roa.query";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";

interface DosageFormROAFormProps {
  dosageFormId: number;
}

function getValueByAccessor<T>(row: T, accessor: string): unknown {
  return accessor.split(".").reduce<unknown>((acc, key) => {
    if (acc && typeof acc === "object" && key in acc) {
      return (acc as Record<string, unknown>)[key];
    }
    return undefined;
  }, row);
}

function DosageFormROAForm({ dosageFormId }: DosageFormROAFormProps) {
  const createMutation = useCreateDosageFormROA();
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const deleteMutation = useDeleteDosageFormROA();

  // Fetch data from backend
  const { data, isLoading } = useListDosageFormROA(1, 10, { dosageFormId });

  // Local editable state
  const [rows, setRows] = useState<DosageFormROAResponse[]>([]);

  // Sync API data into local rows
  useEffect(() => {
    if (data && data.data.length > 0) {
      setRows(data.data);
    } else {
      // If no data, show an empty row
      const emptyRow: DosageFormROAResponse = {
        id: 0,
        dosageFormId,
        roaId: 0,
        displayOrder: 0,
        status: "active",
        organizationId: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 0,
        updatedBy: 0,
      };
      setRows([emptyRow]);
    }
  }, [data, dosageFormId]);

  const columns = DosageFormROAColumns(rows, setRows, createMutation);

  if (isLoading) {
    return <p className="text-sm text-gray-500">Loading Dosage Form ROAs...</p>;
  }

  return (
    <Box
      sx={{
        backgroundColor: "#f9f9f9",
        borderRadius: 2,
        boxShadow: 2,
        maxWidth: "100%",
        margin: "auto",
        my: 2,
      }}
    >
      <Box className="flex justify-end mb-2">
        <button
          onClick={() => {
            const newRow: DosageFormROAResponse = {
              id: 0,
              dosageFormId,
              roaId: 0,
              displayOrder: 0,
              status: "active",
              organizationId: 0,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              createdBy: 0,
              updatedBy: 0,
            };
            setRows(prev => [...prev, newRow]);
          }}
          className="bg-[#3A59D1] text-white px-3 py-1 rounded text-xs hover:bg-[#2f48a1]"
        >
          + Add ROA
        </button>
      </Box>

      <table className="w-full border text-xs">
        <thead>
          <tr className="bg-[#3A59D1] text-white">
            {columns.map((column, index) => (
              <th key={index} className="border px-2 py-1 text-left">
                {column.header}
              </th>
            ))}
            <th className="border px-2 py-1 text-left">Actions</th>
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr key={rowIndex}>
              {columns.map((column, colIndex) => (
                <td key={colIndex} className="border px-2 py-1">
                  {column.Edit
                    ? column.Edit(rowIndex)
                    : String(getValueByAccessor(row, column.accessorKey) ?? "")}
                </td>
              ))}
              <td className="border px-2 py-1">
                {rows.length > 1 && (
                  <button
                    onClick={() => {
                      setDeleteIndex(rowIndex);
                      setConfirmOpen(true);
                    }}
                    className="text-red-500 hover:text-red-700 p-1"
                    title="Delete"
                  >
                    <Trash size={16} />
                  </button>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <Dialog open={confirmOpen} onClose={() => setConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this ROA mapping?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmOpen(false)}>Cancel</Button>
          <Button
            onClick={() => {
              if (deleteIndex === null) return;

              const row = rows[deleteIndex];
              setConfirmOpen(false);

              if (row.id === 0) {
                setRows(prev => prev.filter((_, i) => i !== deleteIndex));
              } else {
                deleteMutation.mutate(row.id, {
                  onSuccess: () => {
                    setRows(prev => prev.filter((_, i) => i !== deleteIndex));
                  },
                });
              }

              setDeleteIndex(null);
            }}
            color="error"
            variant="contained"
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default DosageFormROAForm;