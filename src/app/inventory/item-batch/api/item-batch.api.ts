import apiClient from "@/app/api/api";
import { ItemBatchFilters, ItemBatchPaginatedResponse, ItemBatchResponse, ItemBatchSuccessResponse, ItemBatchRequest } from "../types/item-batch.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listItemBatches = async (
  page?: number,
  limit?: number,
  filters?: ItemBatchFilters,
): Promise<ItemBatchPaginatedResponse> => {
  const res = await apiClient.get<ItemBatchPaginatedResponse>("/item-batch-numbers", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string,unknown>)
    },
  });
  return res.data;
};

export const getItemBatch = async (
  id: number
): Promise<ItemBatchResponse> => {
  const res = await apiClient.get<ItemBatchResponse>(`/item-batch-numbers/${id}`);
  return res.data;
};

export const createItemBatch = async (
  itemBatchRequest: ItemBatchRequest
): Promise<ItemBatchSuccessResponse> => {
  const res = await apiClient.post<ItemBatchSuccessResponse>("/item-batch-numbers", itemBatchRequest);
  return res.data;
};

export const updateItemBatch = async (
  id: number,
  itemBatchRequest: ItemBatchRequest
): Promise<ItemBatchSuccessResponse> => {
  const res = await apiClient.patch<ItemBatchSuccessResponse>(`/item-batch-numbers/${id}`, itemBatchRequest);
  return res.data;
};
export const deleteItemBatch = async (
  id: number
): Promise<ItemBatchSuccessResponse> => {
  const res = await apiClient.delete<ItemBatchSuccessResponse>(`/item-batch-numbers/${id}`);
  return res.data;
};