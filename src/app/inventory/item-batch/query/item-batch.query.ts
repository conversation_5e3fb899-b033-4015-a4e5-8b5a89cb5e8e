import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createItemBatch,
  deleteItemBatch,
  getItemBatch,
  listItemBatches,
  updateItemBatch,
} from "../api/item-batch.api";
import {
  ItemBatchFilters,
  ItemBatchRequest,
  ItemBatchSuccessResponse,
} from "../types/item-batch.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";


export const useListItemBatches = (
  page?: number,
  limit?: number,
  filters: ItemBatchFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery({
    queryKey: ["item-batch-numbers", page, limit, filterKey],
    queryFn: () => listItemBatches(page, limit, filters),
  });
};

export const useGetItemBatch = (id: number | undefined) => {
  return useQuery({
    queryKey: ["item-batch-number", id],
    queryFn: () => getItemBatch(id!),
    enabled: !!id,
  });
};

export const useCreateItemBatch = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ItemBatchSuccessResponse,
    ApiErrorResponse,
    ItemBatchRequest
  >({
    mutationFn: createItemBatch,
    mutationKey: ["createItemBatch"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["item-batch-numbers"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateItemBatch = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ItemBatchSuccessResponse,
    ApiErrorResponse,
    { id: number; itemBatchRequest: ItemBatchRequest }
  >({
    mutationKey: ["updateItemBatch"],
    mutationFn: ({ id, itemBatchRequest }) =>
      updateItemBatch(id, itemBatchRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["item-batch-numbers"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteItemBatch = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ItemBatchSuccessResponse,
    ApiErrorResponse,
    number
  >({
    mutationKey: ["deleteItemBatch"],
    mutationFn: (id) => deleteItemBatch(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["item-batch-numbers"] });
      toast.success(data.message || "Item batch deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
