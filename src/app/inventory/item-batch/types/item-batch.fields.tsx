"use client";

import { ItemBatchResponse } from "@/app/inventory/item-batch/types/item-batch.types";
import { DynamicColumn } from "@/app/common/types/common.types";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { TextField, MenuItem, Box } from "@mui/material";
import { statusOptions, statusMap, getStatusLabel } from "@/app/common/types/status.types";
import { MRT_ColumnDef } from "material-react-table";
import { localTime } from "@/app/common/utils/serialize.utils";
import { useCreateItemBatch } from "../query/item-batch.query";
import { CommonAutoComplete } from "@/app/common/dropdown/CommonAutoComplete";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { OrganizationResponse } from "@/app/organization/organization/types/organization.types";

export const ItemBatchColumns = (
  rows: ItemBatchResponse[] = [], 
  setRows: React.Dispatch<React.SetStateAction<ItemBatchResponse[]>>=()=>{},
  createMutation: ReturnType<typeof useCreateItemBatch>,
  editableRowIndex: number | null
): DynamicColumn[] => {
  const handleChange = (
    index: number,
    field: keyof ItemBatchResponse,
    value: unknown
  ) => {
    const updated = [...rows];
    updated[index][field] = value;
    setRows(updated);
  };

  function addRows() {
    const itemBatch: ItemBatchResponse = {
        id: 0,
        itemId: 0,
        batchNumber: "",
        mfgDate: null,
        expDate: "",
        status: "active",
        organizationId: 0,
        createdAt: "",
        updatedAt: "",
        createdBy: 0,
        updatedBy: 0,
    };
    setRows([...rows, itemBatch]);
  }

  return [
    { accessorKey: "id", header: "ID", isEditable: false },
    {
      accessorKey: "batchNumber",
      header: "Batch Number",
      Edit: (rowIndex: number) => (
        <TextField
          size="small"
          value={rows[rowIndex].batchNumber}
          onChange={(e) =>
            handleChange(rowIndex, "batchNumber", e.target.value)
          }
          fullWidth
        />
      ),
    },
    {
      accessorKey: "mfgDate",
      header: "Manufacturing Date",
      Edit: (rowIndex: number) => (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DatePicker
            value={rows[rowIndex].mfgDate ? dayjs(rows[rowIndex].mfgDate) : null}
            onChange={(date) =>
              handleChange(
                rowIndex,
                "mfgDate",
                date ? date.toISOString() : null
              )
            }
            slotProps={{ textField: { size: "small", fullWidth: true } }}
          />
        </LocalizationProvider>
      ),
    },
    {
      accessorKey: "expDate",
      header: "Expiry Date",
      Edit: (rowIndex: number) => (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DatePicker
            value={rows[rowIndex].expDate ? dayjs(rows[rowIndex].expDate) : null}
            onChange={(date) =>
              handleChange(
                rowIndex,
                "expDate",
                date ? date.toISOString() : ""
              )
            }
            slotProps={{ textField: { size: "small", fullWidth: true } }}
          />
        </LocalizationProvider>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      Edit: (rowIndex: number) => (
        <TextField
          select
          size="small"
          value={rows[rowIndex].status}
          onChange={(e) => handleChange(rowIndex, "status", e.target.value)}
          fullWidth
        >
          {statusOptions.map((val) => (
            <MenuItem key={val} value={val}>
              {statusMap[val]}
            </MenuItem>
          ))}
        </TextField>
      ),
    },
    {
        accessorKey: "organization.name",
        header: "Organization",
        Edit: (rowIndex: number) => {
          const row = rows[rowIndex];
          const isNewRow = row.id === 0;
          const isLastRow = rowIndex === rows.length - 1;

          const isEditable = editableRowIndex === rowIndex || isNewRow;
          if (!isEditable) return <span>{row.organization?.name || ""}</span>;
          
          if (isNewRow) {
            return (
              <Box
                onKeyDown={async (e) => {
                  if (
                    e.key === "Tab" &&
                    !e.shiftKey &&
                    isLastRow &&
                    isNewRow
                  ) 
                    {
                      const mfg = row.mfgDate ? new Date(row.mfgDate) : null;
                      const exp = row.expDate ? new Date(row.expDate) : null;

                      if (!mfg || !exp) {
                        // Prevent submission if either date is missing
                        return;
                      }

                      if (exp <= mfg) {
                        // Show alert or log error if dates are invalid
                        alert("Expiry Date must be greater than Manufacturing Date.");
                        return;
                    }
                  {
                    const payload = {
                      itemId: row.itemId,
                      mfgDate: row.mfgDate,
                      batchNumber: row.batchNumber,
                      expDate: row.expDate,
                      status: row.status,
                      organizationId: row.organizationId,
                    };

                    createMutation.mutate(payload, {
                      onSuccess: () => {
                        setTimeout(() => addRows(), 0);
                      },
                    });
                  }
                }}
                }
              >
                <CommonAutoComplete<OrganizationResponse, number>
                  value={row.organizationId}
                  onChange={(val) =>
                    handleChange(rowIndex, "organizationId", val || 0)
                  }
                  useDataQuery={useListOrganization}
                  labelKey="name"
                  valueKey="id"
                />
              </Box>
            );
          }
          return null;
        },
      }
  ];
};

export const itemBatchTableColumns: MRT_ColumnDef<ItemBatchResponse>[] = [
  { accessorKey: "id", header: "ID" },
  { accessorKey: "batchNumber", header: "Batch Number" },
  { accessorKey: "mfgDate", header: "Manufacturing Date",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: "expDate", header: "Expiry Date" ,
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    filterVariant: "select",
    filterSelectOptions: statusOptions.map((value) => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => getStatusLabel(cell.getValue<string>()),
  },
  { accessorKey: "organization.name", header: "Organization" },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: "createdBy", header: "Created By" },
  { accessorKey: "updatedBy", header: "Updated By" },
];
