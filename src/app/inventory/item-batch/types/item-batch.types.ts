import { z } from "zod";
import { BaseResponse, PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { OrganizationResponse } from "@/app/organization/organization/types/organization.types";

export interface ItemBatchNumberProps {
  itemId?: number;
}

export interface ItemBatchResponse extends BaseResponse, Record<string, unknown> {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  itemId: number;
  batchNumber: string;
  mfgDate: string | null;
  expDate: string;
  status: string;
  organizationId: number;
  organization?: OrganizationResponse;
}

export type ItemBatchPaginatedResponse = PaginatedResponse<ItemBatchResponse>;
export type ItemBatchSuccessResponse = SuccessResponse<ItemBatchResponse>;

export const itemBatchRequestSchema = z.object({
  itemId: z.number().int().positive("Item ID must be a positive number"),
  batchNumber: z.string()
    .min(1, "Batch number is required")
    .max(50, "Batch number cannot exceed 50 characters"),
  mfgDate: z.string().nullable().optional(),
  expDate: z.string().min(1, "Expiry date is required"),
  status: z.string().min(1, "Status is required"),
  organizationId: z.number().int().positive("Organization ID must be a positive number").optional(),
});

export type ItemBatchRequest = z.infer<typeof itemBatchRequestSchema>;

export interface ItemBatchFilters {
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  itemId?: number;
  batchNumber?: string;
  mfgDate?: string;
  expDate?: string;
  status?: string;
  organizationId?: number;
}
