import apiClient from "@/app/api/api";
import { CombinationGenericPaginatedResponse, CombinationGenericResponse, CombinationGenericSuccessResponse, CombinationGenericRequest, CombinationGenericFilters } from "../types/combination-generic.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listCombinationGeneric = async (
  page?: number,
  limit?: number,
  filters?: CombinationGenericFilters
): Promise<CombinationGenericPaginatedResponse> => {
  const res = await apiClient.get<CombinationGenericPaginatedResponse>("/combination-generic", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};

export const getCombinationGeneric = async (
  id: number
): Promise<CombinationGenericResponse> => {
  const res = await apiClient.get<CombinationGenericResponse>(`/combination-generic/${id}`);
  return res.data;
}

export const createCombinationGeneric = async (
  CombinationGenericRequest: CombinationGenericRequest
): Promise<CombinationGenericSuccessResponse> => {
  const res = await apiClient.post<CombinationGenericSuccessResponse>("/combination-generic", CombinationGenericRequest);
  return res.data;
}

export const updateCombinationGeneric = async (
  id: number,
  CombinationGenericRequest: CombinationGenericRequest
): Promise<CombinationGenericSuccessResponse> => {
  const res = await apiClient.patch<CombinationGenericSuccessResponse>(`/combination-generic/${id}`, CombinationGenericRequest);
  return res.data;
}

export const deleteCombinationGeneric = async (
  id: number
): Promise<CombinationGenericSuccessResponse> => {
  const res = await apiClient.delete<CombinationGenericSuccessResponse>(`/combination-generic/${id}`);
  return res.data;
};