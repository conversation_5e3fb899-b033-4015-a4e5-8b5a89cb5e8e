"use client";

import { CombinationGenericColumns, CombinationGenericResponse } from "./types/combination-generic.types";
import { useListCombinationGeneric } from "./query/combination-generic.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteCombinationGeneric } from "./query/combination-generic.query";

import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import CombinationsForm from "./forms/combination-generic.forms";

function CombinationGenericPage() {
  const router = useRouter();
  const deleteMutation = useDeleteCombinationGeneric();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<CombinationGenericResponse | null>(
    null
  );
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "CombinationGeneric",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  // ✅ Render inside a page
function Page() {
  return <CombinationsForm />;
}


  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);

  // Open modal and set row to delete
  const handleOpenDelete = (row: CombinationGenericResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
    Page();
  };

  // Close modal and clear selected row
  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          // Optionally, refetch list or show success message
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          // Optionally show error notification
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
          <CommonTable<CombinationGenericResponse>
            title="CombinationGenerics"
            columns={CombinationGenericColumns}
            useDataQuery={useListCombinationGeneric}
            hiddenColumns={[
              "organization.name",
              "createdAt",
              "updatedAt",
              "createdBy",
              "updatedBy",
            ]}
            onCreate={() => router.push("/inventory/combination-generic/create")}
            onExport={() => console.log("Export Category")}
            onEdit={(row) => router.push(`/inventory/combination-generic/edit/${row.id}`)}
            onDelete={(row) => handleOpenDelete(row)}
            isCreate={rolePermissionData?.data[0].create}
            isEdit={rolePermissionData?.data[0].update}
            isDelete={rolePermissionData?.data[0].delete}
          />

          <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Are you sure you want to delete CombinationGeneric item{" "}
                <strong>{selectedRow?.genericId}</strong>? This action cannot be
                undone.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDelete} variant="outlined">
                Cancel
              </Button>
              <Button
                onClick={handleConfirmDelete}
                variant="contained"
                color="error"
                disabled={deleteMutation.isPending}
              >
                Confirm
              </Button>
            </DialogActions>
          </Dialog>
      
    </>
  );
}

export default CombinationGenericPage;
