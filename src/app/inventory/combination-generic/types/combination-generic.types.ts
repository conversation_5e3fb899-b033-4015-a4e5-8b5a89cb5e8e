import { MRT_ColumnDef } from "material-react-table";
import { z } from "zod";
import {
  statusOptions,
  statusMap,
  getStatusLabel,
} from "@/app/common/types/status.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";
export interface CombinationGenericProps {
  itemId?: number;
}

export interface CombinationGenericResponse extends Record<string, unknown> {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  description: string;
  combinationId: number;
  genericId: number;
  displayOrder: number;
  status: "active" | "inactive";
  organizationId: number;
  generics: {
    genericId: number;
  }[];
}

export const CombinationGenericColumns: MRT_ColumnDef<CombinationGenericResponse>[] = [
  { accessorKey: "id", header: "ID", grow: false, size: 50 },
  { accessorKey: "description", header: "Description" },
  {
    accessorKey: "status",
    header: "Status",
    filterVariant: "select",
    filterSelectOptions: statusOptions.map((value) => ({
      value,
      label: statusMap[value], // Capitalize first letter
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: "organization.name", header: "Organization" },
  { accessorKey: "generic.name", header: "Generic"},
  { accessorKey: "combination.name", header: "Combination"}
];

export type CombinationGenericPaginatedResponse = PaginatedResponse<CombinationGenericResponse>;
export type CombinationGenericSuccessResponse = SuccessResponse<CombinationGenericResponse>;

export const CombinationGenericRequestSchema = z.object({
  description: z.string()
    .nullable()
    .transform((val) => (val != null ? val.trim() : val)),
  status: z.enum(["active", "inactive"]),
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number")
    .optional(),
  combinationId: z.number().int(),
  displayOrder: z.coerce.number().int().refine((val) => !isNaN(val), {
    message: "Display order must be a number",
  }),
  genericId: z.number().int(),
  generics: z
    .array(
      z.object({
        genericId: z.number().nullable(),
      })
    )
    .min(1, "At least one generic is required"),
});

export type CombinationGenericRequest = z.infer<typeof CombinationGenericRequestSchema>;

export interface CombinationGenericFilters {
  status?: "active" | "inactive";
  organizationId?: number;
  genericId?: number;
  combinationId?: number;
  displayOrder?: number;
  createdAt?: string; // ISO date string
  updatedAt?: string; // ISO date string
  createdBy?: number;
  updatedBy?: number;
}
