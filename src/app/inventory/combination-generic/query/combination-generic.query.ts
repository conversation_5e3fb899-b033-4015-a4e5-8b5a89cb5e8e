import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createCombinationGeneric,
  deleteCombinationGeneric,
  getCombinationGeneric,
  listCombinationGeneric,
  updateCombinationGeneric,
} from "../api/combination-generic.api";
import {
  CombinationGenericFilters,
  CombinationGenericPaginatedResponse,
  CombinationGenericRequest,
  CombinationGenericResponse,
  CombinationGenericSuccessResponse,
} from "../types/combination-generic.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListCombinationGeneric = (
  page?: number,
  limit?: number,
  filters: CombinationGenericFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<CombinationGenericPaginatedResponse, ApiErrorResponse>({
    queryKey: ["combination-generic", page, limit, filterKey],
    queryFn: () => listCombinationGeneric(page, limit, filters),
  });
};

export const useGetCombinationGeneric = (id: number | undefined) => {
  return useQuery<CombinationGenericResponse, ApiErrorResponse>({
    queryKey: ["combination-generic", id],
    queryFn: () => getCombinationGeneric(id!),
    enabled: !!id,
  });
};

export const useCreateCombinationGeneric = () => {
  const queryClient = useQueryClient();
  return useMutation<
    CombinationGenericSuccessResponse,
    ApiErrorResponse,
    CombinationGenericRequest
  >({
    mutationKey: ["createCombinationGeneric"],
    mutationFn: (CombinationGenericRequest: CombinationGenericRequest) =>
      createCombinationGeneric(CombinationGenericRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["combination-generic"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create CombinationGeneric");
    },
  });
};

export const useUpdateCombinationGeneric = () => {
  const queryClient = useQueryClient();
  return useMutation<
    CombinationGenericSuccessResponse,
    ApiErrorResponse,
    { id: number; CombinationGenericRequest: CombinationGenericRequest }
  >({
    mutationKey: ["updateCombinationGeneric"],
    mutationFn: ({
      id,
      CombinationGenericRequest,
    }: {
      id: number;
      CombinationGenericRequest: CombinationGenericRequest;
    }) => updateCombinationGeneric(id, CombinationGenericRequest), // Assuming update uses the same API as create
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["combination-generic"] }); // Invalidate the list query to refresh data
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update CombinationGeneric");
    },
  });
};

export const useDeleteCombinationGeneric = () => {
  const queryClient = useQueryClient();
  return useMutation<CombinationGenericSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteCombinationGeneric"],
    mutationFn: (id: number) => deleteCombinationGeneric(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["combination-generic"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete CombinationGeneric");
    },
  });
};
