import { z } from "zod";
import { BaseResponse, PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { StoreResponse } from "@/app/inventory/stores/types/stores.types";
import { ItemBatchResponse } from "@/app/inventory/item-batch/types/item-batch.types";

export interface StockAuditItemProps {
  stockAuditId?: number;
}

export interface StockAuditItemResponse extends BaseResponse, Record<string, unknown> {
  id: number;
  stockAuditId: number;
  subLocationId: number;
  itemBatchId: number;
  systemStock: number;
  physicalStock: number;
  differenceType: number;
  subLocation?: StoreResponse;
  itemBatch?: ItemBatchResponse;
}

export type StockAuditItemPaginatedResponse = PaginatedResponse<StockAuditItemResponse>;
export type StockAuditItemSuccessResponse = SuccessResponse<StockAuditItemResponse>;

export const stockAuditItemRequestSchema = z.object({
  stockAuditId: z.number().int().positive("Stock Audit ID is required"),
  subLocationId: z.number().int().positive("Sub Location ID is required"),
  itemBatchId: z.number().int().positive("Item Batch ID is required"),
  systemStock: z.number().int().nonnegative("System stock must be non-negative"),
  physicalStock: z.number().int().nonnegative("Physical stock must be non-negative"),
  differenceType: z.number().int().nonnegative("Difference Type must be non-negative"),
});

export type StockAuditItemRequest = z.infer<typeof stockAuditItemRequestSchema>;

export interface StockAuditItemFilters {
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  stockAuditId?: number;
  subLocationId?: number;
  itemBatchId?: number;
  differenceType?: number;
};