"use client";

import { StockAuditItemResponse } from "@/app/inventory/stock-audit-items/types/stock-audit-items.types";
import { useListStores } from "../../stores/query/stores.query";
import { StoreResponse } from "../../stores/types/stores.types";
import { useListItemBatches } from "../../item-batch/query/item-batch.query";
import { ItemBatchResponse } from "../../item-batch/types/item-batch.types";
import { CommonAutoComplete } from "@/app/common/dropdown/CommonAutoComplete";
import { InputNumber } from "antd";
import { MRT_ColumnDef } from "material-react-table";
import { DynamicColumn } from "@/app/common/types/common.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { useCreateStockAuditItem } from "../query/stock-audit-items.query";

export const StockAuditItemColumns = (
  rows: StockAuditItemResponse[] = [],
  setRows: React.Dispatch<React.SetStateAction<StockAuditItemResponse[]>> = () => {},
  createMutation: ReturnType<typeof useCreateStockAuditItem>,
  editableRowIndex: number | null
): DynamicColumn[] => {
  const handleChange = (
    index: number,
    field: keyof StockAuditItemResponse,
    value: unknown
  ) => {
    if (rows) {
      const updated = [...rows];
      updated[index][field] = value;
      setRows(updated);
    }
  };

  function addRows() {
    const stockAuditItem: StockAuditItemResponse = {
      id: 0,
      stockAuditId: 0,
      subLocationId: 0,
      itemBatchId: 0,
      systemStock: 0,
      physicalStock: 0,
      differenceType: 0,
      createdAt: "",
      updatedAt: "",
      createdBy: 0,
      updatedBy: 0
    };
    setRows([...rows, stockAuditItem]);
  }

  return [
    { accessorKey: "id", header: "ID", isEditable: false },

    {
      accessorKey: "subLocation.name",
      header: "Sub Location",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.subLocation?.name || ""}</span>;

        return (
          <CommonAutoComplete<StoreResponse, number>
            value={row.subLocationId}
            onChange={(val) => handleChange(rowIndex, "subLocationId", val || 0)}
            useDataQuery={useListStores}
            labelKey="name"
            valueKey="id"
          />
        );
      },
    },

    {
      accessorKey: "itemBatch.batchNumber",
      header: "Item Batch",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.itemBatch?.batchNumber || ""}</span>;

        return (
          <CommonAutoComplete<ItemBatchResponse, number>
            value={row.itemBatchId}
            onChange={(val) => handleChange(rowIndex, "itemBatchId", val || 0)}
            useDataQuery={useListItemBatches}
            labelKey="batchNumber"
            valueKey="id"
          />
        );
      },
    },

    {
      accessorKey: "systemStock",
      header: "System Stock",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.systemStock}</span>;

        return (
          <InputNumber
            min={0}
            value={row.systemStock}
            onChange={(val) => handleChange(rowIndex, "systemStock", val)}
          />
        );
      },
    },

    {
      accessorKey: "physicalStock",
      header: "Physical Stock",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.physicalStock}</span>;
        
        return (
          <InputNumber
            min={0}
            value={row.physicalStock}
            onChange={(val) => handleChange(rowIndex, "physicalStock", val)}
            
          />
        );
      },
    },

    {
      accessorKey: "differenceType",
      header: "Difference Type",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        const isNewRow = row.id === 0;
        const isEditable = editableRowIndex === rowIndex || isNewRow;

        if (!isEditable) return <span>{row.differenceType}</span>;

        return (
          <InputNumber
            min={0}
            value={row.differenceType}
            onChange={(val) => handleChange(rowIndex, "differenceType", val)}
            onKeyDown={async (e) => {
              const isLastRow = rowIndex === rows.length - 1;
              if (e.key === "Tab" && !e.shiftKey && isLastRow && isNewRow) {
                if (row.subLocationId > 0 && row.itemBatchId > 0) {
                  const payload = {
                    stockAuditId: row.stockAuditId,
                    subLocationId: row.subLocationId,
                    itemBatchId: row.itemBatchId,
                    systemStock: row.systemStock,
                    physicalStock: row.physicalStock,
                    differenceType: row.differenceType,
                  };

                  createMutation.mutate(payload, {
                    onSuccess: () => {
                      setTimeout(() => addRows(), 0);
                    },
                  });
                }
              }
            }}
          />
        );
      },
    },
  ];
};

export const stockAuditItemColumns: MRT_ColumnDef<StockAuditItemResponse>[] = [
  { accessorKey: "id", header: "ID", grow: false, size: 50 },
  { accessorKey: 'subLocation.name', header: 'Sub Location' },
  { accessorKey: 'itemBatch.batchNumber', header: 'Item Batch' },
  { accessorKey: 'systemStock', header: 'System Stock' },
  { accessorKey: 'physicalStock', header: 'Physical Stock' },
  { accessorKey: 'differenceType', header: 'Difference Type' },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: "createdBy", header: "Created By" },
  { accessorKey: "updatedBy", header: "Updated By" },
];