import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createStockAuditItem,
  deleteStockAuditItem,
  getStockAuditItem,
  listStockAuditItems,
  updateStockAuditItem,
} from "../api/stock-audit-items.api";
import { StockAuditItemRequest, StockAuditItemSuccessResponse } from "../types/stock-audit-items.types";
import toast from "react-hot-toast";
import { StockAuditItemFilters } from "../types/stock-audit-items.types";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListStockAuditItems = (
  page?: number,
  limit?: number,
  filters: StockAuditItemFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery({
    queryKey: ["stock-audit-items", page, limit, filterKey],
    queryFn: () => listStockAuditItems(page, limit, filters),
  });
};

export const useGetStockAuditItem = (id: number | undefined) => {
  return useQuery({
    queryKey: ["stock-audit-item", id],
    queryFn: () => getStockAuditItem(id!),
    enabled: !!id,
  });
};

export const useCreateStockAuditItem = () => {
  const queryClient = useQueryClient();
  return useMutation<
    StockAuditItemSuccessResponse,
    ApiErrorResponse,
    StockAuditItemRequest
  >({
    mutationFn: createStockAuditItem,
    mutationKey: ["createStockAuditItem"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["stock-audit-items"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateStockAuditItem = () => {
  const queryClient = useQueryClient();
  return useMutation<
    StockAuditItemSuccessResponse,
    ApiErrorResponse,
    { id: number; stockAuditItemRequest: StockAuditItemRequest }
  >({
    mutationKey: ["updateStockAuditItem"],
    mutationFn: ({ id, stockAuditItemRequest }) => updateStockAuditItem(id, stockAuditItemRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["stock-audit-items"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteStockAuditItem = () => {
  const queryClient = useQueryClient();
  return useMutation<
    StockAuditItemSuccessResponse,
    ApiErrorResponse,
    number
  >({
    mutationKey: ["deleteStockAuditItem"],
    mutationFn: (id) => deleteStockAuditItem(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["stock-audit-items"] });
      toast.success(data.message || "Stock Audit Item deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};