"use client";

import { useEffect, useState } from "react";
import { Box } from "@mui/material";
import { Check, Pencil, Trash } from "lucide-react";
import { StockAuditItemResponse } from "../types/stock-audit-items.types";
import { StockAuditItemColumns } from "../types/stock-audit-items.fields";
import { 
  useCreateStockAuditItem, 
  useDeleteStockAuditItem, 
  useListStockAuditItems, 
  useUpdateStockAuditItem 
} from "../query/stock-audit-items.query";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";

function getValueByAccessor<T>(row: T, accessor: string): unknown {
  return accessor.split(".").reduce<unknown>((acc, key) => {
    if (acc && typeof acc === "object" && key in acc) {
      return (acc as Record<string, unknown>)[key];
    }
    return undefined;
  }, row);
}

function StockAuditItemForm({ stockAuditId }: { stockAuditId: number }) {
  const createMutation = useCreateStockAuditItem();
  const [editableRowIndex, setEditableRowIndex] = useState<number | null>(null);
  const updateMutation = useUpdateStockAuditItem();
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const deleteMutation = useDeleteStockAuditItem();

  const { data, isLoading } = useListStockAuditItems(1, 10, { stockAuditId });
  const [rows, setRows] = useState<StockAuditItemResponse[]>([]);

  useEffect(() => {
    if (data && data.data.length > 0) {
      setRows(data.data);
    } else {
      const emptyRow: StockAuditItemResponse = {
        id: 0,
        stockAuditId,
        subLocationId: 0,
        itemBatchId: 0,
        systemStock: 0,
        physicalStock: 0,
        differenceType: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 0,
        updatedBy: 0,
      };
      setRows([emptyRow]);
    }
  }, [data, stockAuditId]);

const columns = StockAuditItemColumns(
  rows, 
  setRows, 
  createMutation,
  editableRowIndex
);

  if (isLoading) {
    return <p className="text-sm text-gray-500">Loading Stock Audit Items...</p>;
  }

  const handleAddRow = () => {
    const newRow: StockAuditItemResponse = {
      id: 0,
      stockAuditId,
      subLocationId: 0,
      itemBatchId: 0,
      systemStock: 0,
      physicalStock: 0,
      differenceType: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 0,
      updatedBy: 0,
    };
    setRows(prev => [...prev, newRow]);
  };

  const handleSaveRow = (rowIndex: number) => {
    const row = rows[rowIndex];
    if (row.id === 0) {
      createMutation.mutate({
        stockAuditId: row.stockAuditId,
        subLocationId: row.subLocationId,
        itemBatchId: row.itemBatchId,
        systemStock: row.systemStock,
        physicalStock: row.physicalStock,
        differenceType: row.differenceType,
      }, {
        onSuccess: () => {
          setEditableRowIndex(null);
        },
      });
    } else {
      updateMutation.mutate({
        id: row.id,
        stockAuditItemRequest: {
          stockAuditId: row.stockAuditId,
          subLocationId: row.subLocationId,
          itemBatchId: row.itemBatchId,
          systemStock: row.systemStock,
          physicalStock: row.physicalStock,
          differenceType: row.differenceType,
        }
      }, {
        onSuccess: () => {
          setEditableRowIndex(null);
        },
      });
    }
  };

  const handleDeleteRow = () => {
    if (deleteIndex === null) return;

    const row = rows[deleteIndex];
    setConfirmOpen(false);

    if (row.id === 0) {
      setRows(prev => prev.filter((_, i) => i !== deleteIndex));
    } else {
      deleteMutation.mutate(row.id, {
        onSuccess: () => {
          setRows(prev => prev.filter((_, i) => i !== deleteIndex));
        },
      });
    }

    setDeleteIndex(null);
  };

  return (
    <Box
      sx={{
        backgroundColor: "#f9f9f9",
        borderRadius: 2,
        boxShadow: 2,
        maxWidth: "100%",
        margin: "auto",
        my: 2,
      }}
    >
      <Box className="flex justify-end mb-2">
        <button
          onClick={handleAddRow}
          className="bg-[#3A59D1] text-white px-3 py-1 rounded text-xs hover:bg-[#2f48a1]"
        >
          + Add Audit Item
        </button>
      </Box>

      <table className="w-full border text-xs">
        <thead>
          <tr className="bg-[#3A59D1] text-white">
            {columns.map((column, index) => (
              <th key={index} className="border px-2 py-1 text-left">
                {column.header}
              </th>
            ))}
            <th className="border px-2 py-1 text-left">Actions</th>
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr key={rowIndex}>
              {columns.map((column, colIndex) => (
                <td key={colIndex} className="border px-2 py-1">
                  {column.Edit
                    ? column.Edit(rowIndex)
                    : String(getValueByAccessor(row, column.accessorKey) ?? "")}
                </td>
              ))}
              <td className="border px-2 py-1">
                <div className="flex items-center gap-2">
                  {editableRowIndex === rowIndex ? (
                    <button
                      onClick={() => handleSaveRow(rowIndex)}
                      className="text-green-600 hover:text-green-800"
                      title="Save"
                    >
                      <Check size={16} />
                    </button>
                  ) : (
                    <button
                      onClick={() => setEditableRowIndex(rowIndex)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Edit"
                    >
                      <Pencil size={16} />
                    </button>
                  )}
                  <button
                    onClick={() => {
                      setDeleteIndex(rowIndex);
                      setConfirmOpen(true);
                    }}
                    className="text-red-500 hover:text-red-700"
                    title="Delete"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <Dialog open={confirmOpen} onClose={() => setConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this Stock Audit Item?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteRow}
            color="error"
            variant="contained"
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default StockAuditItemForm;