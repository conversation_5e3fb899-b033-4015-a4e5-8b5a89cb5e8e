import apiClient from "@/app/api/api";
import { 
  StockAuditItemFilters,
  StockAuditItemPaginatedResponse, 
  StockAuditItemResponse, 
  StockAuditItemSuccessResponse, 
  StockAuditItemRequest 
} from "../types/stock-audit-items.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listStockAuditItems = async (
  page?: number,
  limit?: number,
  filters?: StockAuditItemFilters,
): Promise<StockAuditItemPaginatedResponse> => {
  const res = await apiClient.get<StockAuditItemPaginatedResponse>("/stock-audit-items", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string,unknown>),
    },
  });
  return res.data;
};

export const getStockAuditItem = async (
  id: number
): Promise<StockAuditItemResponse> => {
  const res = await apiClient.get<StockAuditItemResponse>(`/stock-audit-items/${id}`);
  return res.data;
}

export const createStockAuditItem = async (
  stockAuditItemRequest: StockAuditItemRequest
): Promise<StockAuditItemSuccessResponse> => {
  const res = await apiClient.post<StockAuditItemSuccessResponse>("/stock-audit-items", stockAuditItemRequest);
  return res.data;
}

export const updateStockAuditItem = async (
  id: number,
  stockAuditItemRequest: StockAuditItemRequest
): Promise<StockAuditItemSuccessResponse> => {
  const res = await apiClient.patch<StockAuditItemSuccessResponse>(`/stock-audit-items/${id}`, stockAuditItemRequest);
  return res.data;
}

export const deleteStockAuditItem = async (
  id: number
): Promise<StockAuditItemSuccessResponse> => {
  const res = await apiClient.delete<StockAuditItemSuccessResponse>(`/stock-audit-items/${id}`);
  return res.data;
};