"use client";

import { StockAuditItemResponse } from "./types/stock-audit-items.types";
import { stockAuditItemColumns } from "./types/stock-audit-items.fields";
import { useListStockAuditItems } from "./query/stock-audit-items.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteStockAuditItem } from "./query/stock-audit-items.query";
import { StockAuditItemProps } from "./types/stock-audit-items.types";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";

function StockAuditItems({ stockAuditId }: StockAuditItemProps) {
  const router = useRouter();
  const deleteMutation = useDeleteStockAuditItem();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<StockAuditItemResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "StockAuditItems",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );
  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);
  const handleOpenDelete = (row: StockAuditItemResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<StockAuditItemResponse>
        title="Stock Audit Items"
        columns={stockAuditItemColumns}
        useDataQuery={useListStockAuditItems}
        hiddenColumns={[
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/stock-audit-items/create")}
        onExport={() => console.log("Export Stock Audit Items")}
        onEdit={(row) => router.push(`/inventory/stock-audit-items/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0]?.create}
        isEdit={rolePermissionData?.data[0]?.update}
        isDelete={rolePermissionData?.data[0]?.delete}
        isFilterable={true}
        initialFilters={[{ id: "stockAuditId", value: stockAuditId }]}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this Stock Audit Item?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default StockAuditItems;