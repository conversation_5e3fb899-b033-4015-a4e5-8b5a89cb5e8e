"use client";

import {
  routeOfAdministrationColumns,
  RouteOfAdministrationResponse,
} from "./types/route-of-administration.types";
import { useListRouteOfAdministration } from "./query/route-of-administration.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteRouteOfAdministration } from "./query/route-of-administration.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";

function RouteOfAdministrationPage() {
  const router = useRouter();
  const deleteMutation = useDeleteRouteOfAdministration();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<RouteOfAdministrationResponse | null>(
    null
  );
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "RouteOfAdmin",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);
  // Open modal and set row to delete
  const handleOpenDelete = (row: RouteOfAdministrationResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  // Close modal and clear selected row
  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          // Optionally, refetch list or show success message
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          // Optionally show error notification
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<RouteOfAdministrationResponse>
        title="Route of Administrations"
        columns={routeOfAdministrationColumns}
        useDataQuery={useListRouteOfAdministration}
        hiddenColumns={["organization.name", "createdAt", "updatedAt","createdBy","updatedBy"]}
        onCreate={() => router.push("/inventory/route-of-administration/create")}
        onExport={() => console.log("Export routeOfAdministration")}
        onEdit={(row) =>
          router.push(`/inventory/route-of-administration/edit/${row.id}`)
        }
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete routeOfAdministration item{" "}
            <strong>{selectedRow?.name}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default RouteOfAdministrationPage;
