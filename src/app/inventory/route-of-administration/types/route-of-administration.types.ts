import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from '@/app/common/utils/serialize.utils';

export interface RouteOfAdministrationResponse extends Record<string, unknown> {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  code: string;
  abbreviation: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
  organizationId: number;
}

export const routeOfAdministrationColumns: MRT_ColumnDef<RouteOfAdministrationResponse>[] = [
  { accessorKey: 'id', header: 'ID' , grow: false, size: 50},
  { accessorKey: 'code', header: 'Code' },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'abbreviation', header: 'Abbreviation' },
  { accessorKey: "description", header: "Description" },

   { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value], // Capitalize first letter
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    } 
  },
  { accessorKey: 'organization.name', header: 'Organization' },
  {
      accessorKey: "createdAt",
      header: "Created At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Updated At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type RouteOfAdministrationPaginatedResponse = PaginatedResponse<RouteOfAdministrationResponse>;
export type RouteOfAdministrationSuccessResponse = SuccessResponse<RouteOfAdministrationResponse>;

export const routeOfAdministrationRequestSchema = z.object({
  name: z.string()
    .trim()
    .min(1, "Name is required"),
  abbreviation: z.string()
    .trim()
    .min(1, "Abbreviation is required"),
  description: z.string()
    .trim()
    .nullable(),
  status: z.enum(['active', 'inactive']),
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number")
    .optional(),
});


export type RouteOfAdministrationRequest = z.infer<typeof routeOfAdministrationRequestSchema>;

export interface RouteOfAdministrationFilters {
  id?: number;
  code?: string;
  name?: string;
  abbreviation?: string;
  status?: 'active' | 'inactive';
  organizationId?: number;
  createdAt?: string; // ISO date string
  updatedAt?: string; // ISO date string
  createdBy?: number;
  updatedBy?: number;
}
