"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uI<PERSON>,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateRouteOfAdministration,
  useGetRouteOfAdministration,
  useUpdateRouteOfAdministration,
} from "../query/route-of-administration.query";
import { useRef, useEffect } from "react";
import {
  routeOfAdministrationRequestSchema,
  RouteOfAdministrationRequest,
} from "@/app/inventory/route-of-administration/types/route-of-administration.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function RouteOfAdministrationForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: routeOfAdministrationData, isLoading } = useGetRouteOfAdministration(id);
  const createMutation = useCreateRouteOfAdministration();
  const updateMutation = useUpdateRouteOfAdministration();

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    // watch,
    formState: { errors },
  } = useForm<RouteOfAdministrationRequest>({
    resolver: zodResolver(routeOfAdministrationRequestSchema),
    defaultValues: {
      name: "",
      abbreviation: "",
      description: "",
      status: "active",
      organizationId: undefined,
    },
  });

  // const watchStatus = watch("status");

  useEffect(() => {
  if (nameRef.current) {
    nameRef.current.focus();
  }
}, []);

  useEffect(() => {
    if (isEditMode && routeOfAdministrationData) {
      reset(routeOfAdministrationData);
    }
  }, [isEditMode, routeOfAdministrationData, reset]);

  const onSubmit = (data: RouteOfAdministrationRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate({ id, routeOfAdministrationRequest: data },
        {
          onSuccess: () => {
            // After successful edit, navigate back to the list page
            router.push("/inventory/route-of-administration");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          // After successful create, navigate back to the list page
          reset();
          router.push("/inventory/route-of-administration");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

return (
    <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Route of Administration" : "Create Route of Administration"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Name */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  inputRef={nameRef}
                  label="Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  autoFocus
                  required
                />
              )}
            />
          </Grid>

          {/* Abbreviation */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="abbreviation"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Abbreviation"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.abbreviation}
                  helperText={errors.abbreviation?.message}
                />
              )}
            />
          </Grid>

          {/* Status (only in edit mode) */}
          {isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    {statuses.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
          )}

          {/* Organization (only in create mode) */}
          {!isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="organizationId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                    label="Organization"
                    value={field.value}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
                )}
              />
            </Grid>
          )}

          {/* Description */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Description"
                  size="small"
                  multiline
                  rows={4}
                  fullWidth
                  {...field}
                  error={!!errors.description}
                  helperText={errors.description?.message}
                />
              )}
            />
          </Grid>
        </Grid>

        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            variant="contained" 
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}