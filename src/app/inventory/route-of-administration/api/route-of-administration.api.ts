import apiClient from "@/app/api/api";
import {
  RouteOfAdministrationPaginatedResponse,
  RouteOfAdministrationResponse,
  RouteOfAdministrationSuccessResponse,
  RouteOfAdministrationRequest,
  RouteOfAdministrationFilters
} from "../types/route-of-administration.types.js";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listRouteOfAdministration = async (
  page?: number,
  limit?: number,
  filters?: RouteOfAdministrationFilters
): Promise<RouteOfAdministrationPaginatedResponse> => {
  const res = await apiClient.get<RouteOfAdministrationPaginatedResponse>("/route-of-admins", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};
export const getRouteOfAdministration = async (
  id: number
): Promise<RouteOfAdministrationResponse> => {
  const res = await apiClient.get<RouteOfAdministrationResponse>(`/route-of-admins/${id}`);
  return res.data;
};

export const createRouteOfAdministration = async (
  routeOfAdministrationRequest: RouteOfAdministrationRequest
): Promise<RouteOfAdministrationSuccessResponse> => {
  const res = await apiClient.post<RouteOfAdministrationSuccessResponse>("/route-of-admins", routeOfAdministrationRequest);
  return res.data;
};

export const updateRouteOfAdministration = async (
  id: number,
  routeOfAdministrationRequest: RouteOfAdministrationRequest
): Promise<RouteOfAdministrationSuccessResponse> => {
  const res = await apiClient.patch<RouteOfAdministrationSuccessResponse>(`/route-of-admins/${id}`, routeOfAdministrationRequest);
  return res.data;
};

export const deleteRouteOfAdministration = async (
  id: number
): Promise<RouteOfAdministrationSuccessResponse> => {
  const res = await apiClient.delete<RouteOfAdministrationSuccessResponse>(`/route-of-admins/${id}`);
  return res.data;
};