"use client";

import { useParams } from "next/navigation";
import RouteOfAdministrationForm from "@/app/inventory/route-of-administration/forms/route-of-administration.forms";
import OrganizationPage from "@/app/organization/organization/page";


export default function EditRouteOfAdministrationPage() {
  const params = useParams();
  const id = Number(params?.id);

  return (
    <>
    <RouteOfAdministrationForm id={id} />
    <OrganizationPage />
    </>
  )
}