import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createRouteOfAdministration,
  deleteRouteOfAdministration,
  getRouteOfAdministration,
  listRouteOfAdministration,
  updateRouteOfAdministration,
} from "../api/route-of-administration.api";
import {
  RouteOfAdministrationFilters,
  RouteOfAdministrationPaginatedResponse,
  RouteOfAdministrationRequest,
  RouteOfAdministrationSuccessResponse,
} from "../types/route-of-administration.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListRouteOfAdministration = (
  page?: number,
  limit?: number,
  filters: RouteOfAdministrationFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<RouteOfAdministrationPaginatedResponse, ApiErrorResponse>({
    queryKey: ["routes-of-admins", page, limit, filterKey],
    queryFn: () => listRouteOfAdministration(page, limit, filters),
  });
};
export const useGetRouteOfAdministration = (id: number | undefined) => {
  return useQuery({
    queryKey: ["routes-of-admin", id],
    queryFn: () => getRouteOfAdministration(id!),
    enabled: !!id, // only run if id is defined
  });
};

export const useCreateRouteOfAdministration = () => {
  const queryClient = useQueryClient();
  return useMutation<
    RouteOfAdministrationSuccessResponse,
    ApiErrorResponse,
    RouteOfAdministrationRequest
  >({
    mutationFn: createRouteOfAdministration,
    mutationKey: ["createRouteOfAdministration"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["routes-of-admins"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateRouteOfAdministration = () => {
  const queryClient = useQueryClient();
  return useMutation<
    RouteOfAdministrationSuccessResponse, // The wrapped response from backend
    ApiErrorResponse,
    { id: number; routeOfAdministrationRequest: RouteOfAdministrationRequest }
  >({
    mutationKey: ["updateRouteOfAdministration"],
    mutationFn: ({ id, routeOfAdministrationRequest }) =>
      updateRouteOfAdministration(id, routeOfAdministrationRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["routes-of-admins"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteRouteOfAdministration = () => {
  const queryClient = useQueryClient();
  return useMutation<
    RouteOfAdministrationSuccessResponse, // Actual API response type
    ApiErrorResponse,
    number // Mutation input is just the ID
  >({
    mutationKey: ["deleteRouteOfAdministration"],
    mutationFn: (id) => deleteRouteOfAdministration(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["routes-of-admins"] });
      toast.success(
        data.message || "Route of administration deleted successfully!"
      );
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
