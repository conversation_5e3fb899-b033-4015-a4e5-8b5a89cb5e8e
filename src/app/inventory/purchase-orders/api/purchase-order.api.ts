import apiClient from "@/app/api/api";
import { 
  PurchaseOrderPaginatedResponse, 
  PurchaseOrderResponse, 
  PurchaseOrderSuccessResponse, 
  PurchaseOrderRequest, 
  PurchaseOrderFilters
} from "../types/purchase-orders.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";


export const listPurchaseOrders = async (
  page?: number,
  limit?: number,
  filters?: PurchaseOrderFilters
): Promise<PurchaseOrderPaginatedResponse> => {
  const res = await apiClient.get<PurchaseOrderPaginatedResponse>("/purchase-orders", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });
  return res.data;
};

export const getPurchaseOrder = async (
  id: number
): Promise<PurchaseOrderResponse> => {
  const res = await apiClient.get<PurchaseOrderResponse>(`/purchase-orders/${id}`);
  return res.data;
};

export const createPurchaseOrder = async (
  purchaseOrderRequest: PurchaseOrderRequest
): Promise<PurchaseOrderSuccessResponse> => {
  const res = await apiClient.post<PurchaseOrderSuccessResponse>("/purchase-orders", purchaseOrderRequest);
  return res.data;
};

export const updatePurchaseOrder = async (
  id: number,
  purchaseOrderRequest: PurchaseOrderRequest
): Promise<PurchaseOrderSuccessResponse> => {
  const res = await apiClient.patch<PurchaseOrderSuccessResponse>(`/purchase-orders/${id}`, purchaseOrderRequest);
  return res.data;
};

export const deletePurchaseOrder = async (
  id: number
): Promise<PurchaseOrderSuccessResponse> => {
  const res = await apiClient.delete<PurchaseOrderSuccessResponse>(`/purchase-orders/${id}`);
  return res.data;
};