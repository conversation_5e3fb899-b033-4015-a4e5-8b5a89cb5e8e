import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createPurchaseOrder,
  deletePurchaseOrder,
  getPurchaseOrder,
  listPurchaseOrders,
  updatePurchaseOrder,
} from "../api/purchase-order.api";
import {
  PurchaseOrderFilters,
  PurchaseOrderPaginatedResponse,
  PurchaseOrderRequest,
  PurchaseOrderSuccessResponse,
} from "../types/purchase-orders.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListPurchaseOrders = (
  page?: number,
  limit?: number,
  filters: PurchaseOrderFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<PurchaseOrderPaginatedResponse, ApiErrorResponse>({
    queryKey: ["purchase-orders", page, limit, filterKey],
    queryFn: () => listPurchaseOrders(page, limit, filters),
  });
};
export const useGetPurchaseOrder = (id: number | undefined) => {
  return useQuery({
    queryKey: ["purchase-order", id],
    queryFn: () => getPurchaseOrder(id!),
    enabled: !!id, // only run if id is defined
  });
};

export const useCreatePurchaseOrder = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PurchaseOrderSuccessResponse,
    ApiErrorResponse,
    PurchaseOrderRequest
  >({
    mutationFn: createPurchaseOrder,
    mutationKey: ["createPurchaseOrder"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["purchase-orders"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdatePurchaseOrder = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PurchaseOrderSuccessResponse, // The wrapped response from backend
    ApiErrorResponse,
    { id: number; purchaseOrderRequest: PurchaseOrderRequest }
  >({
    mutationKey: ["updatePurchaseOrder"],
    mutationFn: ({ id, purchaseOrderRequest }) =>
      updatePurchaseOrder(id, purchaseOrderRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["purchase-orders"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeletePurchaseOrder = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PurchaseOrderSuccessResponse, // Actual API response type
    ApiErrorResponse,
    number // Mutation input is just the ID
  >({
    mutationKey: ["deletePurchaseOrder"],
    mutationFn: (id) => deletePurchaseOrder(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["purchase-orders"] });
      toast.success(data.message || "Purchase order deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
