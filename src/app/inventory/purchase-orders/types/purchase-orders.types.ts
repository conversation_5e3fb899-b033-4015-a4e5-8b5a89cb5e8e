
import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from '@/app/common/utils/serialize.utils';

export interface PurchaseOrderResponse extends Record<string, unknown> {
  id: number;
  vendorId: number;
  orderDate: string;
  paymentTerms: string;
  leadTime: number | null;
  targetLocation: string | null;
  status: string;
  organizationId: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
}
export const statusOptions = ['draft', 'submitted', 'approved', 'cancelled', 'received'] as const;
export const statusMap = {
  draft: 'Draft',
  submitted: 'Submitted',
  approved: 'Approved',
  cancelled: 'Cancelled',
  received: 'Received'
} as const;

export function getStatusLabel(status: string) {
  return statusMap[status as keyof typeof statusMap] || status;
}
export const purchaseOrderColumns: MRT_ColumnDef<PurchaseOrderResponse>[] = [
  { accessorKey: 'id', header: 'ID',grow: false, size: 50 },
  { accessorKey: 'vendor.name', header: 'Vendor' },
  {
        accessorKey: "orderDate",
        header: "Order Date",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
  { accessorKey: 'paymentTerms', header: 'Payment Terms' },
  { accessorKey: 'leadTime', header: 'Lead Time (days)' },
  { accessorKey: 'targetLocation', header: 'Target Location' },
 { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value], // Capitalize first letter
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    } 

  }, 

  { accessorKey: 'organization.name', header: 'Organization' },
    {
        accessorKey: "createdAt",
        header: "Created At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
      {
        accessorKey: "updatedAt",
        header: "Updated At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type PurchaseOrderPaginatedResponse = PaginatedResponse<PurchaseOrderResponse>
export type PurchaseOrderSuccessResponse = SuccessResponse<PurchaseOrderResponse>

export const purchaseOrderRequestSchema = z.object({
  vendorId: z.number().int().positive("Vendor ID must be a positive number"),
  orderDate: z.string().min(1, "Order date is required"),
  paymentTerms: z.string().min(1, "Payment terms are required").max(100, "Payment terms cannot exceed 100 characters"),
  leadTime: z.number().nullable().optional(),
  targetLocation: z.string().max(255, "Target location cannot exceed 255 characters").nullable().optional(),
  status: z.string().min(1, "Status is required"),
  organizationId: z.number().int().positive("Organization ID must be a positive number"),
});

export type PurchaseOrderRequest = z.infer<typeof purchaseOrderRequestSchema>;

export interface PurchaseOrderFilters {
  vendorId?: number;
  status?: string;
  orderDate?: string;
  targetLocation?: string;
  organizationId?: number;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
}
