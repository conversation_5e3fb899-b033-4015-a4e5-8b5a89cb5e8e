"use client";

import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DatePicker } from "@mui/x-date-pickers";
import dayjs from 'dayjs';

import {
  Box,
  Button,
  MenuItem,
  TextField,
  Typography,
  Card,
  Grid
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreatePurchaseOrder,
  useGetPurchaseOrder,
  useUpdatePurchaseOrder,
} from "../query/purchase-order.query";
import { useRef, useEffect } from "react";
import {
  purchaseOrderRequestSchema,
  PurchaseOrderRequest,
} from "@/app/inventory/purchase-orders/types/purchase-orders.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from '@/app/common/dropdown/CommonDropdown';
import { OrganizationPaginatedResponse, OrganizationResponse } from '@/app/organization/organization/types/organization.types';
import { useListOrganization } from '@/app/organization/organization/query/organization.query';
import { VendorPaginatedResponse, VendorResponse } from '../../vendors/types/vendor.types';
import { useListVendors } from '../../vendors/query/vendor.query';

type Props = {
  id?: number;
};

const paymentTermsOptions = [
  { label: "Net 30", value: "Net 30" },
  { label: "Net 60", value: "Net 60" },
  { label: "Advance", value: "Advance" },
  { label: "On Delivery", value: "On Delivery" },
];

const statuses = [
  { label: "Draft", value: "draft" },
  { label: "Submitted", value: "submitted" },
  { label: "Approved", value: "approved" },
  { label: "Cancelled", value: "cancelled" },
  { label: "Received", value: "received" },
];

export default function PurchaseOrderForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: purchaseOrderData, isLoading } = useGetPurchaseOrder(id);
  const createMutation = useCreatePurchaseOrder();
  const updateMutation = useUpdatePurchaseOrder();

  const vendorRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<PurchaseOrderRequest>({
    resolver: zodResolver(purchaseOrderRequestSchema),
    defaultValues: {
      vendorId: undefined,
      orderDate: new Date().toISOString().split('T')[0],
      paymentTerms: "Net 30",
      leadTime: null,
      targetLocation: null,
      status: "draft",
      organizationId: undefined,
    },
  });

  useEffect(() => {
    if (vendorRef.current) {
      vendorRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isEditMode && purchaseOrderData) {
      reset({
        vendorId: purchaseOrderData.vendorId,
        orderDate: purchaseOrderData.orderDate,
        paymentTerms: purchaseOrderData.paymentTerms,
        leadTime: purchaseOrderData.leadTime,
        targetLocation: purchaseOrderData.targetLocation,
        status: purchaseOrderData.status,
        organizationId: purchaseOrderData.organizationId,
      });
    }
  }, [isEditMode, purchaseOrderData, reset]);

  const onSubmit = (data: PurchaseOrderRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate({ id, purchaseOrderRequest: data },
        {
          onSuccess: () => {
            // After successful edit, navigate back to the list page
            router.push("/inventory/purchase-orders");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          // After successful create, navigate back to the list page
          reset();
          router.push("/inventory/purchase-orders");
        },
      });
    }
  };
  
  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
        <Typography variant="h6" mb={3}>
          {isEditMode ? "Edit Purchase Order" : "Create Purchase Order"}
        </Typography>

        <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
          <Grid container spacing={2}>
            {/* Vendor Field */}
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="vendorId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<VendorResponse, VendorPaginatedResponse, number>
                    label="Vendor"
                    value={field.value}
                    onChange={field.onChange}
                    useDataQuery={useListVendors}
                    inputRef={vendorRef}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.vendorId}
                    helperText={errors.vendorId?.message}
                  />
                )}
              />
            </Grid>

            {/* Order Date */}
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="orderDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    label="Order Date"
                    value={field.value ? dayjs(field.value) : null}
                    onChange={(date) =>
                      field.onChange(date?.toISOString().split('T')[0] ?? null)
                    }
                    slotProps={{
                      textField: {
                        size: "small",
                        fullWidth: true,
                        error: !!errors.orderDate,
                        helperText: errors.orderDate?.message,
                        required: true,
                      },
                    }}
                  />
                )}
              />
            </Grid>

            {/* Payment Terms */}
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="paymentTerms"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Payment Terms"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.paymentTerms}
                    helperText={errors.paymentTerms?.message}
                    required
                  >
                    {paymentTermsOptions.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>

            {/* Lead Time */}
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="leadTime"
                control={control}
                render={({ field }) => (
                  <TextField
                    label="Lead Time (days)"
                    type="number"
                    size="small"
                    fullWidth
                    {...field}
                    value={field.value || ""}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                    error={!!errors.leadTime}
                    helperText={errors.leadTime?.message}
                  />
                )}
              />
            </Grid>

            {/* Status */}
            {isEditMode && (
              <Grid size={{ xs: 12, md: 6, lg: 4 }}>
                <Controller
                  name="status"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      select
                      label="Status"
                      size="small"
                      fullWidth
                      value={field.value || "draft"}
                      onChange={field.onChange}
                      error={!!errors.status}
                      helperText={errors.status?.message}
                    >
                      {statuses.map((option) => (
                        <MenuItem key={option.value} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </TextField>
                  )}
                />
              </Grid>
            )}

            {/* Organization */}
            {!isEditMode && (
              <Grid size={{ xs: 12, md: 6, lg: 4 }}>
                <Controller
                  name="organizationId"
                  control={control}
                  render={({ field }) => (
                    <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                      label="Organization"
                      value={field.value}
                      onChange={field.onChange}
                      useDataQuery={useListOrganization}
                      labelKey="name"
                      valueKey="id"
                      searchable
                      searchKey="name"
                      error={!!errors.organizationId}
                      helperText={errors.organizationId?.message}
                    />
                  )}
                />
              </Grid>
            )}

            {/* Target Location */}
            <Grid size={{ xs: 12 }}>
              <Controller
                name="targetLocation"
                control={control}
                render={({ field }) => (
                  <TextField
                    label="Target Location"
                    size="small"
                    fullWidth
                    multiline
                    rows={3}
                    {...field}
                    value={field.value || ""}
                    error={!!errors.targetLocation}
                    helperText={errors.targetLocation?.message}
                  />
                )}
              />
            </Grid>
          </Grid>

          {/* Action Buttons */}
          <Box
            sx={{
              display: "flex",
              gap: 2,
              justifyContent: "flex-end",
              mt: 3,
            }}
          >
            <Button
              variant="outlined"
              color="inherit"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={createMutation.isPending || updateMutation.isPending}
            >
              {isEditMode ? "Update" : "Create"}
            </Button>
          </Box>
        </Box>
      </Card>
    </LocalizationProvider>
  );
}