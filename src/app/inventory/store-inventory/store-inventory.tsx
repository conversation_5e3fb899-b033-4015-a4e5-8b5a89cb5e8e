'use client';

import { storeInventoryColumns, StoreInventoryProps, StoreInventoryResponse } from "./types/store-inventory.types";
import { useListStoreInventory } from "./query/store-inventory.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteStoreInventory } from "./query/store-inventory.query";
import { Toaster } from "react-hot-toast";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";

function StoreInventory({ storeId }: StoreInventoryProps) {
  const router = useRouter();
  const deleteMutation = useDeleteStoreInventory();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<StoreInventoryResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "Store Inventory",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev: RolePermissionFilters) => ({
      ...prev,
      roleId,
    }));
  }, []);

  const handleOpenDelete = (row: StoreInventoryResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<StoreInventoryResponse>
        title="Store Inventory"
        columns={storeInventoryColumns}
        useDataQuery={useListStoreInventory}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/store-inventory/create")}
        onExport={() => console.log("Export Store Inventory")}
        onEdit={(row) => router.push(`/inventory/store-inventory/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data?.[0]?.create}
        isEdit={rolePermissionData?.data?.[0]?.update}
        isDelete={rolePermissionData?.data?.[0]?.delete}     
        initialFilters={[{ id: "storeId", value: storeId }]} 
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete store inventory item with ID{" "}
            <strong>{selectedRow?.id}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
      <Toaster position="top-right" />
    </>
  );
}

export default StoreInventory;