import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createStoreInventory,
  deleteStoreInventory,
  getStoreInventory,
  listStoreInventory,
  updateStoreInventory,
} from "../api/store-inventory.api";
import { toast } from "react-hot-toast";
import {
  SuccessResponse,
} from "@/app/common/types/common.types";
import {
  StoreInventoryPaginatedResponse,
  StoreInventoryRequest,
  StoreInventoryResponse,
  StoreInventoryFilters,
} from "@/app/inventory/store-inventory/types/store-inventory.types";
import { AxiosError } from "axios";
import { ApiError } from "next/dist/server/api-utils";

export const useListStoreInventory = (
  page?: number,
  limit?: number,
  filters: StoreInventoryFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery<StoreInventoryPaginatedResponse, AxiosError<ApiError>>({
    queryKey: ["store-inventories", page, limit, filterKey],
    queryFn: () => listStoreInventory(page, limit, filters),
  });
};

export const useGetStoreInventory = (id: number | undefined) => {
  return useQuery<StoreInventoryResponse, AxiosError<ApiError>>({
    queryKey: ["store-inventory", id],
    queryFn: () => getStoreInventory(id!),
    enabled: !!id,
  });
};

export const useCreateStoreInventory = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<StoreInventoryResponse>,
    AxiosError<ApiError>,
    StoreInventoryRequest
  >({
    mutationKey: ["createStoreInventory"],
    mutationFn: (storeInventoryRequest) => createStoreInventory(storeInventoryRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["store-inventories"] });
      toast.success(response.message || "Store inventory created successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create store inventory");
    },
  });
};

export const useUpdateStoreInventory = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<StoreInventoryResponse>,
    AxiosError<ApiError>,
    { id: number; storeInventoryRequest: StoreInventoryRequest }
  >({
    mutationKey: ["updateStoreInventory"],
    mutationFn: ({ id, storeInventoryRequest }) =>
      updateStoreInventory(id, storeInventoryRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["store-inventories"] });
      toast.success(response.message || "Store inventory updated successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update store inventory");
    },
  });
};

export const useDeleteStoreInventory = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<StoreInventoryResponse>,
    AxiosError<ApiError>,
    number
  >({
    mutationKey: ["deleteStoreInventory"],
    mutationFn: (id) => deleteStoreInventory(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["store-inventories"] });
      toast.success(response.message || "Store inventory deleted successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete store inventory");
    },
  });
};