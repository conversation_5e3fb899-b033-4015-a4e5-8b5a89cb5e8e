"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uI<PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  Card,
  Grid
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateStoreInventory,
  useGetStoreInventory,
  useUpdateStoreInventory,
} from "../query/store-inventory.query";
import { useEffect } from "react";
import {
  storeInventoryRequestSchema,
  StoreInventoryRequest,
} from "@/app/inventory/store-inventory/types/store-inventory.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListStores } from "@/app/inventory/stores/query/stores.query";
import { StorePaginatedResponse, StoreResponse } from "@/app/inventory/stores/types/stores.types";
import { ItemBatchPaginatedResponse, ItemBatchResponse } from "@/app/inventory/item-batch/types/item-batch.types";
import { useListItemBatches } from "../../item-batch/query/item-batch.query";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function StoreInventoryForm({ id }: Props) {
  const isEditMode = !!id;
  const router = useRouter();
  const { data: storeInventoryData, isLoading } = useGetStoreInventory(id);
  const createMutation = useCreateStoreInventory();
  const updateMutation = useUpdateStoreInventory();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<StoreInventoryRequest>({
    resolver: zodResolver(storeInventoryRequestSchema),
    defaultValues: {
      storeId: 0,
      itemBatchNumberId: 0,
      quantity: 0,
      status: "active",
      organizationId: undefined,
    },
  });

  useEffect(() => {
    if (isEditMode && storeInventoryData) {
      reset(storeInventoryData);
    }
  }, [isEditMode, storeInventoryData, reset]);

  const onSubmit = (formData: StoreInventoryRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, storeInventoryRequest: formData },
        {
          onSuccess: () => {
            router.push("/inventory/store-inventory");
          },
        }
      );
    } else {
      createMutation.mutate(formData, {
        onSuccess: () => {
          reset();
          router.push("/inventory/store-inventory");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Store Inventory" : "Create Store Inventory"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Store */}
          <Grid  size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="storeId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<
                  StoreResponse,
                  StorePaginatedResponse,
                  number
                >
                  label="Store"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListStores}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.storeId}
                  helperText={errors.storeId?.message}
                />
              )}
            />
          </Grid>

          {/* Batch Number */}
          <Grid  size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
              name="itemBatchNumberId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<ItemBatchResponse,ItemBatchPaginatedResponse,number>
                  label="Batch Number"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListItemBatches}
                  labelKey="batchNumber"
                  valueKey="id"
                  searchable
                  searchKey="batchNumber"
                  error={!!errors.itemBatchNumberId}
                  helperText={errors.itemBatchNumberId?.message}
                />

              )}
            />
          </Grid>

          {/* Quantity */}
          <Grid  size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="quantity"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Quantity"
                  type="number"
                  size="small"
                  fullWidth
                  onChange={(e) => field.onChange(Number(e.target.value))}
                  error={!!errors.quantity}
                  helperText={errors.quantity?.message}
                />
              )}
            />
          </Grid>

          {/* Status */}
          {isEditMode && (
            <Grid  size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    {statuses.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
          )}

          {/* Organization */}
          {!isEditMode && (
            <Grid  size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="organizationId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<
                    OrganizationResponse,
                    OrganizationPaginatedResponse,
                    number
                  >
                    label="Organization"
                    value={field.value}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
                )}
              />
            </Grid>
          )}
        </Grid>

        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}