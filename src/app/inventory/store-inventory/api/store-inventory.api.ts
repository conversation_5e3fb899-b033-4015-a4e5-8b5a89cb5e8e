import apiClient from "@/app/api/api";
import { 
  StoreInventoryPaginatedResponse, 
  StoreInventoryResponse, 
  StoreInventorySuccessResponse, 
  StoreInventoryRequest,
  StoreInventoryFilters 
} from "../types/store-inventory.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listStoreInventory = async (
  page?: number,
  limit?: number,
  filters?: StoreInventoryFilters
): Promise<StoreInventoryPaginatedResponse> => {
  const res = await apiClient.get<StoreInventoryPaginatedResponse>("/store-inventories", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string,unknown>)
    },
  });
  return res.data;
};

export const getStoreInventory = async (
  id: number
): Promise<StoreInventoryResponse> => {
  const res = await apiClient.get<StoreInventoryResponse>(`/store-inventories/${id}`);
  return res.data;
}

export const createStoreInventory = async (
  storeInventoryRequest: StoreInventoryRequest
): Promise<StoreInventorySuccessResponse> => {
  const res = await apiClient.post<StoreInventorySuccessResponse>("/store-inventories", storeInventoryRequest);
  return res.data;
};

export const updateStoreInventory = async (
  id: number,
  storeInventoryRequest: StoreInventoryRequest
): Promise<StoreInventorySuccessResponse> => {
  const res = await apiClient.patch<StoreInventorySuccessResponse>(`/store-inventories/${id}`, storeInventoryRequest);
  return res.data;
};

export const deleteStoreInventory = async (
  id: number
): Promise<StoreInventorySuccessResponse> => {
  const res = await apiClient.delete<StoreInventorySuccessResponse>(`/store-inventories/${id}`);
  return res.data;
};