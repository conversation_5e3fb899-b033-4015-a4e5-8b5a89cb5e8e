import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
import { SuccessResponse, PaginatedResponse, BaseResponse } from "@/app/common/types/common.types";
import { localTime } from '@/app/common/utils/serialize.utils';

export interface StoreInventoryProps {
  storeId?: number;
}

export interface StoreInventoryResponse extends BaseResponse, Record<string, unknown> {
  storeId: number;
  itemBatchNumberId: number;
  quantity: number;
  status: 'active' | 'inactive';
  organizationId: number;
}

export const storeInventoryColumns: MRT_ColumnDef<StoreInventoryResponse>[] = [
  { accessorKey: 'id', header: 'ID', grow: false, size: 50 },
  { accessorKey: 'store.name', header: 'Store' },
  { accessorKey: 'itemBatchNumber.name', header: 'Batch Number' },
  { accessorKey: 'quantity', header: 'Quantity' },
  { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    }
  },
  {
        accessorKey: "createdAt",
        header: "Created At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
      {
        accessorKey: "updatedAt",
        header: "Updated At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
  { accessorKey: 'createdBy', header: 'Created By'},
  { accessorKey: 'updatedBy', header: 'Updated By'},
  { accessorKey: 'organization.name', header: 'Organization'},
];

export type StoreInventoryPaginatedResponse = PaginatedResponse<StoreInventoryResponse>;
export type StoreInventorySuccessResponse = SuccessResponse<StoreInventoryResponse>;

export const storeInventoryRequestSchema = z.object({
  storeId: z
    .number()
    .int()
    .positive("Store ID must be a positive number"),
    
  itemBatchNumberId: z
    .number()
    .int()
    .positive("Batch Number ID must be a positive number"),

  quantity: z
    .number()
    .int()
    .nonnegative("Quantity must be a non-negative integer"),

  status: z.enum(['active', 'inactive']),

  organizationId: z
    .number()
    .int()
    .positive("Organization ID must be a positive number"),
});

export type StoreInventoryRequest = z.infer<typeof storeInventoryRequestSchema>;

export interface StoreInventoryFilters {
  storeId?: number;
  itemBatchNumberId?: number;
  quantity?: number;
  status?: 'active' | 'inactive';
  organizationId?: number;
  createdBy?: number;
  updatedBy?: number;
  createdAt?: string;
  updatedAt?: string;
}