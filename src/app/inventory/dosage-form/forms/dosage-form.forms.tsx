"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CircularProgress,
  Grid,
  MenuItem,
  TextField,
  Typography,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  DosageFormRequest,
  dosageFormRequestSchema,
} from "../types/dosage-form.types";
import {
  useCreateDosageForm,
  useGetDosageForm,
  useUpdateDosageForm,
} from "../query/dosage-form.query";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import {
  OrganizationPaginatedResponse,
  OrganizationResponse,
} from "@/app/organization/organization/types/organization.types";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function DosageForm({ id }: Props) {
  const isEditMode = !!id;
  const router = useRouter();
  const { data: dosageData, isLoading } = useGetDosageForm(id);
  const createMutation = useCreateDosageForm();
  const updateMutation = useUpdateDosageForm();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<DosageFormRequest>({
    resolver: zodResolver(dosageFormRequestSchema),
    defaultValues: {
      dosageFormHimsCode: "",
      dosageFormName: "",
      dosageFormNameDisplay: "",
      dosageFormAbbreviation: "",
      dosageFormDescription: "",
      dosageFormSnomedCode: "",
      dosageFormCdcCode: "",
      dosageFormStatus: "active",
      organizationId: undefined,
    },
  });

  useEffect(() => {
    if (isEditMode && dosageData) {
      reset({
        ...dosageData,
        organizationId:
          dosageData.organizationId === null
            ? undefined
            : dosageData.organizationId,
      });
    }
  }, [isEditMode, dosageData, reset]);

  const onSubmit = (data: DosageFormRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, dosageFormRequest: data },
        {
          onSuccess: () => {
            router.push("/inventory/dosage-form");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          router.push("/inventory/dosage-form");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Dosage Form" : "Create Dosage Form"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* HIMS Code */}
          {/* <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="dosageFormHimsCode"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="HIMS Code"
                  size="small"
                  fullWidth
                  error={!!errors.dosageFormHimsCode}
                  helperText={errors.dosageFormHimsCode?.message}
                />
              )}
            />
          </Grid> */}
          {/* Name */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="dosageFormName"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Name"
                  size="small"
                  fullWidth
                  required
                  error={!!errors.dosageFormName}
                  helperText={errors.dosageFormName?.message}
                />
              )}
            />
          </Grid>
          {/* Display Name */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="dosageFormNameDisplay"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Display Name"
                  size="small"
                  fullWidth
                  error={!!errors.dosageFormNameDisplay}
                  helperText={errors.dosageFormNameDisplay?.message}
                />
              )}
            />
          </Grid>
          {/* Abbreviation */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="dosageFormAbbreviation"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Abbreviation"
                  size="small"
                  fullWidth
                  error={!!errors.dosageFormAbbreviation}
                  helperText={errors.dosageFormAbbreviation?.message}
                />
              )}
            />
          </Grid>
          {/* Description */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="dosageFormDescription"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Description"
                  size="small"
                  fullWidth
                  error={!!errors.dosageFormDescription}
                  helperText={errors.dosageFormDescription?.message}
                />
              )}
            />
          </Grid>
          {/* SNOMED Code */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="dosageFormSnomedCode"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="SNOMED Code"
                  size="small"
                  fullWidth
                  error={!!errors.dosageFormSnomedCode}
                  helperText={errors.dosageFormSnomedCode?.message}
                />
              )}
            />
          </Grid>
          {/* CDC Code */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="dosageFormCdcCode"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="CDC Code"
                  size="small"
                  fullWidth
                  error={!!errors.dosageFormCdcCode}
                  helperText={errors.dosageFormCdcCode?.message}
                />
              )}
            />
          </Grid>
          {/* Status */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="dosageFormStatus"
              control={control}
              render={({ field }) => (
                <TextField
                  select
                  label="Status"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.dosageFormStatus}
                  helperText={errors.dosageFormStatus?.message}
                >
                  {statuses.map((opt) => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
          {/* Organization */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="organizationId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<
                  OrganizationResponse,
                  OrganizationPaginatedResponse,
                  number
                >
                  label="Organization"
                  value={field.value === null ? undefined : field.value}
                  onChange={field.onChange}
                  useDataQuery={useListOrganization}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.organizationId}
                  helperText={errors.organizationId?.message}
                />
              )}
            />
          </Grid>
        </Grid>

        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
            startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}