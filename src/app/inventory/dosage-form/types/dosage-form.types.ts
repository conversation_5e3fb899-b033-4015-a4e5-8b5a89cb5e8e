import { MRT_ColumnDef } from "material-react-table";
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from "@/app/common/types/status.types";
import { SuccessResponse, PaginatedResponse, BaseResponse } from "@/app/common/types/common.types";
import { localTime } from "@/app/common/utils/serialize.utils";

export interface DosageFormResponse extends BaseResponse, Record<string, unknown> {
  dosageFormId: number;
  dosageFormHimsCode: string;
  dosageFormName: string;
  dosageFormNameDisplay: string;
  dosageFormAbbreviation: string;
  dosageFormDescription: string;
  dosageFormSnomedCode: string;
  dosageFormCdcCode: string;
  dosageFormStatus: "active" | "inactive";
  createdAt: string;
  createdBy: number;
  updatedAt: string;
  updatedBy: number;
  organizationId?: number | null;
}

export const dosageFormColumns: MRT_ColumnDef<DosageFormResponse>[] = [
  { accessorKey: "dosageFormId", header: "ID", grow: false, size: 50 },
  { accessorKey: "dosageFormHimsCode", header: "HIMS Code" },
  { accessorKey: "dosageFormName", header: "Name" },
  { accessorKey: "dosageFormNameDisplay", header: "Display Name" },
  { accessorKey: "dosageFormAbbreviation", header: "Abbreviation" },
  { accessorKey: "dosageFormDescription", header: "Description" },
  { accessorKey: "dosageFormSnomedCode", header: "SNOMED Code" },
  { accessorKey: "dosageFormCdcCode", header: "CDC Code" },
  {
    accessorKey: "dosageFormStatus",
    header: "Status",
    filterVariant: "select",
    filterSelectOptions: statusOptions.map((value) => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    }
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: "createdBy", header: "Created By" },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: "updatedBy", header: "Updated By" },
  { accessorKey: "organization.name", header: "Organization" },
];

export type DosageFormPaginatedResponse = PaginatedResponse<DosageFormResponse>;
export type DosageFormSuccessResponse = SuccessResponse<DosageFormResponse>;

export const dosageFormRequestSchema = z.object({
  dosageFormHimsCode: z.string()
    .max(50, "HIMS Code must be less than 50 characters")
    .transform(val => val.trim()),
  dosageFormName: z.string()
    .min(1, "Name is required")
    .max(100, "Name must be less than 100 characters")
    .transform(val => val.trim()),
  dosageFormNameDisplay: z.string()
    .max(100, "Display Name must be less than 100 characters")
    .transform(val => val.trim()),
  dosageFormAbbreviation: z.string()
    .max(20, "Abbreviation must be less than 20 characters")
    .transform(val => val.trim()),
  dosageFormDescription: z.string()
    .max(255, "Description must be less than 255 characters")
    .nullable()
    .transform(val => val?.trim() || null),
  dosageFormSnomedCode: z.string()
    .max(50, "SNOMED Code must be less than 50 characters")
    .transform(val => val.trim()),
  dosageFormCdcCode: z.string()
    .max(50, "CDC Code must be less than 50 characters")
    .transform(val => val.trim()),
  dosageFormStatus: z.enum(["active", "inactive"]),
  createdAt: z.string().optional(),
  createdBy: z.number().optional(),
  updatedAt: z.string().optional(),
  updatedBy: z.number().optional(),
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number")
    .nullable()
    .optional(),
});

export type DosageFormRequest = z.infer<typeof dosageFormRequestSchema>;

export interface DosageFormFilters {
  dosageFormId?: number;
  dosageFormHimsCode?: string;
  dosageFormName?: string;
  dosageFormNameDisplay?: string;
  dosageFormAbbreviation?: string;
  dosageFormDescription?: string;
  dosageFormSnomedCode?: string;
  dosageFormCdcCode?: string;
  dosageFormStatus?: "active" | "inactive";
  createdAt?: string;
  createdBy?: number;
  updatedAt?: string;
  updatedBy?: number;
}