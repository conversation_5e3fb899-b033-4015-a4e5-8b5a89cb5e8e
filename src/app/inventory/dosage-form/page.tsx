"use client";

import { dosageFormColumns, DosageFormResponse } from "./types/dosage-form.types";
import { useListDosageForm } from "./query/dosage-form.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteDosageForm } from "./query/dosage-form.query";

import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";

function DosageFormPage() {
  const router = useRouter();
  const deleteMutation = useDeleteDosageForm();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<DosageFormResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] = useState<RolePermissionFilters>({
    moduleName: "Inventory",
    featureName: "DosageForm",
    roleId: 0,
  });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);

  // Open modal and set row to delete
  const handleOpenDelete = (row: DosageFormResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  // Close modal and clear selected row
  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.dosageFormId) {
      deleteMutation.mutate(selectedRow.dosageFormId, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<DosageFormResponse>
        title="Dosage Forms"
        columns={dosageFormColumns}
        useDataQuery={useListDosageForm}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/dosage-form/create")}
        onExport={() => console.log("Export Dosage Form")}
        onEdit={(row) => router.push(`/inventory/dosage-form/edit/${row.dosageFormId}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete dosage form{" "}
            <strong>{selectedRow?.dosageFormName}</strong>? This action cannot be
            undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default DosageFormPage;