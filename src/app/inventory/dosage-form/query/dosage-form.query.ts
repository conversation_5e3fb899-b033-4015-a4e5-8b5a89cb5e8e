import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createDosageForm,
  deleteDosageForm,
  getDosageForm,
  listDosageForm,
  updateDosageForm,
} from "../api/dosage-form.api";
import {
  DosageFormFilters,
  DosageFormPaginatedResponse,
  DosageFormRequest,
  DosageFormResponse,
  DosageFormSuccessResponse,
} from "../types/dosage-form.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListDosageForm = (
  page?: number,
  limit?: number,
  filters: DosageFormFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<DosageFormPaginatedResponse, ApiErrorResponse>({
    queryKey: ["dosageForms", page, limit, filterKey],
    queryFn: () => listDosageForm(page, limit, filters),
  });
};

export const useGetDosageForm = (id: number | undefined) => {
  return useQuery<DosageFormResponse, ApiErrorResponse>({
    queryKey: ["dosageForm", id],
    queryFn: () => getDosageForm(id!),
    enabled: !!id,
  });
};

export const useCreateDosageForm = () => {
  const queryClient = useQueryClient();
  return useMutation<
    DosageFormSuccessResponse,
    ApiErrorResponse,
    DosageFormRequest
  >({
    mutationKey: ["createDosageForm"],
    mutationFn: (dosageFormRequest: DosageFormRequest) =>
      createDosageForm(dosageFormRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["dosageForms"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create dosage form");
    },
  });
};

export const useUpdateDosageForm = () => {
  const queryClient = useQueryClient();
  return useMutation<
    DosageFormSuccessResponse,
    ApiErrorResponse,
    { id: number; dosageFormRequest: DosageFormRequest }
  >({
    mutationKey: ["updateDosageForm"],
    mutationFn: ({
      id,
      dosageFormRequest,
    }: {
      id: number;
      dosageFormRequest: DosageFormRequest;
    }) => updateDosageForm(id, dosageFormRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["dosageForms"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update dosage form");
    },
  });
};

export const useDeleteDosageForm = () => {
  const queryClient = useQueryClient();
  return useMutation<DosageFormSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteDosageForm"],
    mutationFn: (id: number) => deleteDosageForm(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["dosageForms"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete dosage form");
    },
  });
};