import apiClient from "@/app/api/api";
import {
  DosageFormPaginatedResponse,
  DosageFormResponse,
  DosageFormSuccessResponse,
  DosageFormRequest,
  DosageFormFilters,
} from "../types/dosage-form.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listDosageForm = async (
  page?: number,
  limit?: number,
  filters?: DosageFormFilters
): Promise<DosageFormPaginatedResponse> => {
  const res = await apiClient.get<DosageFormPaginatedResponse>("/dosage-forms", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};

export const getDosageForm = async (
  id: number
): Promise<DosageFormResponse> => {
  const res = await apiClient.get<DosageFormResponse>(`/dosage-forms/${id}`);
  return res.data;
};

export const createDosageForm = async (
  dosageFormRequest: DosageFormRequest
): Promise<DosageFormSuccessResponse> => {
  const res = await apiClient.post<DosageFormSuccessResponse>("/dosage-forms", dosageFormRequest);
  return res.data;
};

export const updateDosageForm = async (
  id: number,
  dosageFormRequest: DosageFormRequest
): Promise<DosageFormSuccessResponse> => {
  const res = await apiClient.patch<DosageFormSuccessResponse>(`/dosage-forms/${id}`, dosageFormRequest);
  return res.data;
};

export const deleteDosageForm = async (
  id: number
): Promise<DosageFormSuccessResponse> => {
  const res = await apiClient.delete<DosageFormSuccessResponse>(`/dosage-forms/${id}`);
  return res.data;
};