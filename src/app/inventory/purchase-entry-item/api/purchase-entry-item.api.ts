import apiClient from "@/app/api/api";
import {
  PurchaseEntryItemFilters,
  PurchaseEntryItemPaginatedResponse,
  PurchaseEntryItemResponse,
  PurchaseEntryItemSuccessResponse,
  PurchaseEntryItemRequest,
} from "../types/purchase-entry-item.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listPurchaseEntryItems = async (
  page?: number,
  limit?: number,
  filters?: PurchaseEntryItemFilters
): Promise<PurchaseEntryItemPaginatedResponse> => {
  const res = await apiClient.get<PurchaseEntryItemPaginatedResponse>(
    "/purchase-entry-items",
    {
      params: {
        ...(page !== undefined && { page }),
        ...(limit !== undefined && { limit }),
        ...serializeFilters(filters as Record<string, unknown>),
      },
    }
  );
  return res.data;
};


export const getPurchaseEntryItem = async (
  id: number
): Promise<PurchaseEntryItemResponse> => {
  const res = await apiClient.get<PurchaseEntryItemResponse>(
    `/purchase-entry-items/${id}`
  );
  return res.data;
};

export const createPurchaseEntryItem = async (
  purchaseEntryItemRequest: PurchaseEntryItemRequest
): Promise<PurchaseEntryItemSuccessResponse> => {
  const res = await apiClient.post<PurchaseEntryItemSuccessResponse>(
    "/purchase-entry-items",
    purchaseEntryItemRequest
  );
  return res.data;
};

export const updatePurchaseEntryItem = async (
  id: number,
  purchaseEntryItemRequest: PurchaseEntryItemRequest
): Promise<PurchaseEntryItemSuccessResponse> => {
  const res = await apiClient.patch<PurchaseEntryItemSuccessResponse>(
    `/purchase-entry-items/${id}`,
    purchaseEntryItemRequest
  );
  return res.data;
};

export const deletePurchaseEntryItem = async (
  id: number
): Promise<PurchaseEntryItemSuccessResponse> => {
  const res = await apiClient.delete<PurchaseEntryItemSuccessResponse>(
    `/purchase-entry-items/${id}`
  );
  return res.data;
};
