"use client";

import { PurchaseEntryItemResponse } from "@/app/inventory/purchase-entry-item/types/purchase-entry-item.types";
import { CommonAutoComplete } from "@/app/common/dropdown/CommonAutoComplete";
import { InputNumber } from "antd";
import { MRT_ColumnDef } from "material-react-table";
import { DynamicColumn } from "@/app/common/types/common.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { useCreatePurchaseEntryItem } from "../query/purchase-entry-item.query";
import { useListUom } from "../../uom/query/uom.query";
import { UomResponse } from "../../uom/types/uom.types";
import { useListItems } from "../../items/query/items.query";
import { ItemResponse } from "../../items/types/items.types";
import { statusOptions, statusMap, getStatusLabel } from "@/app/common/types/status.types";
import { OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { useListOfferTerms } from "../../offer-terms/query/offer-terms.query";
import { useListItemBatches } from "../../item-batch/query/item-batch.query";
import { ItemBatchResponse } from "../../item-batch/types/item-batch.types";
import { OfferTermsResponse } from "../../offer-terms/types/offer-terms.types";

export const PurchaseEntryItemColumns = (
  rows: PurchaseEntryItemResponse[] = [],
  setRows: React.Dispatch<React.SetStateAction<PurchaseEntryItemResponse[]>> = () => {},
  createMutation: ReturnType<typeof useCreatePurchaseEntryItem>,
  editableRowIndex: number | null
): DynamicColumn[] => {
  const handleChange = (
    index: number,
    field: keyof PurchaseEntryItemResponse,
    value: unknown
  ) => {
    if (rows) {
      const updated = [...rows];
      updated[index][field] = value;
      setRows(updated);
    }
  };

  function addRows() {
    const purchaseEntryItem: PurchaseEntryItemResponse = {
      id: 0,
      purchaseEntryId: 0,
      itemId: 0,
      itemBatchNumberId: 0,
      itemUomId: 0,
      offerTermId: 0,
      purchaseQty: 0,
      freeQty: null,
      purchaseRate: 0,
      taxId: null,
      discount: null,
      discountAmount: null,
      mrp: null,
      totalAmount: 0,
      ucp: null,
      status: "active",
      organizationId: 0,
      createdAt: "",
      updatedAt: "",
      createdBy: 0,
      updatedBy: 0,
    };
    setRows([...rows, purchaseEntryItem]);
  }

  return [
    { accessorKey: "id", header: "ID", isEditable: false },

    {
      accessorKey: "purchaseEntryId",
      header: "Purchase Entry",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.purchaseEntryId}</span>;

        return (
          <InputNumber
            min={0}
            value={row.purchaseEntryId}
            onChange={(val) => handleChange(rowIndex, "purchaseEntryId", val)}
          />
        );
      },
    },

    {
      accessorKey: "item.name",
      header: "Item",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.item?.name || ""}</span>;

        return (
          <CommonAutoComplete<ItemResponse, number>
            value={row.itemId}
            onChange={(val) => handleChange(rowIndex, "itemId", val || 0)}
            useDataQuery={useListItems}
            labelKey="name"
            valueKey="id"
          />
        );
      },
    },

    {
      accessorKey: "itemBatchNumber.batchNumber",
      header: "Batch Number",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{(row.itemBatchNumber as { name?: string })?.name}</span>;

        return (
          <CommonAutoComplete<ItemBatchResponse, number>
            value={row.itemBatchNumberId}
            onChange={(val) => handleChange(rowIndex, "itemBatchNumberId", val || 0)}
            useDataQuery={useListItemBatches }
            labelKey="batchNumber"
            valueKey="id"
          />
        );
      },
    },

    {
      accessorKey: "itemUom.name",
      header: "UOM",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.itemUom?.name || ""}</span>;

        return (
          <CommonAutoComplete<UomResponse, number>
            value={row.itemUomId}
            onChange={(val) => handleChange(rowIndex, "itemUomId", val || 0)}
            useDataQuery={useListUom}
            labelKey="name"
            valueKey="id"
          />
        );
      },
    },

    {
      accessorKey: "offerTerm.name",
      header: "Offer Term",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{(row.offerTerm as { name?: string })?.name}</span>;

        return (
          <CommonAutoComplete<OfferTermsResponse, number>
            value={row.offerTermId}
            onChange={(val) => handleChange(rowIndex, "offerTermId", val || 0)}
            useDataQuery={useListOfferTerms}
            labelKey="name"
            valueKey="id"
          />
        );
      },
    },

    {
      accessorKey: "purchaseQty",
      header: "Purchase Qty",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.purchaseQty}</span>;

        return (
          <InputNumber
            min={0}
            value={row.purchaseQty}
            onChange={(val) => handleChange(rowIndex, "purchaseQty", val)}
          />
        );
      },
    },

    {
      accessorKey: "freeQty",
      header: "Free Qty",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.freeQty || "-"}</span>;

        return (
          <InputNumber
            min={0}
            value={row.freeQty || undefined}
            onChange={(val) => handleChange(rowIndex, "freeQty", val || null)}
          />
        );
      },
    },

    {
      accessorKey: "purchaseRate",
      header: "Purchase Rate",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.purchaseRate}</span>;

        return (
          <InputNumber
            min={0}
            value={row.purchaseRate}
            onChange={(val) => handleChange(rowIndex, "purchaseRate", val)}
          />
        );
      },
    },

    {
  accessorKey: "tax.name",
  header: "Tax",
  Edit: (rowIndex: number) => {
    const row = rows[rowIndex];
    if (row.id !== 0 && editableRowIndex !== rowIndex)
      return <span>{row.taxId || row.taxId || "-"}</span>;

    return (
      <InputNumber
        min={0}
        value={row.taxId || undefined}
        onChange={(val) => handleChange(rowIndex, "taxId", val || null)}
      />
    );
  },
},

    {
      accessorKey: "discount",
      header: "Discount (%)",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.discount || "-"}</span>;

        return (
          <InputNumber
            min={0}
            max={100}
            value={row.discount || undefined}
            onChange={(val) => handleChange(rowIndex, "discount", val || null)}
          />
        );
      },
    },

    {
      accessorKey: "discountAmount",
      header: "Discount Amount",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.discountAmount || "-"}</span>;

        return (
          <InputNumber
            min={0}
            value={row.discountAmount || undefined}
            onChange={(val) => handleChange(rowIndex, "discountAmount", val || null)}
          />
        );
      },
    },

    {
      accessorKey: "mrp",
      header: "MRP",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.mrp || "-"}</span>;

        return (
          <InputNumber
            min={0}
            value={row.mrp || undefined}
            onChange={(val) => handleChange(rowIndex, "mrp", val || null)}
          />
        );
      },
    },

    {
  accessorKey: "totalAmount",
  header: "Total Amount",
  Edit: (rowIndex: number) => {
    const row = rows[rowIndex];
    const isNewRow = row.id === 0;
    const isEditable = editableRowIndex === rowIndex || isNewRow;

    if (!isEditable) return <span>{row.totalAmount}</span>;

    return (
      <InputNumber
        min={0}
        value={row.totalAmount}
        onChange={(val) => handleChange(rowIndex, "totalAmount", val)}
        onKeyDown={async (e) => {
          const isLastRow = rowIndex === rows.length - 1;
          if (e.key === "Tab" && !e.shiftKey && isLastRow && isNewRow) {
            if (row.itemId > 0 && row.purchaseQty > 0 && row.purchaseRate > 0) {
              const payload = {
                purchaseEntryId: row.purchaseEntryId,
                itemId: row.itemId,
                itemBatchNumberId: row.itemBatchNumberId,
                itemUomId: row.itemUomId,
                offerTermId: row.offerTermId,
                purchaseQty: row.purchaseQty,
                freeQty: row.freeQty,
                purchaseRate: row.purchaseRate,
                taxId: row.taxId,
                discount: row.discount,
                discountAmount: row.discountAmount,
                mrp: row.mrp,
                totalAmount: row.totalAmount,
                ucp: row.ucp,
                status: row.status,
                organizationId: row.organizationId,
              };

              // Focus on the next element first to prevent UI freeze
              setTimeout(() => {
                const nextElement = document.querySelector<HTMLElement>('button[title="Save"]');
                if (nextElement) {
                  nextElement.focus();
                }
              }, 0);

              createMutation.mutate(payload, {
                onSuccess: () => {
                  addRows();
                },
              });
            }
          }
        }}
      />
    );
  },
},
    {
      accessorKey: "ucp",
      header: "Unit Cost Price",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.ucp || "-"}</span>;

        return (
          <InputNumber
            min={0}
            value={row.ucp || undefined}
            onChange={(val) => handleChange(rowIndex, "ucp", val || null)}
          />
        );
      },
    },

    {
      accessorKey: "status",
      header: "Status",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.status}</span>;

        return (
          <select
            value={row.status}
            onChange={(e) => handleChange(rowIndex, "status", e.target.value)}
            className="border rounded p-1 w-full"
          >
            {statusOptions.map((option) => (
              <option key={option} value={option}>
                {statusMap[option]}
              </option>
            ))}
          </select>
        );
      },
    },

    {
          accessorKey: "organization.name",
          header: "Organization",
          Edit: (rowIndex: number) =>
            rows[rowIndex] ? (
              <CommonAutoComplete<OrganizationResponse, number>
                value={rows[rowIndex].organizationId}
                onChange={(val: number | "") =>
                  handleChange(rowIndex, "organizationId", val || 0)
                }
                useDataQuery={useListOrganization}
                labelKey="name"
                valueKey="id"
              />
            ) : null,
        },
  ];
};

export const purchaseEntryItemColumns: MRT_ColumnDef<PurchaseEntryItemResponse>[] = [
  { accessorKey: "id", header: "ID", grow: false, size: 50 },
  { accessorKey: "purchaseEntryId", header: "Purchase Entry" },
  { accessorKey: 'item.name', header: 'Item' },
  { accessorKey: 'itemBatchNumber.batchNumber', header: 'Batch Number' },
  { accessorKey: 'itemUom.name', header: 'UOM' },
  { accessorKey: 'offerTerm.name', header: 'Offer Term' },
  { accessorKey: 'purchaseQty', header: 'Purchase Qty' },
  { accessorKey: 'freeQty', header: 'Free Qty' },
  { accessorKey: 'purchaseRate', header: 'Purchase Rate' },
  { accessorKey: 'tax.name', header: 'Tax' },
  { accessorKey: 'discount', header: 'Discount (%)' },
  { accessorKey: 'discountAmount', header: 'Discount Amount' },
  { accessorKey: 'mrp', header: 'MRP' },
  { accessorKey: 'totalAmount', header: 'Total Amount' },
  { accessorKey: 'ucp', header: 'Unit Cost Price' },
  { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    } 
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: 'organization.name', header: 'Organization' },
  { accessorKey: "createdBy", header: "Created By" },
  { accessorKey: "updatedBy", header: "Updated By" },
];