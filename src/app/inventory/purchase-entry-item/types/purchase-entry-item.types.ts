import { z } from "zod";
import { PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";

export interface PurchaseEntryItemResponse extends Record<string, unknown> {
  id: number;
  purchaseEntryId: number;
  itemId: number;
  itemBatchNumberId: number;
  itemUomId: number;
  offerTermId: number;
  purchaseQty: number;
  freeQty?: number | null;
  purchaseRate: number;
  taxId?: number | null;  // Kept as optional number
  discount?: number | null;
  discountAmount?: number | null;
  mrp?: number | null;
  totalAmount: number;
  ucp?: number | null;
  status: string;
  organizationId: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  // Added optional relations for display purposes
  item?: {
    name: string;
  };
  itemUom?: {
    name: string;
  };
  organization?: {
    name: string;
  };
}

export type PurchaseEntryItemPaginatedResponse = PaginatedResponse<PurchaseEntryItemResponse>;
export type PurchaseEntryItemSuccessResponse = SuccessResponse<PurchaseEntryItemResponse>;

export const purchaseEntryItemRequestSchema = z.object({
  purchaseEntryId: z.number().int().positive(),
  itemId: z.number().int().positive(),
  itemBatchNumberId: z.number().int().positive(),
  itemUomId: z.number().int().positive(),
  offerTermId: z.number().int().positive(),
  purchaseQty: z.number().min(0),
  freeQty: z.union([z.number().min(0), z.literal(null)]).optional(),
  purchaseRate: z.number().min(0),
  taxId: z.union([z.number().int().min(0), z.literal(null)]).optional(), // Kept as optional number input
  discount: z.union([z.number().min(0).max(100), z.literal(null)]).optional(),
  discountAmount: z.union([z.number().min(0), z.literal(null)]).optional(),
  mrp: z.union([z.number().min(0), z.literal(null)]).optional(),
  totalAmount: z.number().min(0),
  ucp: z.union([z.number().min(0), z.literal(null)]).optional(),
  status: z.string().min(1),
  organizationId: z.number().int().positive(),
});

export type PurchaseEntryItemRequest = z.infer<typeof purchaseEntryItemRequestSchema>;

export interface PurchaseEntryItemFilters {
  purchaseEntryId?: number;
  itemId?: number;
  itemBatchNumberId?: number;
  itemUomId?: number;
  offerTermId?: number;
  taxId?: number;
  organizationId?: number;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
}