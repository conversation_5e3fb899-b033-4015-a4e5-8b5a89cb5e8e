"use client";

import { useCallback, useEffect, useState } from "react";
import {
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { Check, Pencil, Trash } from "lucide-react";
import { PurchaseEntryItemResponse } from "../types/purchase-entry-item.types";
import { PurchaseEntryItemColumns } from "../types/purchase-entry-item.fields";
import {
  useCreatePurchaseEntryItem,
  useDeletePurchaseEntryItem,
  useListPurchaseEntryItems,
  useUpdatePurchaseEntryItem,
} from "../query/purchase-entry-item.query";

// Helper to ensure number
const toNumber = (val: unknown): number => Number(val) || 0;

function getValueByAccessor<T>(row: T, accessor: string): unknown {
  return accessor.split(".").reduce<unknown>((acc, key) => {
    if (acc && typeof acc === "object" && key in acc) {
      return (acc as Record<string, unknown>)[key];
    }
    return undefined;
  }, row);
}

function PurchaseEntryItemForm({ purchaseEntryId }: { purchaseEntryId: number }) {
  const createMutation = useCreatePurchaseEntryItem();
  const updateMutation = useUpdatePurchaseEntryItem();
  const deleteMutation = useDeletePurchaseEntryItem();

  const [editableRowIndex, setEditableRowIndex] = useState<number | null>(null);
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [rows, setRows] = useState<PurchaseEntryItemResponse[]>([]);

  const { data, isLoading } = useListPurchaseEntryItems(1, 10, { purchaseEntryId });

  const handleAddNewRow = useCallback(() => {
    const newRow: PurchaseEntryItemResponse = {
      id: 0,
      purchaseEntryId,
      itemId: 0,
      itemBatchNumberId: 0,
      itemUomId: 0,
      offerTermId: 0,
      purchaseQty: 0,
      freeQty: null,
      purchaseRate: 0,
      taxId: null,
      discount: null,
      discountAmount: null,
      mrp: null,
      totalAmount: 0,
      ucp: null,
      status: "active",
      organizationId: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: 0,
      updatedBy: 0,
    };
    setRows((prev) => [...prev, newRow]);
    setEditableRowIndex(rows.length);
  }, [purchaseEntryId, rows.length]);

  useEffect(() => {
    if (data && data.data.length > 0) {
      setRows(data.data);
    } else {
      handleAddNewRow();
    }
  }, [data, purchaseEntryId, handleAddNewRow]);

  const handleSaveRow = (rowIndex: number) => {
    const row = rows[rowIndex];

    const payload = {
      purchaseEntryId: toNumber(row.purchaseEntryId),
      itemId: toNumber(row.itemId),
      itemBatchNumberId: toNumber(row.itemBatchNumberId),
      itemUomId: toNumber(row.itemUomId),
      offerTermId: toNumber(row.offerTermId),
      purchaseQty: toNumber(row.purchaseQty),
      freeQty: row.freeQty,
      purchaseRate: toNumber(row.purchaseRate),
      taxId: row.taxId,
      discount: row.discount,
      discountAmount: row.discountAmount,
      mrp: row.mrp,
      totalAmount: toNumber(row.totalAmount),
      ucp: row.ucp,
      status: row.status,
      organizationId: toNumber(row.organizationId),
    };

    if (row.id === 0) {
      createMutation.mutate(payload, {
        onSuccess: () => setEditableRowIndex(null),
      });
    } else {
      updateMutation.mutate(
        {
          id: row.id,
          request: payload,
        },
        {
          onSuccess: () => setEditableRowIndex(null),
        }
      );
    }
  };

  const columns = PurchaseEntryItemColumns(rows, setRows, createMutation, editableRowIndex);

  if (isLoading) {
    return <p className="text-sm text-gray-500">Loading Purchase Entry Items...</p>;
  }

  return (
    <Box
      sx={{
        backgroundColor: "#f9f9f9",
        borderRadius: 2,
        boxShadow: 2,
        maxWidth: "100%",
        margin: "auto",
        my: 2,
      }}
    >
      <Box className="flex justify-end mb-2">
        <button
          onClick={handleAddNewRow}
          className="bg-[#3A59D1] text-white px-3 py-1 rounded text-xs hover:bg-[#2f48a1]"
        >
          + Add Item
        </button>
      </Box>

      <table className="w-full border text-xs">
        <thead>
          <tr className="bg-[#3A59D1] text-white">
            {columns.map((column, index) => (
              <th key={index} className="border px-2 py-1 text-left">
                {column.header}
              </th>
            ))}
            <th className="border px-2 py-1 text-left">Actions</th>
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr key={rowIndex}>
              {columns.map((column, colIndex) => (
                <td key={colIndex} className="border px-2 py-1">
                  {column.Edit
                    ? column.Edit(rowIndex)
                    : String(getValueByAccessor(row, column.accessorKey) ?? "")}
                </td>
              ))}
              <td className="border px-2 py-1">
                <div className="flex items-center gap-2">
                  {editableRowIndex === rowIndex ? (
                    <button
                      onClick={() => handleSaveRow(rowIndex)}
                      className="text-green-600 hover:text-green-800"
                      title="Save"
                    >
                      <Check size={16} />
                    </button>
                  ) : (
                    <button
                      onClick={() => setEditableRowIndex(rowIndex)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Edit"
                    >
                      <Pencil size={16} />
                    </button>
                  )}
                  <button
                    onClick={() => {
                      setDeleteIndex(rowIndex);
                      setConfirmOpen(true);
                    }}
                    className="text-red-500 hover:text-red-700"
                    title="Delete"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      <Dialog open={confirmOpen} onClose={() => setConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this Purchase Entry Item?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmOpen(false)}>Cancel</Button>
          <Button
            onClick={() => {
              if (deleteIndex === null) return;
              const row = rows[deleteIndex];
              setConfirmOpen(false);

              if (row.id === 0) {
                setRows((prev) => prev.filter((_, i) => i !== deleteIndex));
              } else {
                deleteMutation.mutate(row.id, {
                  onSuccess: () => {
                    setRows((prev) => prev.filter((_, i) => i !== deleteIndex));
                  },
                });
              }

              setDeleteIndex(null);
            }}
            color="error"
            variant="contained"
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default PurchaseEntryItemForm;
