import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createPurchaseEntryItem,
  deletePurchaseEntryItem,
  getPurchaseEntryItem,
  listPurchaseEntryItems,
  updatePurchaseEntryItem,
} from "../api/purchase-entry-item.api";
import {
  PurchaseEntryItemFilters,
  PurchaseEntryItemPaginatedResponse,
  PurchaseEntryItemRequest,
  PurchaseEntryItemSuccessResponse,
} from "../types/purchase-entry-item.types";
import { toast } from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListPurchaseEntryItems = (
  page?: number,
  limit?: number,
  filters: PurchaseEntryItemFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<PurchaseEntryItemPaginatedResponse, ApiErrorResponse>({
    queryKey: ["purchase-entry-items", page, limit, filterKey],
    queryFn: () => listPurchaseEntryItems(page, limit, filters),
  });
};
export const useGetPurchaseEntryItem = (id: number | undefined) => {
  return useQuery({
    queryKey: ["purchase-entry-item", id],
    queryFn: () => getPurchaseEntryItem(id!),
    enabled: !!id,
  });
};

export const useCreatePurchaseEntryItem = () => {
  const queryClient = useQueryClient();

  return useMutation<
    PurchaseEntryItemSuccessResponse,
    ApiErrorResponse,
    PurchaseEntryItemRequest
  >({
    mutationKey: ["createPurchaseEntryItem"],
    mutationFn: (request) => createPurchaseEntryItem(request),
    onSuccess: (data) => {
      toast.success(
        data.message || "Purchase Entry Item created successfully!"
      );
      queryClient.invalidateQueries({ queryKey: ["purchase-entry-items"] });
    },
    onError: (error) => {
      toast.error(
        `Creation failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdatePurchaseEntryItem = () => {
  const queryClient = useQueryClient();

  return useMutation<
    PurchaseEntryItemSuccessResponse,
    ApiErrorResponse,
    { id: number; request: PurchaseEntryItemRequest }
  >({
    mutationKey: ["updatePurchaseEntryItem"],
    mutationFn: ({ id, request }) => updatePurchaseEntryItem(id, request),
    onSuccess: (data) => {
      toast.success(
        data.message || "Purchase Entry Item updated successfully!"
      );
      queryClient.invalidateQueries({ queryKey: ["purchase-entry-items"] });
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeletePurchaseEntryItem = () => {
  const queryClient = useQueryClient();

  return useMutation<
    PurchaseEntryItemSuccessResponse, // Use the actual response type
    ApiErrorResponse,
    number
  >({
    mutationKey: ["deletePurchaseEntryItem"], // Fixed typo in key
    mutationFn: (id) => deletePurchaseEntryItem(id),
    onSuccess: (data) => {
      toast.success(
        data.message || "Purchase Entry Item deleted successfully!"
      );
      queryClient.invalidateQueries({ queryKey: ["purchase-entry-items"] }); // Fixed query key
    },
    onError: (error) => {
      toast.error(
        `Deletion failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
