'use client';

import { purchaseEntryItemColumns } from "./types/purchase-entry-item.fields";
import { useListPurchaseEntryItems } from "./query/purchase-entry-item.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeletePurchaseEntryItem } from "./query/purchase-entry-item.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { PurchaseEntryItemResponse } from "./types/purchase-entry-item.types"
function PurchaseEntryItem() {
  const router = useRouter();
  const deleteMutation = useDeletePurchaseEntryItem();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<PurchaseEntryItemResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "PurchaseEntryItem",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);

  const handleOpenDelete = (row: PurchaseEntryItemResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<PurchaseEntryItemResponse>
        title="Purchase Entry Items"
        columns={purchaseEntryItemColumns}
        useDataQuery={useListPurchaseEntryItems}
        hiddenColumns={[
          "organizationId", 
          "createdAt", 
          "updatedAt", 
          "createdBy", 
          "updatedBy",
          "itemBatchNumberId",
          "itemUomId",
          "offerTermId",
          "taxId"
        ]}
        onCreate={() => router.push("/inventory/purchase-entry-item/create")}
        onExport={() => console.log("Export Purchase Entry Items")}
        onEdit={(row) => router.push(`/inventory/purchase-entry-item/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete purchase entry item{" "}
            <strong>ID: {selectedRow?.id}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default PurchaseEntryItem;