"use client";
import {
  purchaseEntryColumns,
  PurchaseEntryResponse,
} from "./types/purchase-entries.types";
import { useListPurchaseEntries } from "./query/purchase-entries.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeletePurchaseEntry } from "./query/purchase-entries.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";

function PurchaseEntries() {
  const router = useRouter();
  const deleteMutation = useDeletePurchaseEntry();
      
  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<PurchaseEntryResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "PurchaseEntry",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);
     
  const handleOpenDelete = (row: PurchaseEntryResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
          // Optionally show success notification
          // enqueueSnackbar("Purchase entry deleted successfully", { variant: "success" });
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          // Optionally show error notification
          // enqueueSnackbar("Error deleting purchase entry", { variant: "error" });
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<PurchaseEntryResponse>
        title="Purchase Entries"
        columns={purchaseEntryColumns}
        useDataQuery={useListPurchaseEntries}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/purchase-entries/create")}
        onExport={() => console.log("Export Purchase Entries")}
        onEdit={(row) =>
          router.push(`/inventory/purchase-entries/edit/${row.id}`)
        }
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}
      />
      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete purchase entry{" "}
            <strong>{selectedRow?.vendorInvoiceNo}</strong>? This action cannot
            be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default PurchaseEntries;