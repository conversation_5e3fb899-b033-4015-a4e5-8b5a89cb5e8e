"use client";

import { useParams } from "next/navigation";
import PurchaseEntryForm from "@/app/inventory/purchase-entries/forms/purchase-entries.forms";
import { Box, CircularProgress, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import PurchaseEntryItemForm from "@/app/inventory/purchase-entry-item/forms/purchase-entry-item.forms";

export default function EditPurchaseEntryPage() {
  const params = useParams();
  const [id, setId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (params?.id) {
      const parsedId = Number(params.id);
      if (!isNaN(parsedId)) {
        setId(parsedId);
      } else {
        console.error("Invalid ID format");
      }
      setIsLoading(false);
    }
  }, [params?.id]);

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (id === null) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <Typography color="error">Invalid purchase entry ID</Typography>
      </Box>
    );
  }

  return (
    <>
      <PurchaseEntryForm id={id} />
      <PurchaseEntryItemForm purchaseEntryId={id} />
    </>
  );
}