import apiClient from "@/app/api/api";
import {
  PurchaseEntryPaginatedResponse,
  PurchaseEntryResponse,
  PurchaseEntrySuccessResponse,
  PurchaseEntryRequest,
  PurchaseEntryFilters} from "../types/purchase-entries.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listPurchaseEntries = async (
  page?: number,
  limit?: number,
  filters?: PurchaseEntryFilters
): Promise<PurchaseEntryPaginatedResponse> => {
  const res = await apiClient.get<PurchaseEntryPaginatedResponse>("/purchase-entries", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });
  return res.data;
};

export const getPurchaseEntry = async (
  id: number
): Promise<PurchaseEntryResponse> => {
  const res = await apiClient.get<PurchaseEntryResponse>(`/purchase-entries/${id}`);
  return res.data;
};

export const createPurchaseEntry = async (
  purchaseEntryRequest: PurchaseEntryRequest
): Promise<PurchaseEntrySuccessResponse> => {
  const res = await apiClient.post<PurchaseEntrySuccessResponse>("/purchase-entries", purchaseEntryRequest);
  return res.data;
};

export const updatePurchaseEntry = async (
  id: number,
  purchaseEntryRequest: PurchaseEntryRequest
): Promise<PurchaseEntrySuccessResponse> => {
  const res = await apiClient.patch<PurchaseEntrySuccessResponse>(`/purchase-entries/${id}`, purchaseEntryRequest);
  return res.data;
};

export const deletePurchaseEntry = async (
  id: number
): Promise<PurchaseEntrySuccessResponse> => {
  const res = await apiClient.delete<PurchaseEntrySuccessResponse>(`/purchase-entries/${id}`);
  return res.data;
};
