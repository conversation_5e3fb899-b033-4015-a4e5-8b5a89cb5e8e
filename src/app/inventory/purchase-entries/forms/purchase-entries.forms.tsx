"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uItem,
  TextField,
  Typography,
  Card,
  Checkbox,
  FormControlLabel,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreatePurchaseEntry,
  useGetPurchaseEntry,
  useUpdatePurchaseEntry,
} from "../query/purchase-entries.query";
import { useRef, useEffect } from "react";
import {
  purchaseEntryRequestSchema,
  PurchaseEntryRequest,
} from "../types/purchase-entries.types";
import { useRouter } from "next/navigation";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListGstItcEligibility } from "../../gst-itc/query/gst-itc.query";
import { GstItcEligibilityPaginatedResponse, GstItcEligibilityResponse } from "../../gst-itc/types/gst-itc.types";
import { useListPurchaseOrders } from "../../purchase-orders/query/purchase-order.query";
import { useListStores } from "../../stores/query/stores.query";
import { StorePaginatedResponse, StoreResponse } from "../../stores/types/stores.types";
import { VendorPaginatedResponse, VendorResponse } from "../../vendors/types/vendor.types";
import { PurchaseOrderResponse, PurchaseOrderPaginatedResponse } from "../../purchase-orders/types/purchase-orders.types";
import { useListVendors } from "../../vendors/query/vendor.query";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Draft", value: "draft" },
  { label: "Pending Approval", value: "pendingApproval" },
  { label: "Approved", value: "approved" },
  { label: "Returned", value: "returned" },
];

export default function PurchaseEntryForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: purchaseEntryData, isLoading } = useGetPurchaseEntry(id);
  const createMutation = useCreatePurchaseEntry();
  const updateMutation = useUpdatePurchaseEntry();
  const router = useRouter();
  const firstFieldRef = useRef<HTMLInputElement>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<PurchaseEntryRequest>({
    resolver: zodResolver(purchaseEntryRequestSchema),
    defaultValues: {
      purchaseOrderId: undefined,
      vendorId: undefined,
      vendorInvoiceNo: "",
      vendorInvoiceDate: new Date().toISOString(),
      paymentTerms: "",
      purchaseEntryNumber: "",
      totalPurchaseRate: 0,
      storeId: undefined,
      gstItcEligibleId: undefined,
      discount: 0,
      discountApply: false,
      discountAmount: 0,
      dueDate: new Date().toISOString(),
      subTotalAmount: 0,
      totalAmount: 0,
      status: "draft",
      organizationId: undefined,
    },
  });

  useEffect(() => {
    if (isEditMode && purchaseEntryData) {
      reset({
        ...purchaseEntryData,
        // Ensure all fields have values, even if they're null in the API response
        storeId: purchaseEntryData.storeId || 0,
        // Add similar fallbacks for other fields if needed
      });
      setTimeout(() => {
        firstFieldRef.current?.focus();
      }, 0);
    }
  }, [isEditMode, purchaseEntryData, reset]);

  // In purchase-entries.forms.tsx
const onSubmit = (data: PurchaseEntryRequest) => {
  if (isEditMode && id) {
    updateMutation.mutate(
      { id, purchaseEntryRequest: data },
      {
        onSuccess: () => {
          router.push("/inventory/purchase-entries");
        },
      }
    );
  } else {
    createMutation.mutate(data, {
      onSuccess: (response) => {
        // Redirect to edit page with the new ID
        if (response.data?.id) {
          router.push(`/inventory/purchase-entries/edit/${response.data.id}`);
        } else {
          // Fallback in case ID is not available
          router.push("/inventory/purchase-entries");
        }
      },
    });
  }
};




  const handleCancel = () => {
    router.back();
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

return (
  <LocalizationProvider dateAdapter={AdapterDayjs}>
    <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Purchase Entry" : "Create Purchase Entry"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Row 1 */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="purchaseOrderId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<PurchaseOrderResponse, PurchaseOrderPaginatedResponse, number>
                  label="Purchase Order"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListPurchaseOrders}
                  inputRef={firstFieldRef}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.purchaseOrderId}
                  helperText={errors.purchaseOrderId?.message}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="vendorId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<VendorResponse, VendorPaginatedResponse, number>
                  label="Vendor"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListVendors}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.vendorId}
                  helperText={errors.vendorId?.message}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="vendorInvoiceNo"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Vendor Invoice No"
                  size="small"
                  fullWidth
                  value={field.value || ""}
                  onChange={field.onChange}
                  error={!!errors.vendorInvoiceNo}
                  helperText={errors.vendorInvoiceNo?.message}
                  required
                />
              )}
            />
          </Grid>

          {/* Row 2 */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="vendorInvoiceDate"
              control={control}
              render={({ field }) => (
                <DatePicker
                  label="Invoice Date"
                  value={field.value ? dayjs(field.value) : dayjs()}
                  onChange={(date) => field.onChange(date?.toISOString())}
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true,
                      error: !!errors.vendorInvoiceDate,
                      helperText: errors.vendorInvoiceDate?.message,
                      required: true,
                    },
                  }}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="paymentTerms"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Payment Terms"
                  size="small"
                  fullWidth
                  value={field.value || ""}
                  onChange={field.onChange}
                  error={!!errors.paymentTerms}
                  helperText={errors.paymentTerms?.message}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="purchaseEntryNumber"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Entry Number"
                  size="small"
                  fullWidth
                  value={field.value || ""}
                  onChange={field.onChange}
                  error={!!errors.purchaseEntryNumber}
                  helperText={errors.purchaseEntryNumber?.message}
                />
              )}
            />
          </Grid>

          {/* Row 3 */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="storeId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<StoreResponse, StorePaginatedResponse, number>
                  label="Store"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListStores}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.storeId}
                  helperText={errors.storeId?.message}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="gstItcEligibleId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<GstItcEligibilityResponse, GstItcEligibilityPaginatedResponse, number>
                  label="GST ITC Eligible"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListGstItcEligibility}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.gstItcEligibleId}
                  helperText={errors.gstItcEligibleId?.message}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="dueDate"
              control={control}
              render={({ field }) => (
                <DatePicker
                  label="Due Date"
                  value={field.value ? dayjs(field.value) : dayjs()}
                  onChange={(date) => field.onChange(date?.toISOString())}
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true,
                      error: !!errors.dueDate,
                      helperText: errors.dueDate?.message,
                    },
                  }}
                />
              )}
            />
          </Grid>

          {/* Row 4 - Pricing */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="totalPurchaseRate"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Total Purchase Rate"
                  type="number"
                  size="small"
                  fullWidth
                  value={field.value || 0}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                  error={!!errors.totalPurchaseRate}
                  helperText={errors.totalPurchaseRate?.message}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="subTotalAmount"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Sub Total"
                  type="number"
                  size="small"
                  fullWidth
                  value={field.value || 0}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                  error={!!errors.subTotalAmount}
                  helperText={errors.subTotalAmount?.message}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="totalAmount"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Total Amount"
                  type="number"
                  size="small"
                  fullWidth
                  value={field.value || 0}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                  error={!!errors.totalAmount}
                  helperText={errors.totalAmount?.message}
                />
              )}
            />
          </Grid>

          {/* Row 5 - Discount */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="discount"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Discount (%)"
                  type="number"
                  size="small"
                  fullWidth
                  value={field.value || 0}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                  error={!!errors.discount}
                  helperText={errors.discount?.message}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="discountAmount"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Discount Amount"
                  type="number"
                  size="small"
                  fullWidth
                  value={field.value || 0}
                  onChange={(e) => field.onChange(Number(e.target.value))}
                  error={!!errors.discountAmount}
                  helperText={errors.discountAmount?.message}
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="discountApply"
              control={control}
              render={({ field }) => (
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={field.value || false}
                      onChange={(e) => field.onChange(e.target.checked)}
                    />
                  }
                  label="Apply Discount"
                />
              )}
            />
          </Grid>

          {/* Organization (Create Mode Only) */}
          {!isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="organizationId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                    label="Organization"
                    value={field.value}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
                )}
              />
            </Grid>
          )}

          {/* Status (Edit Mode Only) */}
          {isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    value={field.value || "draft"}
                    onChange={field.onChange}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    {statuses.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
          )}
        </Grid>

        {/* Action Buttons */}
        <Box
          sx={{
            display: "flex",
            justifyContent: "flex-end",
            gap: 2,
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={handleCancel}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  </LocalizationProvider>
);
}