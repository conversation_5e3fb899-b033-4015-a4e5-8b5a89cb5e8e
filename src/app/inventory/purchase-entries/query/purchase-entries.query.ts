import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createPurchaseEntry,
  deletePurchaseEntry,
  getPurchaseEntry,
  listPurchaseEntries,
  updatePurchaseEntry,
} from "../api/purchase-entries.api";
import {
  PurchaseEntryFilters,
  PurchaseEntryPaginatedResponse,
  PurchaseEntryRequest,
  PurchaseEntrySuccessResponse,
} from "../types/purchase-entries.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListPurchaseEntries = (
  page?: number,
  limit?: number,
  filters: PurchaseEntryFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<PurchaseEntryPaginatedResponse, ApiErrorResponse>({
    queryKey: ["purchase-entries", page, limit, filterKey],
    queryFn: () => listPurchaseEntries(page, limit, filters),
  });
};
export const useGetPurchaseEntry = (id: number | undefined) => {
  return useQuery({
    queryKey: ["purchase-entry", id],
    queryFn: () => getPurchaseEntry(id!),
    enabled: !!id, // only run if id is defined
  });
};
// In purchase-entries.query.ts
export const useCreatePurchaseEntry = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PurchaseEntrySuccessResponse,
    ApiErrorResponse,
    PurchaseEntryRequest
  >({
    mutationFn: createPurchaseEntry,
    mutationKey: ["createPurchaseEntry"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["purchase-entries"] });
      toast.success(data.message);
      // Return the data so we can access it in the component
      return data;
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdatePurchaseEntry = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PurchaseEntrySuccessResponse, // The wrapped response from backend
    ApiErrorResponse,
    { id: number; purchaseEntryRequest: PurchaseEntryRequest }
  >({
    mutationKey: ["updatePurchaseEntry"],
    mutationFn: ({ id, purchaseEntryRequest }) =>
      updatePurchaseEntry(id, purchaseEntryRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["purchase-entries"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeletePurchaseEntry = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PurchaseEntrySuccessResponse, // Actual API response type
    ApiErrorResponse,
    number // Mutation input is just the ID
  >({
    mutationKey: ["deletePurchaseEntry"],
    mutationFn: (id) => deletePurchaseEntry(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["purchase-entries"] });
      toast.success(data.message || "Purchase entry deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
