import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
import { BaseResponse, PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from '@/app/common/utils/serialize.utils';

export interface PurchaseEntryResponse extends BaseResponse, Record<string, unknown> {
  code: string;
  purchaseOrderId: number;
  vendorId: number;
  vendorInvoiceNo: string;
  vendorInvoiceDate: string;
  paymentTerms: string;
  purchaseEntryNumber?: string | null;
  totalPurchaseRate: number;
  storeId: number;
  gstItcEligibleId: number;
  discount?: number | null;
  discountApply?: boolean | null;
  discountAmount?: number | null;
  dueDate?: string | null;
  subTotalAmount: number;
  totalAmount: number;
  status: string;
  organizationId: number;
}

export const purchaseEntryColumns: MRT_ColumnDef<PurchaseEntryResponse>[] = [
  { accessorKey: 'id', header: 'ID' ,grow: false, size: 50},
  { accessorKey: 'code', header: 'Code' },
  { accessorKey: 'purchaseOrder.name', header: 'Purchase Order' },
  { accessorKey: 'vendor.name', header: 'Vendor' },
  { accessorKey: 'vendorInvoiceNo', header: 'Vendor Invoice No' },
  { accessorKey: 'vendorInvoiceDate', header: 'Vendor Invoice Date'},
  { accessorKey: 'paymentTerms', header: 'Payment Term'},
  { accessorKey: 'purchaseEntryNumber', header: 'Purchase Entry Number'},
  { accessorKey: 'totalPurchaseRate', header: 'Total Purchase Rate'},
  { accessorKey: 'store.name', header: 'Store'},
  { accessorKey: 'gstItcEligible.name', header: 'GST ITC Eligible'},
  { accessorKey: 'discount', header: 'Discount'},
  { accessorKey: 'discountApply', header: 'Discount Apply'},
  { accessorKey: 'dueDate', header: 'Due Date'},
  { accessorKey: 'totalAmount', header: 'Total Amount' },
  { accessorKey: 'subTotalAmount', header: 'Sub Total Amount' },
  { accessorKey: 'discountAmount', header: 'Discount Amount' },
  { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value], // Capitalize first letter
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    } 
  },
  {
      accessorKey: "createdAt",
      header: "Created At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Updated At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
  { accessorKey: 'organization.name', header: 'Organization' },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];


export type PurchaseEntryPaginatedResponse = PaginatedResponse<PurchaseEntryResponse>;
export type PurchaseEntrySuccessResponse = SuccessResponse<PurchaseEntryResponse>;

export const purchaseEntryRequestSchema = z.object({
 purchaseOrderId: z.number().int().positive("Purchase Order ID is required"),
  vendorId: z.number().int().positive("Vendor ID is required"),
  vendorInvoiceNo: z
    .string()
    .min(1, "Vendor Invoice No is required")
    .transform((val) => val.trim()),
  vendorInvoiceDate: z
    .string()
    .min(1, "Vendor Invoice Date is required")
    .transform((val) => val.trim()),
  paymentTerms: z
    .string()
    .min(1, "Payment Terms are required")
    .transform((val) => val.trim()),
  purchaseEntryNumber: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val != null ? val.trim() : val)),
  totalPurchaseRate: z.number().min(0, "Total Purchase Rate must be positive"),
  storeId: z.number().int().positive("Store ID is required"),
  gstItcEligibleId: z.number().int().positive("GST ITC Eligible ID is required"),
  discount: z.number().min(0).max(100).optional().nullable(),
  discountApply: z.boolean().optional().nullable(),
  discountAmount: z.number().min(0).optional().nullable(),
  dueDate: z
    .string()
    .optional()
    .nullable()
    .transform((val) => (val != null ? val.trim() : val)),
  subTotalAmount: z.number().min(0, "Sub Total must be positive"),
  totalAmount: z.number().min(0, "Total must be positive"),
  status: z
    .string()
    .min(1, "Status is required")
    .max(20)
    .transform((val) => val.trim()),
  organizationId: z.number().int().positive("Organization ID is required"),
});

export type PurchaseEntryRequest = z.infer<typeof purchaseEntryRequestSchema>;

export interface PurchaseEntryFilters{
  purchaseOrderId?: number;
  vendorId?: number;
  vendorInvoiceNo?: string;
  vendorInvoiceDate?: string;
  paymentTerms?: string;
  purchaseEntryNumber?: string;
  storeId?: number;
  gstItcEligibleId?: number;
  dueDate?: string;
  organizationId?: number;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
}