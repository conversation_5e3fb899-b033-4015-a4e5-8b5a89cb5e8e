import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  listRoa,
  getRoa,
  createRoa,
  updateRoa,
  deleteRoa,
} from "../api/roa.api";
import { RoaFilters, RoaRequest } from "../types/roa.types";

// List Roa
export function useRoaList(page?: number, limit?: number, filters?: RoaFilters) {
  return useQuery({
    queryKey: ["roa-list", page, limit, filters],
    queryFn: () => listRoa(page, limit, filters)
  });
}

// Get single Roa
export function useRoa(id: number) {
  return useQuery({
    queryKey: ["roa", id],
    queryFn: () => getRoa(id),
    enabled: !!id,
  });
}

// Create Roa
export function useCreateRoa() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: RoaRequest) => createRoa(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roa-list"] });
    },
  });
}

// Update Roa
export function useUpdateRoa() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: RoaRequest }) =>
      updateRoa(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roa-list"] });
    },
  });
}

// Delete Roa
export function useDeleteRoa() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => deleteRoa(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["roa-list"] });
    },
  });
}