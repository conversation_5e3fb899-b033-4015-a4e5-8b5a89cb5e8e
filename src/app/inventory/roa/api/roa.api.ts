import apiClient from "@/app/api/api";
import { RoaPaginatedResponse, RoaResponse, RoaSuccessResponse, RoaRequest, RoaFilters } from "../types/roa.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listRoa = async (
  page?: number,
  limit?: number,
  filters?: RoaFilters
): Promise<RoaPaginatedResponse> => {
  const res = await apiClient.get<RoaPaginatedResponse>("/roa", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });
  return res.data;
};

export const getRoa = async (
  id: number
): Promise<RoaResponse> => {
  const res = await apiClient.get<RoaResponse>(`/roas/${id}`);
  return res.data;
};

export const createRoa = async (
  roaRequest: RoaRequest
): Promise<RoaSuccessResponse> => {
  const res = await apiClient.post<RoaSuccessResponse>("/roas", roaRequest);
  return res.data;
};

export const updateRoa = async (
  id: number,
  roaRequest: RoaRequest
): Promise<RoaSuccessResponse> => {
  const res = await apiClient.patch<RoaSuccessResponse>(`/roas/${id}`, roaRequest);
  return res.data;
};

export const deleteRoa = async (
  id: number
): Promise<RoaSuccessResponse> => {
  const res = await apiClient.delete<RoaSuccessResponse>(`/roas/${id}`);
  return res.data;
};