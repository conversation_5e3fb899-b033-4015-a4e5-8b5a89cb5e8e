import { MRT_ColumnDef } from "material-react-table";
import { z } from "zod";
import {
  statusOptions,
  statusMap,
  getStatusLabel,
} from "@/app/common/types/status.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";

export interface RoaResponse extends Record<string, unknown> {
  roaId: number;
  roaHimsCode: string;
  roaName: string;
  roaNameDisplay: string;
  roaAbbreviation: string;
  roaDescription: string;
  roaSnomedCode: string;
  roaCdcCode: string;
  roaStatus: "active" | "inactive";
  createdAt: string;
  createdBy: number;
  updatedAt: string;
  updatedBy: number;

}

export const roaColumns: MRT_ColumnDef<RoaResponse>[] = [
  { accessorKey: "roaId", header: "ID", grow: false, size: 50 },
  { accessorKey: "roaHimsCode", header: "HIMS Code" },
  { accessorKey: "roaName", header: "Name" },
  { accessorKey: "roaNameDisplay", header: "Display Name" },
  { accessorKey: "roaAbbreviation", header: "Abbreviation" },
  { accessorKey: "roaDescription", header: "Description" },
  { accessorKey: "roaSnomedCode", header: "SNOMED Code" },
  { accessorKey: "roaCdcCode", header: "CDC Code" },
  {
    accessorKey: "roaStatus",
    header: "Status",
    filterVariant: "select",
    filterSelectOptions: statusOptions.map((value) => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => getStatusLabel(cell.getValue<string>()),
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => localTime(cell.getValue<string>()),
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => localTime(cell.getValue<string>()),
  },
];

export type RoaPaginatedResponse = PaginatedResponse<RoaResponse>;
export type RoaSuccessResponse = SuccessResponse<RoaResponse>;

export const roaRequestSchema = z.object({
  roaHimsCode: z.string().min(1, "HIMS Code is required").transform((val) => val.trim()),
  roaName: z.string().min(1, "Name is required").transform((val) => val.trim()),
  roaNameDisplay: z.string().min(1, "Display Name is required").transform((val) => val.trim()),
  roaAbbreviation: z.string().nullable().transform((val) => (val != null ? val.trim() : val)),
  roaDescription: z.string().nullable().transform((val) => (val != null ? val.trim() : val)),
  roaSnomedCode: z.string().nullable().transform((val) => (val != null ? val.trim() : val)),
  roaCdcCode: z.string().nullable().transform((val) => (val != null ? val.trim() : val)),
  roaStatus: z.enum(["active", "inactive"]),
});

export type RoaRequest = z.infer<typeof roaRequestSchema>;

export interface RoaFilters {
  roaName?: string;
  roaStatus?: "active" | "inactive";
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
}