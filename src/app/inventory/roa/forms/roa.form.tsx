"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Grid,
  <PERSON>u<PERSON>tem,
  <PERSON>Field,
  Typography,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { roaRequestSchema, RoaRequest } from "../types/roa.types";
import { useRef, useEffect } from "react";
import { useRouter } from "next/navigation";

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

interface RoaFormProps {
  defaultValues?: Partial<RoaRequest>;
  onSubmit: (data: RoaRequest) => void;
  isEditMode?: boolean;
}

export function RoaForm({ defaultValues, onSubmit, isEditMode }: RoaFormProps) {
  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<RoaRequest>({
    resolver: zodResolver(roaRequestSchema),
    defaultValues: {
      roaHimsCode: "",
      roaName: "",
      roaNameDisplay: "",
      roaAbbreviation: "",
      roaDescription: "",
      roaSnomedCode: "",
      roaCdcCode: "",
      roaStatus: "active",
      ...defaultValues,
    },
  });

  useEffect(() => {
    if (defaultValues) {
      reset({ ...defaultValues });
    }
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, [defaultValues, reset]);

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", mb: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit ROA" : "Create ROA"}
      </Typography>
      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
         <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="roaHimsCode"
              control={control}
              render={({ field }) => (
                <TextField
                  inputRef={nameRef}
                  label="HIMS Code"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.roaHimsCode}
                  helperText={errors.roaHimsCode?.message}
                  required
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="roaName"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.roaName}
                  helperText={errors.roaName?.message}
                  required
                />
              )}
            />
          </Grid>
           <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="roaNameDisplay"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Display Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.roaNameDisplay}
                  helperText={errors.roaNameDisplay?.message}
                  required
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="roaAbbreviation"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Abbreviation"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.roaAbbreviation}
                  helperText={errors.roaAbbreviation?.message}
                />
              )}
            />
          </Grid>
         <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="roaDescription"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Description"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.roaDescription}
                  helperText={errors.roaDescription?.message}
                />
              )}
            />
          </Grid>
       <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="roaSnomedCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="SNOMED Code"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.roaSnomedCode}
                  helperText={errors.roaSnomedCode?.message}
                />
              )}
            />
          </Grid>
         <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="roaCdcCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="CDC Code"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.roaCdcCode}
                  helperText={errors.roaCdcCode?.message}
                />
              )}
            />
          </Grid>
    <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="roaStatus"
              control={control}
              render={({ field }) => (
                <TextField
                  select
                  label="Status"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.roaStatus}
                  helperText={errors.roaStatus?.message}
                  required
                >
                  {statuses.map((opt) => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
        </Grid>
        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={isSubmitting}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { roaRequestSchema, RoaRequest } from "../types/roa.types";

// interface RoaFormProps {
//   defaultValues?: Partial<RoaRequest>;
//   onSubmit: (data: RoaRequest) => void;
// }

// export function RoaForm({ defaultValues, onSubmit }: RoaFormProps) {
//   const {
//     register,
//     handleSubmit,
//     formState: { errors, isSubmitting },
//   } = useForm<RoaRequest>({
//     resolver: zodResolver(roaRequestSchema),
//     defaultValues,
//   });

//   return (
//     <form onSubmit={handleSubmit(onSubmit)} autoComplete="off">
//       <div>
//         <label>HIMS Code</label>
//         <input {...register("roaHimsCode")} />
//         {errors.roaHimsCode && <span>{errors.roaHimsCode.message}</span>}
//       </div>
//       <div>
//         <label>Name</label>
//         <input {...register("roaName")} />
//         {errors.roaName && <span>{errors.roaName.message}</span>}
//       </div>
//       <div>
//         <label>Display Name</label>
//         <input {...register("roaNameDisplay")} />
//         {errors.roaNameDisplay && <span>{errors.roaNameDisplay.message}</span>}
//       </div>
//       <div>
//         <label>Abbreviation</label>
//         <input {...register("roaAbbreviation")} />
//         {errors.roaAbbreviation && <span>{errors.roaAbbreviation.message}</span>}
//       </div>
//       <div>
//         <label>Description</label>
//         <input {...register("roaDescription")} />
//         {errors.roaDescription && <span>{errors.roaDescription.message}</span>}
//       </div>
//       <div>
//         <label>SNOMED Code</label>
//         <input {...register("roaSnomedCode")} />
//         {errors.roaSnomedCode && <span>{errors.roaSnomedCode.message}</span>}
//       </div>
//       <div>
//         <label>CDC Code</label>
//         <input {...register("roaCdcCode")} />
//         {errors.roaCdcCode && <span>{errors.roaCdcCode.message}</span>}
//       </div>
//       <div>
//         <label>Status</label>
//         <select {...register("roaStatus")}>
//           <option value="active">Active</option>
//           <option value="inactive">Inactive</option>
//         </select>
//         {errors.roaStatus && <span>{errors.roaStatus.message}</span>}
//       </div>
//       <button type="submit" disabled={isSubmitting}>
//         Submit
//       </button>
//        </form>
//   );
// }