"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { RoaForm } from "../../forms/roa.form";
import { getRoa, updateRoa } from "../../api/roa.api";
import { RoaRequest, RoaResponse } from "../../types/roa.types";

export default function RoaEdit() {
  const params = useParams();
  const router = useRouter();
  const id = params?.id as string | undefined;
  const [defaultValues, setDefaultValues] = useState<Partial<RoaRequest>>();

  useEffect(() => {
    if (id) {
      getRoa(Number(id)).then((data: RoaResponse) => {
        setDefaultValues({
          roaHimsCode: data.roaHimsCode,
          roaName: data.roaName,
          roaNameDisplay: data.roaNameDisplay,
          roaAbbreviation: data.roaAbbreviation,
          roaDescription: data.roaDescription,
          roaSnomedCode: data.roaSnomedCode,
          roaCdcCode: data.roaCdcCode,
          roaStatus: data.roaStatus,
        });
      });
    }
  }, [id]);

  const handleSubmit = async (data: RoaRequest) => {
    if (id) {
      await updateRoa(Number(id), data);
      router.push("/inventory/roa");
    }
  };

  if (!defaultValues) return <div>Loading...</div>;

  return (
    <div>
      <h2>Edit Roa</h2>
      <RoaForm defaultValues={defaultValues} onSubmit={handleSubmit} />
    </div>
  );
}