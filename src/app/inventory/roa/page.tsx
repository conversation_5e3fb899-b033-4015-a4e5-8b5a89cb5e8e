"use client";
import { useRouter } from "next/navigation";
import { MaterialReactTable } from "material-react-table";
import { useRoaList, useDeleteRoa } from "./query/roa.query";
import { roaColumns } from "./types/roa.types";

export default function RoaPage() {
  const router = useRouter();
  const { data, isLoading } = useRoaList();
  const deleteRoa = useDeleteRoa();

  return (
    <div>
      <h2>Route of Administration (ROA)</h2>
      {/* <button onClick={() => router.push("/inventory/roa/create")}>Add ROA</button> */}
      <MaterialReactTable
        columns={roaColumns}
        data={data?.data || []}
        state={{ isLoading }}
        enableRowActions
        renderRowActions={({ row }) => (
          <>
            <button onClick={() => router.push(`/inventory/roa/edit/${row.original.roaId}`)}>
              Edit
            </button>
            <button
              onClick={() => {
                if (window.confirm("Delete this ROA?")) {
                  deleteRoa.mutate(row.original.roaId);
                }
              }}
            >
              Delete
            </button>
          </>
        )}
      />
    </div>
  );
}