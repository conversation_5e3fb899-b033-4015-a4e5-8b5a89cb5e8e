import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createStockAudit,
  deleteStockAudit,
  getStockAudit,
  listStockAudits,
  updateStockAudit,
} from "../api/stock-audits.api";
import { StockAuditRequest, StockAuditFilters, StockAuditSuccessResponse } from "../types/stock-audits.types";
import { toast } from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListStockAudits = (
  page?: number,
  limit?: number,
  filters: StockAuditFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery({
    queryKey: ["stock-audits", page, limit, filterKey],
    queryFn: () => listStockAudits(page, limit, filters),
  });
};

export const useGetStockAudit = (id: number | undefined) => {
  return useQuery({
    queryKey: ["stock-audit", id],
    queryFn: () => getStockAudit(id!),
    enabled: !!id,
  });
};

export const useCreateStockAudit = () => {
  const queryClient = useQueryClient();

  return useMutation<
    StockAuditSuccessResponse,
    ApiErrorResponse,
    StockAuditRequest
  >({
    mutationKey: ["createStockAudit"],
    mutationFn: createStockAudit,
    onSuccess: (data) => {
      toast.success(data.message || "Stock Audit created successfully!");
      queryClient.invalidateQueries({ queryKey: ["stock-audits"] });
    },
    onError: (error) => {
      toast.error(
        `Creation failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateStockAudit = () => {
  const queryClient = useQueryClient();

  return useMutation<
    StockAuditSuccessResponse,
    ApiErrorResponse,
    { id: number; stockAuditRequest: StockAuditRequest }
  >({
    mutationKey: ["updateStockAudit"],
    mutationFn: ({ id, stockAuditRequest }) => updateStockAudit(id, stockAuditRequest),
    onSuccess: (data) => {
      toast.success(data.message || "Stock Audit updated successfully!");
      queryClient.invalidateQueries({ queryKey: ["stock-audits"] });
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteStockAudit = () => {
  const queryClient = useQueryClient();

  return useMutation<StockAuditSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteStockAudit"],
    mutationFn: deleteStockAudit,
    onSuccess: (data) => {
      toast.success(data.message || "Stock Audit deleted successfully!");
      queryClient.invalidateQueries({ queryKey: ["stock-audits"] });
    },
    onError: (error) => {
      toast.error(
        `Deletion failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};