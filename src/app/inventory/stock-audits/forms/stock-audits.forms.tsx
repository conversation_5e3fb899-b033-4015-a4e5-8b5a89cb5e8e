"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  TextField,
  Typo<PERSON>,
  Card,
  Grid,
  MenuItem,
} from "@mui/material";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useCreateStockAudit, useGetStockAudit, useUpdateStockAudit } from "../query/stock-audits.query";
import { useEffect } from "react";
import { stockAuditRequestSchema, StockAuditRequest } from "@/app/inventory/stock-audits/types/stock-audits.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { useListStores } from "@/app/inventory/stores/query/stores.query";
import { useListStockAuditTypes } from "@/app/inventory/stock-audit-type/query/stock-audit-type.query";
import { useListUsers } from "@/app/organization/users/query/user.query";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { StorePaginatedResponse, StoreResponse } from "../../stores/types/stores.types";
import { StockAuditTypePaginatedResponse, StockAuditTypeResponse } from "../../stock-audit-type/types/stock-audit-type.types";
import dayjs from "dayjs";
import { UserPaginatedResponse, UserResponse } from "@/app/organization/users/types/user.types";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Initiated", value: "initiated" },
  { label: "In Audit", value: "in-audit" },
  { label: "Cancelled", value: "cancelled" },
  { label: "In Review", value: "in-review" },
  { label: "Accepted", value: "accepted" },
  { label: "Rejected", value: "rejected" },    
];

export default function StockAuditForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: stockAuditData, isLoading } = useGetStockAudit(id);
  const createMutation = useCreateStockAudit();
  const updateMutation = useUpdateStockAudit();
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<StockAuditRequest>({
    resolver: zodResolver(stockAuditRequestSchema),
    defaultValues: {
      storeId: 0,
      auditTypeId: 0,
      auditDate: new Date().toISOString() ,
      assigneeId: 0,
      reviewerId: undefined,
      status: "initiated",
      organizationId: undefined,
    },
  });

  useEffect(() => {
    if (isEditMode && stockAuditData) {
      reset({
        storeId: stockAuditData.storeId,
        auditTypeId: stockAuditData.auditTypeId,
        auditDate: stockAuditData.auditDate,
        assigneeId: stockAuditData.assigneeId,
        reviewerId: stockAuditData.reviewerId,
        status: stockAuditData.status,
        organizationId: stockAuditData.organizationId,
      });
    }
  }, [isEditMode, stockAuditData, reset]);

  const onSubmit = (formData: StockAuditRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, stockAuditRequest: formData },
        {
          onSuccess: () => {
            router.push("/inventory/stock-audits");
          },
        }
      );
    } else {
      createMutation.mutate(formData, {
        onSuccess: () => {
          reset();
          router.push("/inventory/stock-audits");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

 return (
  <LocalizationProvider dateAdapter={AdapterDayjs}>
    <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Stock Audit" : "Create Stock Audit"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Store */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="storeId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<StoreResponse, StorePaginatedResponse, number>
                  label="Store"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListStores}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.storeId}
                  helperText={errors.storeId?.message}
                />
              )}
            />
          </Grid>

          {/* Audit Type */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="auditTypeId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<StockAuditTypeResponse, StockAuditTypePaginatedResponse, number>
                  label="Audit Type"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListStockAuditTypes}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.auditTypeId}
                  helperText={errors.auditTypeId?.message}
                />
              )}
            />
          </Grid>

          {/* Audit Date */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="auditDate"
              control={control}
              render={({ field }) => (
                <DatePicker
                  label="Audit Date"
                  value={field.value ? dayjs(field.value) : null}
                  onChange={(date) =>
                    field.onChange(date ? dayjs(date).toISOString() : "")
                  }
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true,
                      error: !!errors.auditDate,
                      helperText: errors.auditDate?.message,
                      required: true,
                    },
                  }}
                />
              )}
            />
          </Grid>


        {!isEditMode && (
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
            name="organizationId"
            control={control}
            render={({ field }) => (
                <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                label="Organization"
                value={field.value}
                onChange={field.onChange}
                useDataQuery={useListOrganization}
                labelKey="name"
                valueKey="id"
                searchable
                searchKey="name"
                error={!!errors.organizationId}
                helperText={errors.organizationId?.message}
                />
            )}
            />
        </Grid>
        )}
          {/* Status */}
          {isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
                name="status"
                control={control}
                render={({ field }) => (
                <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    value={field.value || "initiated"}
                    onChange={field.onChange}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                >
                    {statuses.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                        {option.label}
                    </MenuItem>
                    ))}
                </TextField>
                )}
            />
            </Grid>
        )}

          {/* Assignee */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="assigneeId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<UserResponse, UserPaginatedResponse, number>
                  label="Assignee"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListUsers}
                  labelKey="firstName"
                  valueKey="id"
                  searchable
                  searchKey="firstName"
                  error={!!errors.assigneeId}
                  helperText={errors.assigneeId?.message}
                />
              )}
            />
          </Grid>

          {/* Reviewer */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="reviewerId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<UserResponse, UserPaginatedResponse, number>
                  label="Reviewer (Optional)"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListUsers}
                  labelKey="firstName"
                  valueKey="id"
                  searchable
                  searchKey="firstName"
                  error={!!errors.reviewerId}
                  helperText={errors.reviewerId?.message}
                />
              )}
            />
          </Grid>
        </Grid>

        {/* Action Buttons */}
        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.push("/inventory/stock-audits")}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  </LocalizationProvider>
);
}