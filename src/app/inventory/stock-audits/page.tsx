"use client";

import { stockAuditColumns, StockAuditResponse } from "./types/stock-audits.types";
import { useListStockAudits } from "./query/stock-audits.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteStockAudit } from "./query/stock-audits.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";

function StockAudits() {
  const router = useRouter();
  const deleteMutation = useDeleteStockAudit();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<StockAuditResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "StockAudits",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );
  useEffect(() => {
      const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
      setRolePermissionFilters((prev) => ({
        ...prev,
        roleId,
      }));
    }, []);

  const handleOpenDelete = (row: StockAuditResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow?.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<StockAuditResponse>
        title="Stock Audits"
        columns={stockAuditColumns}
        useDataQuery={useListStockAudits}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/stock-audits/create")}
        onExport={() => console.log("Export Stock Audits")}
        onEdit={(row) => router.push(`/inventory/stock-audits/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0]?.create}
        isEdit={rolePermissionData?.data[0]?.update}
        isDelete={rolePermissionData?.data[0]?.delete}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this stock audit? 
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default StockAudits;