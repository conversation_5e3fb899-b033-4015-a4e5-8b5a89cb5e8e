import apiClient from "@/app/api/api";
import { 
  StockAuditPaginatedResponse, 
  StockAuditResponse, 
  StockAuditSuccessResponse, 
  StockAuditRequest,
  StockAuditFilters 
} from "../types/stock-audits.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listStockAudits = async (
  page?: number,
  limit?: number,
  filters?: StockAuditFilters
): Promise<StockAuditPaginatedResponse> => {
  const res = await apiClient.get<StockAuditPaginatedResponse>("/stock-audits", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });
  return res.data;
};

export const getStockAudit = async (
  id: number
): Promise<StockAuditResponse> => {
  const res = await apiClient.get<StockAuditResponse>(`/stock-audits/${id}`);
  return res.data;
}

export const createStockAudit = async (
  stockAuditRequest: StockAuditRequest
): Promise<StockAuditSuccessResponse> => {
  const res = await apiClient.post<StockAuditSuccessResponse>("/stock-audits", stockAuditRequest);
  return res.data;
}

export const updateStockAudit = async (
  id: number,
  stockAuditRequest: StockAuditRequest
): Promise<StockAuditSuccessResponse> => {
  const res = await apiClient.patch<StockAuditSuccessResponse>(`/stock-audits/${id}`, stockAuditRequest);
  return res.data;
}

export const deleteStockAudit = async (
  id: number
): Promise<StockAuditSuccessResponse> => {
  const res = await apiClient.delete<StockAuditSuccessResponse>(`/stock-audits/${id}`);
  return res.data;
};