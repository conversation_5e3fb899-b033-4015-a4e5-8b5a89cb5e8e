import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from '@/app/common/utils/serialize.utils';

export const stockAuditStatusOptions = [
  "initiated",
  "in-audit",
  "cancelled",
  "in-review",
  "accepted",
  "rejected"
] as const;

export const stockAuditStatusMap = {
  initiated: "Initiated",
  "in-audit": "In Audit",
  cancelled: "Cancelled",
  "in-review": "In Review",
  accepted: "Accepted",
  rejected: "Rejected"
};

export interface StockAuditResponse extends Record<string, unknown> {
  id: number;
  storeId: number;
  auditTypeId: number;
  auditDate: string;
  assigneeId: number;
  reviewerId: number ;
  status: typeof stockAuditStatusOptions[number];
  organizationId: number;
}

export const stockAuditColumns: MRT_ColumnDef<StockAuditResponse>[] = [
  { accessorKey: 'id', header: 'ID', grow: false, size: 50 },
  { accessorKey: 'store.name', header: 'Store' },
  { accessorKey: 'auditType.name', header: 'Audit Type' },
  { 
    accessorKey: 'auditDate', 
    header: 'Audit Date',
    filterVariant: "date-range",
    Cell: ({ cell }) => localTime(cell.getValue<string>())
  },
  { accessorKey: 'assignee.firstName', header: 'Assignee' },
  { accessorKey: 'reviewer.firstName', header: 'Reviewer' },
  { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: stockAuditStatusOptions.map(status => ({
      value: status,
      label: stockAuditStatusMap[status]
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return stockAuditStatusMap[status as keyof typeof stockAuditStatusMap];
    }
  },
  { accessorKey: 'organization.name', header: 'Organization' },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => localTime(cell.getValue<string>()),
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => localTime(cell.getValue<string>()),
  },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type StockAuditPaginatedResponse = PaginatedResponse<StockAuditResponse>;
export type StockAuditSuccessResponse = SuccessResponse<StockAuditResponse>;

export const stockAuditRequestSchema = z.object({
  storeId: z.number().int().positive("Store ID must be a positive number"),
  auditTypeId: z.number().int().positive("Audit Type ID must be a positive number"),
  auditDate: z.string().min(1, "Audit date is required"),
  assigneeId: z.number().int().positive("Assignee ID must be a positive number"),
  reviewerId: z.number().int().positive("Reviewer ID must be a positive number").optional(),
  status: z.enum(stockAuditStatusOptions),
  organizationId: z.number().int().positive("Organization ID must be a positive number").optional(),
});

export type StockAuditRequest = z.infer<typeof stockAuditRequestSchema>;

export interface StockAuditFilters {
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  storeId?: number;
  auditTypeId?: number;
  assigneeId?: number;
  reviewerId?: number;
  status?: typeof stockAuditStatusOptions[number];
  organizationId?: number;
  auditDateFrom?: string;
  auditDateTo?: string;
};