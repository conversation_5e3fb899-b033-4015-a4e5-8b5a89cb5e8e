// purchase-order-item.api.ts
import apiClient from "@/app/api/api";
import {
  PurchaseOrderItemFilters,
  PurchaseOrderItemPaginatedResponse,
  PurchaseOrderItemResponse,
  PurchaseOrderItemSuccessResponse,
  PurchaseOrderItemRequest,
} from "../types/purchase-order-item.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listPurchaseOrderItems = async (
  page?: number,
  limit?: number,
  filters?: PurchaseOrderItemFilters
): Promise<PurchaseOrderItemPaginatedResponse> => {
  const res = await apiClient.get<PurchaseOrderItemPaginatedResponse>(
    "/purchase-order-items",
    {
      params: {
        ...(page !== undefined && { page }),
        ...(limit !== undefined && { limit }),
        ...serializeFilters(filters as Record<string, unknown>),
      },
    }
  );
  return res.data;
};

export const getPurchaseOrderItem = async (
  id: number
): Promise<PurchaseOrderItemResponse> => {
  const res = await apiClient.get<PurchaseOrderItemResponse>(
    `/purchase-order-items/${id}`
  );
  return res.data;
};

export const createPurchaseOrderItem = async (
  data: PurchaseOrderItemRequest
): Promise<PurchaseOrderItemSuccessResponse> => {
  const res = await apiClient.post<PurchaseOrderItemSuccessResponse>(
    "/purchase-order-items",
    data
  );
  return res.data;
};

export const updatePurchaseOrderItem = async (
  id: number,
  data: PurchaseOrderItemRequest
): Promise<PurchaseOrderItemSuccessResponse> => {
  const res = await apiClient.patch<PurchaseOrderItemSuccessResponse>(
    `/purchase-order-items/${id}`,
    data
  );
  return res.data;
};

export const deletePurchaseOrderItem = async (
  id: number
): Promise<PurchaseOrderItemSuccessResponse> => {
  const res = await apiClient.delete<PurchaseOrderItemSuccessResponse>(
    `/purchase-order-items/${id}`
  );
  return res.data;
};