import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createPurchaseOrderItem,
  deletePurchaseOrderItem,
  getPurchaseOrderItem,
  listPurchaseOrderItems,
  updatePurchaseOrderItem,
} from "../api/purchase-order-item.api";

import {
  PurchaseOrderItemFilters,
  PurchaseOrderItemPaginatedResponse,
  PurchaseOrderItemRequest,
  PurchaseOrderItemSuccessResponse,
} from "../types/purchase-order-item.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListPurchaseOrderItems = (
  page?: number,
  limit?: number,
  filters: PurchaseOrderItemFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<PurchaseOrderItemPaginatedResponse, ApiErrorResponse>({
    queryKey: ["purchase-order-items", page, limit, filterKey],
    queryFn: () => listPurchaseOrderItems(page, limit, filters),
  });
};
export const useGetPurchaseOrderItem = (id: number | undefined) => {
  return useQuery({
    queryKey: ["purchase-order-item", id],
    queryFn: () => getPurchaseOrderItem(id!),
    enabled: !!id, // only run if id is defined
  });
};

export const useCreatePurchaseOrderItem = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PurchaseOrderItemSuccessResponse,
    ApiErrorResponse,
    PurchaseOrderItemRequest
  >({
    mutationFn: createPurchaseOrderItem,
    mutationKey: ["createPurchaseOrderItem"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["purchase-order-items"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdatePurchaseOrderItem = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PurchaseOrderItemSuccessResponse, // The wrapped response from backend
    ApiErrorResponse,
    { id: number; purchaseOrderItemRequest: PurchaseOrderItemRequest }
  >({
    mutationKey: ["updatePurchaseOrderItem"],
    mutationFn: ({ id, purchaseOrderItemRequest }) =>
      updatePurchaseOrderItem(id, purchaseOrderItemRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["purchase-order-items"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeletePurchaseOrderItem = () => {
  const queryClient = useQueryClient();
  return useMutation<
    PurchaseOrderItemSuccessResponse, // Actual API response type
    ApiErrorResponse,
    number // Mutation input is just the ID
  >({
    mutationKey: ["deletePurchaseOrderItem"],
    mutationFn: (id) => deletePurchaseOrderItem(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["purchase-order-items"] });
      toast.success(
        data.message || "Purchase order item deleted successfully!"
      );
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
