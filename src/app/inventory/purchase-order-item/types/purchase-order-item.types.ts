import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from '@/app/common/utils/serialize.utils';
// import { ItemType } from '../../items/types/items.types';
// Define the response type for Purchase Order Item
export interface PurchaseOrderItemResponse extends Record<string, unknown> {
  id: number;
  itemId: number;
  itemUomId: number;
  offerTermId: number;
  purchaseOrderId: number;
  purchaseQty: number;
  freeQty: number | null;
  purchaseRate: number;
  sellingRate: number | null;
  ucp: number | null;
  targetLocation: string | null;
  totalAmount: number;
   status: 'draft' | 'submit' | 'hold' | 'closed';
  organizationId: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
}
export const statusOptions = ['draft', 'submit', 'hold', 'closed'] as const;
export const statusMap = {
  draft: 'Draft',
  submit: 'Submitted',
  hold: 'On Hold',
  closed: 'Closed'
} as const;

export function getStatusLabel(status: string) {
  return statusMap[status as keyof typeof statusMap] || status;
}
export const purchaseOrderItemColumns: MRT_ColumnDef<PurchaseOrderItemResponse>[] = [
  { accessorKey: 'id', header: 'ID' ,grow: false, size: 50},
  { accessorKey: 'item.name', header: 'Item' },
  { accessorKey: 'itemUomId', header: 'Item UOM ID' },
  { accessorKey: 'offerTerm.name', header: 'Offer Term' },
  { accessorKey: 'purchaseOrder.name', header: 'Purchase Order' },
  { accessorKey: 'purchaseQty', header: 'Purchase Quantity' },
  { accessorKey: 'freeQty', header: 'Free Quantity' },
  { accessorKey: 'purchaseRate', header: 'Purchase Rate' },
  { accessorKey: 'sellingRate', header: 'Selling Rate' },
  { accessorKey: 'ucp', header: 'Unit Cost Price (UCP)' },
  { accessorKey: 'targetLocation', header: 'Target Location' },
  { accessorKey: 'totalAmount', header: 'Total Amount' },
  { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value], // Capitalize first letter
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    } 
  },
  { accessorKey: 'organization.name', header: 'Organization' },
    {
        accessorKey: "createdAt",
        header: "Created At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
      {
        accessorKey: "updatedAt",
        header: "Updated At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];
export type PurchaseOrderItemPaginatedResponse = PaginatedResponse<PurchaseOrderItemResponse>;
export type PurchaseOrderItemSuccessResponse = SuccessResponse<PurchaseOrderItemResponse>;

export const purchaseOrderItemRequestSchema = z.object({
    itemId: z.number().int(),
    itemUomId: z.number().int(),
    offerTermId: z.number().int(),
    purchaseOrderId: z.number().int(),
    purchaseQty: z.number().int(),
    freeQty: z.number().int().optional().nullable(),
    purchaseRate: z.number(),
    sellingRate: z.number().optional().nullable(),
    ucp: z.number().optional().nullable(),
    targetLocation: z.string().max(255).transform((val) => val.trim()).optional().nullable(),
    totalAmount: z.number(),
    status: z.enum(['draft', 'submit', 'hold', 'closed']),
    organizationId: z.number().int(),
});

export type PurchaseOrderItemRequest = z.infer<typeof purchaseOrderItemRequestSchema>;


export interface PurchaseOrderItemFilters{
  itemId?: number;
  itemUomId?: number;
  offerTermId?: number;
  purchaseOrderId?: number;
  targetLocation?: string;
  organizationId?: number;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
}

