"use client";

import {
  <PERSON>,
  But<PERSON>,
  MenuItem,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useCreatePurchaseOrderItem, useGetPurchaseOrderItem, useUpdatePurchaseOrderItem } from "../query/purchase-order-item.query";
import { useRef, useEffect } from "react";
import {
  purchaseOrderItemRequestSchema,
  PurchaseOrderItemRequest,
} from "@/app/inventory/purchase-order-item/types/purchase-order-item.types"

import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import {OrganizationPaginatedResponse, OrganizationResponse} from "@/app/organization/organization/types/organization.types";
import {useListOrganization} from "@/app/organization/organization/query/organization.query"
import { ItemPaginatedResponse, ItemResponse } from "@/app/inventory/items/types/items.types";
import { ItemUOMPaginatedResponse, ItemUOMResponse } from "@/app/inventory/item-uom/types/item-uom.types";
import { useListItemUOM } from "@/app/inventory/item-uom/query/item-uom.query";
import { useListItems } from "@/app/inventory/items/query/items.query";
import { OfferTermsPaginatedResponse, OfferTermsResponse } from "../../offer-terms/types/offer-terms.types";
import { useListOfferTerms } from "../../offer-terms/query/offer-terms.query";
import { PurchaseOrderPaginatedResponse, PurchaseOrderResponse } from "../../purchase-orders/types/purchase-orders.types";
import { useListPurchaseOrders } from "../../purchase-orders/query/purchase-order.query";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Draft", value: "draft" },
  { label: "Submitted", value: "submit" },
  { label: "On Hold", value: "hold" },
  { label: "Closed", value: "closed" },
];

export default function PurchaseOrderItemForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: itemData, isLoading } = useGetPurchaseOrderItem(id);
  const createMutation = useCreatePurchaseOrderItem();
  const updateMutation = useUpdatePurchaseOrderItem();
  const router = useRouter();

  const nameRef = useRef<HTMLInputElement>(null);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<PurchaseOrderItemRequest>({
    resolver: zodResolver(purchaseOrderItemRequestSchema),
    defaultValues: {
      itemId: undefined,
      itemUomId: undefined,
      offerTermId: undefined,
      purchaseOrderId: undefined,
      purchaseQty: 0,
      freeQty: 0,
      purchaseRate: 0,
      sellingRate: 0,
      ucp: null,
      targetLocation: null,
      totalAmount: 0,
      status: "draft",
      organizationId: undefined,
    },
  });

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isEditMode && itemData) {
      reset(itemData);
    }
  }, [isEditMode, itemData, reset]);

  const onSubmit = (data: PurchaseOrderItemRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate({ id, purchaseOrderItemRequest: data },
        {
          onSuccess: () => {
            router.push("/inventory/purchase-order-item");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          reset();
          router.push("/inventory/purchase-order-item");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

 return (
  <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
    <Typography variant="h6" mb={3}>
      {isEditMode ? "Edit Purchase Order Item" : "Create Purchase Order Item"}
    </Typography>

    <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
      <Grid container spacing={2}>
        {/* Item */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="itemId"
            control={control}
            render={({ field }) => (
              <CommonDropdown<ItemResponse, ItemPaginatedResponse, number>
                label="Item"
                value={field.value}
                onChange={field.onChange}
                useDataQuery={useListItems}
                labelKey="name"
                valueKey="id"
                searchable
                searchKey="name"
                error={!!errors.itemId}
                helperText={errors.itemId?.message}
              />
            )}
          />
        </Grid>

        {/* Item UOM */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="itemUomId"
            control={control}
            render={({ field }) => (
              <CommonDropdown<ItemUOMResponse, ItemUOMPaginatedResponse, number>
                label="Item UOM"
                value={field.value}
                onChange={field.onChange}
                useDataQuery={useListItemUOM}
                labelKey="id"
                valueKey="id"
                searchable
                searchKey="name"
                error={!!errors.itemUomId}
                helperText={errors.itemUomId?.message}
              />
            )}
          />
        </Grid>

        {/* Offer Term */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="offerTermId"
            control={control}
            render={({ field }) => (
              <CommonDropdown<OfferTermsResponse, OfferTermsPaginatedResponse, number>
                label="Offer Term"
                value={field.value}
                onChange={field.onChange}
                useDataQuery={useListOfferTerms}
                labelKey="name"
                valueKey="id"
                searchable
                searchKey="name"
                error={!!errors.offerTermId}
                helperText={errors.offerTermId?.message}
              />
            )}
          />
        </Grid>

        {/* Purchase Order */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="purchaseOrderId"
            control={control}
            render={({ field }) => (
              <CommonDropdown<PurchaseOrderResponse, PurchaseOrderPaginatedResponse, number>
                label="Purchase Order"
                value={field.value}
                onChange={field.onChange}
                useDataQuery={useListPurchaseOrders}
                labelKey="name"
                valueKey="id"
                searchable
                searchKey="name"
                error={!!errors.purchaseOrderId}
                helperText={errors.purchaseOrderId?.message}
              />
            )}
          />
        </Grid>

        {/* Purchase Quantity */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="purchaseQty"
            control={control}
            render={({ field }) => (
              <TextField
                label="Purchase Quantity"
                type="number"
                size="small"
                fullWidth
                {...field}
                onChange={(e) => field.onChange(Number(e.target.value))}
                error={!!errors.purchaseQty}
                helperText={errors.purchaseQty?.message}
                required
              />
            )}
          />
        </Grid>

        {/* Free Quantity */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="freeQty"
            control={control}
            render={({ field }) => (
              <TextField
                label="Free Quantity"
                type="number"
                size="small"
                fullWidth
                value={field.value || ''}
                onChange={(e) => field.onChange(e.target.value === '' ? null : Number(e.target.value))}
                error={!!errors.freeQty}
                helperText={errors.freeQty?.message}
              />
            )}
          />
        </Grid>

        {/* Purchase Rate */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="purchaseRate"
            control={control}
            render={({ field }) => (
              <TextField
                label="Purchase Rate"
                type="number"
                size="small"
                fullWidth
                {...field}
                onChange={(e) => field.onChange(Number(e.target.value))}
                error={!!errors.purchaseRate}
                helperText={errors.purchaseRate?.message}
                required
              />
            )}
          />
        </Grid>

        {/* Selling Rate */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="sellingRate"
            control={control}
            render={({ field }) => (
              <TextField
                label="Selling Rate"
                type="number"
                size="small"
                fullWidth
                value={field.value || ''}
                onChange={(e) => field.onChange(e.target.value === '' ? null : Number(e.target.value))}
                error={!!errors.sellingRate}
                helperText={errors.sellingRate?.message}
              />
            )}
          />
        </Grid>

        {/* UCP */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="ucp"
            control={control}
            render={({ field }) => (
              <TextField
                label="UCP"
                type="number"
                size="small"
                fullWidth
                value={field.value || ''}
                onChange={(e) => field.onChange(e.target.value === '' ? null : Number(e.target.value))}
                error={!!errors.ucp}
                helperText={errors.ucp?.message}
              />
            )}
          />
        </Grid>

        {/* Target Location */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="targetLocation"
            control={control}
            render={({ field }) => (
              <TextField
                label="Target Location"
                size="small"
                fullWidth
                {...field}
                value={field.value || ''}
                error={!!errors.targetLocation}
                helperText={errors.targetLocation?.message}
              />
            )}
          />
        </Grid>

        {/* Total Amount */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="totalAmount"
            control={control}
            render={({ field }) => (
              <TextField
                label="Total Amount"
                type="number"
                size="small"
                fullWidth
                {...field}
                onChange={(e) => field.onChange(Number(e.target.value))}
                error={!!errors.totalAmount}
                helperText={errors.totalAmount?.message}
              />
            )}
          />
        </Grid>

        {/* Organization */}
        {!isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="organizationId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                  label="Organization"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListOrganization}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.organizationId}
                  helperText={errors.organizationId?.message}
                />
              )}
            />
          </Grid>
        )}

        {/* Status */}
          {isEditMode && (
                      <Grid size={{ xs: 12, md: 6, lg: 4 }}>
                        <Controller
                          name="status"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              select
                              label="Status"
                              size="small"
                              fullWidth
                              value={field.value || "draft"}
                              onChange={field.onChange}
                              error={!!errors.status}
                              helperText={errors.status?.message}
                            >
                              {statuses.map((option) => (
                                <MenuItem key={option.value} value={option.value}>
                                  {option.label}
                                </MenuItem>
                              ))}
                            </TextField>
                          )}
                        />
                      </Grid>
                    )}
      </Grid>

      {/* Action Buttons */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          gap: 2,
          mt: 3,
        }}
      >
        <Button
          variant="outlined"
          color="inherit"
          onClick={() => router.push("/inventory/purchase-order-item")}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={createMutation.isPending || updateMutation.isPending}
        >
          {isEditMode ? "Update" : "Create"}
        </Button>
      </Box>
    </Box>
  </Card>
);
}