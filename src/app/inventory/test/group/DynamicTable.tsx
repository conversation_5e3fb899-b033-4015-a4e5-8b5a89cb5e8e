'use client';

import { useState } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  ColumnDef,
  flexRender,
  ColumnResizeMode,
  RowData,
  CellContext,
} from '@tanstack/react-table';

// Define our base data type
type BaseData = {
  id: string;
  [key: string]: unknown;
};

// Column configuration type
export type DynamicColumn = {
  id: string;
  header: string;
  cellType?: 'text' | 'number' | 'select' | 'checkbox' | 'date';
  options?: string[]; // For select type
  editable?: boolean;
  required?: boolean;
};

type DynamicTableProps = {
  initialData?: BaseData[];
  initialColumns?: DynamicColumn[];
};

/* eslint-disable @typescript-eslint/no-unused-vars */
declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    updateData: (rowIndex: number, columnId: string, value: unknown) => void;
  }
}
/* eslint-enable @typescript-eslint/no-unused-vars */


export function DynamicTable({
  initialData = [],
  initialColumns = [],
}: DynamicTableProps) {
  // State management
  const [data, setData] = useState<BaseData[]>(initialData);
  const [columnsConfig, setColumnsConfig] = useState<DynamicColumn[]>(initialColumns);
  const [columnResizeMode] = useState<ColumnResizeMode>('onChange');

  // Convert our column config to TanStack columns
  const columns: ColumnDef<BaseData>[] = columnsConfig.map((col) => ({
    accessorKey: col.id,
    header: col.header,
    size: 150,
    cell: (cellContext) => (
      <EditableCell
        cellContext={cellContext}
        columnConfig={columnsConfig.find((c) => c.id === cellContext.column.id)}
      />
    ),
  }));

  type EditableCellProps = {
  cellContext: CellContext<BaseData, unknown>;
  columnConfig?: DynamicColumn;
};

function EditableCell({ cellContext, columnConfig }: EditableCellProps) {
  const {
    getValue,
    row: { index },
    column: { id },
    table,
  } = cellContext;

  const initialValue = getValue();
  const [value, setValue] = useState(initialValue);

  const updateValue = (newValue: unknown) => {
    table.options.meta?.updateData(index, id, newValue);
  };

  const handleBlur = () => {
    if (value !== initialValue) {
      updateValue(value);
    }
  };

  if (!columnConfig?.editable) {
    return <span>{String(value ?? "")}</span>;
  }

  switch (columnConfig.cellType) {
    case "number":
      return (
        <input
          type="number"
          value={value as number}
          onChange={(e) => setValue(e.target.valueAsNumber)}
          onBlur={handleBlur}
          className="w-full p-1 border-none bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      );
    case "select":
      return (
        <select
          value={value as string}
          onChange={(e) => {
            setValue(e.target.value);
            updateValue(e.target.value);
          }}
          className="w-full p-1 border-none bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-500"
        >
          {columnConfig.options?.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
      );
    case "checkbox":
      return (
        <input
          type="checkbox"
          checked={Boolean(value)}
          onChange={(e) => {
            setValue(e.target.checked);
            updateValue(e.target.checked);
          }}
          className="h-4 w-4"
        />
      );
    case "date":
      return (
        <input
          type="date"
          value={value as string}
          onChange={(e) => setValue(e.target.value)}
          onBlur={handleBlur}
          className="w-full p-1 border-none bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      );
    default:
      return (
        <input
          type="text"
          value={value as string}
          onChange={(e) => setValue(e.target.value)}
          onBlur={handleBlur}
          className="w-full p-1 border-none bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-500"
        />
      );
  }
}

  // Initialize table
  const table = useReactTable({
    data,
    columns,
    columnResizeMode,
    getCoreRowModel: getCoreRowModel(),
    meta: {
      updateData: (rowIndex, columnId, value) => {
        setData(prev =>
          prev.map((row, index) =>
            index === rowIndex ? { ...row, [columnId]: value } : row
          )
        );
      },
    },
  });

  // Add new column
  /*
  const addColumn = (newColumn: DynamicColumn) => {
    setColumnsConfig([...columnsConfig, newColumn]);
    
    // Add empty values for the new column to existing rows
    setData(data.map(row => ({
      ...row,
      [newColumn.id]: newColumn.cellType === 'checkbox' ? false : '',
    })));
  };*/

  // Remove column
  const removeColumn = (columnId: string) => {
    setColumnsConfig(columnsConfig.filter(col => col.id !== columnId));
    setData(data.map(row => {
      const newRow = { ...row };
      delete newRow[columnId];
      return newRow;
    }));
  };

  // Add new row
  const addRow = () => {
    const newRow: BaseData = { id: crypto.randomUUID() };
    columnsConfig.forEach(col => {
      newRow[col.id] = col.cellType === 'checkbox' ? false : '';
    });
    setData([...data, newRow]);
  };

  // Remove row
  const removeRow = (rowId: string) => {
    setData(data.filter(row => row.id !== rowId));
  };

  return (
    <div className="p-4">
      {/* Column Management */}
      <div className="mb-4 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Manage Columns</h2>
        <div className="flex flex-wrap gap-4">
          {columnsConfig.map(column => (
            <div key={column.id} className="flex items-center gap-2 bg-white p-2 rounded border">
              <span>{column.header}</span>
              <button 
                onClick={() => removeColumn(column.id)}
                className="text-red-500 hover:text-red-700"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto border rounded-lg">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <th 
                    key={header.id} 
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider relative"
                    style={{ width: header.getSize() }}
                  >
                    {flexRender(header.column.columnDef.header, header.getContext())}
                    <div
                      onMouseDown={header.getResizeHandler()}
                      onTouchStart={header.getResizeHandler()}
                      className={`absolute right-0 top-0 h-full w-1 bg-gray-200 cursor-col-resize select-none touch-none ${
                        header.column.getIsResizing() ? 'bg-blue-500' : ''
                      }`}
                    />
                  </th>
                ))}
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            ))}
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {table.getRowModel().rows.map(row => (
              <tr key={row.id} className="hover:bg-gray-50">
                {row.getVisibleCells().map(cell => (
                  <td key={cell.id} className="px-6 py-4">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
                <td className="px-6 py-4">
                  <button
                    onClick={() => removeRow(row.original.id)}
                    className="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Row Management */}
      <div className="mt-4 flex justify-between">
        <button
          onClick={addRow}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Add Row
        </button>
      </div>
    </div>
  );
}