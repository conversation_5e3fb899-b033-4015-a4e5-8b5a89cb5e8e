"use client";

import { Path, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "primereact/button";
import { <PERSON>FieldRenderer } from "@/app/common/table-crud/fields/table-form-fields/table-form-fields.fields";
import { TableCrudColumnDef } from "@/app/common/table-crud/types/table-crud.types";

// Zod schema
const schema = z.object({
  name: z.string().min(1, "Name is required"),
  age: z.coerce.number().min(0, "Age must be at least 0"),
  dob: z.coerce.date(),
  gender: z.string().min(1, "Gender is required"),
  active: z.boolean(),
});

type FormData = z.infer<typeof schema>;

// Column config for Form
const columns: TableCrudColumnDef<FormData>[] = [
  { accessorKey: "name", header: "Name", type: "text" },
  { accessorKey: "age", header: "Age", type: "number" },
  { accessorKey: "dob", header: "DOB", type: "date" },
  {
    accessorKey: "gender",
    header: "Gender",
    type: "select",
    options: [
      { label: "Male", value: "male" },
      { label: "Female", value: "female" },
    ],
  },
  { accessorKey: "active", header: "Active", type: "checkbox" },
];

export default function CrudFormExample() {
  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: "",
      age: 0,
      dob: new Date(),
      gender: "",
      active: false,
    },
  });

  const onSubmit = (data: FormData) => {
    console.log("Submitted data:", data);
  };

  return (
    <form
      onSubmit={form.handleSubmit(onSubmit)}
      className="grid grid-cols-12 gap-4 p-4"
    >
      {columns.map((col) => (
        <FormFieldRenderer<FormData, FormData>
          key={col.accessorKey}
          name={col.accessorKey as Path<FormData>}
          column={col}
          form={form}
          className="col-span-12 sm:col-span-6 md:col-span-4 lg:col-span-3"
        />
      ))}
      <div className="col-span-12 flex justify-end">
        <Button type="submit" label="Submit" />
      </div>
    </form>
  );
}
