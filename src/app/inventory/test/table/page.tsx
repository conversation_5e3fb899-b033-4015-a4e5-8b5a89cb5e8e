"use client";

import React, { useState } from "react";
import { useForm, useFieldArray, Path } from "react-hook-form";
import { DataTable } from "primereact/datatable";
import { Column, ColumnEditorOptions } from "primereact/column";
import { <PERSON><PERSON> } from "primereact/button";
import { TableCrudColumnDef } from "@/app/common/table-crud/types/table-crud.types";
import { FormFieldRenderer } from "@/app/common/table-crud/fields/table-form-fields/table-form-fields.fields";

type Row = {
  id: string;
  name: string;
  age: number;
  gender: string;
  active: boolean;
};

type FormData = {
  rows: Row[];
};

const columns: TableCrudColumnDef<Row>[] = [
  { accessorKey: "name", header: "Name", type: "text" },
  { accessorKey: "age", header: "Age", type: "number" },
  {
    accessorKey: "gender",
    header: "Gender",
    type: "select",
    options: [
      { label: "Male", value: "male" },
      { label: "Female", value: "female" },
    ],
  },
  { accessorKey: "active", header: "Active", type: "checkbox" },
];

export default function DynamicEditableTable() {
  const form = useForm<FormData>({
    defaultValues: {
      rows: [
        {
          id: "1",
          name: "<PERSON> Doe",
          age: 30,
          gender: "male",
          active: true,
        },
        {
          id: "2",
          name: "Jane Smith",
          age: 25,
          gender: "female",
          active: false,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "rows",
    keyName: "fieldId", // avoid key conflicts with id
  });

  const [editingRowIndex, setEditingRowIndex] = useState<number | null>(null);

  const isEditing = (index: number) => editingRowIndex === index;

  // Editor using FormFieldRenderer - now properly uses options.rowIndex
  const editor = (col: TableCrudColumnDef<Row>) => {
    const EditorComponent = (options: ColumnEditorOptions) => {
      const rowIndex = options.rowIndex;
      const fieldName = `rows.${rowIndex}.${col.accessorKey}` as Path<FormData>;
      const row = form.getValues(`rows.${rowIndex}`);

      return (
        <FormFieldRenderer<FormData, Row>
          name={fieldName}
          column={col}
          form={form}
          row={row}
          className="p-fluid"
        />
      );
    };

    EditorComponent.displayName = `Editor_${col.accessorKey}`;
    return EditorComponent;
  };


  const onRowEditInit = (e: { index: number }) => {
    setEditingRowIndex(e.index);
  };

  const onRowEditCancel = () => {
    form.reset(form.getValues());
    setEditingRowIndex(null);
  };

  const onRowEditSave = (e: { index: number }) => {
    form.trigger(`rows.${e.index}`).then((valid) => {
      if (valid) setEditingRowIndex(null);
      isEditing(e.index);
    });
  };

  return (
    <form
      onSubmit={form.handleSubmit((data) => {
        console.log("Submitted rows:", data.rows);
      })}
    >
      <DataTable
        size="small"
        value={fields}
        dataKey="id"
        editMode="row"
        onRowEditInit={onRowEditInit}
        onRowEditCancel={onRowEditCancel}
        onRowEditSave={onRowEditSave}
        tableStyle={{ minWidth: "60rem" }}
        resizableColumns
        showGridlines
        columnResizeMode="fit"
        scrollable
        scrollHeight="400px"
        paginator
        rows={5}
        rowsPerPageOptions={[5, 10, 20]}
      >
        {columns.map((col) => (
          <Column
            key={col.accessorKey}
            field={col.accessorKey}
            header={col.header}
            editor={editor(col)}
            style={{ width: "20%" }}
            filter
            filterPlaceholder={`Search by ${col.header}`}
            showFilterMenu={false}
          />
        ))}

        <Column
          rowEditor
          headerStyle={{ width: "10%", minWidth: "8rem" }}
          bodyStyle={{ textAlign: "center" }}
        />
        <Column
          header="Delete"
          body={(rowData, options) => (
            <Button
              icon="pi pi-trash"
              className="p-button-danger p-button-text"
              onClick={() => remove(options.rowIndex)}
              tooltip="Delete"
            />
          )}
          headerStyle={{ width: "8rem" }}
          bodyStyle={{ textAlign: "center" }}
        />
      </DataTable>

      <div className="mt-3 flex gap-2">
        <Button
          label="Add Row"
          icon="pi pi-plus"
          onClick={() =>
            append({
              id: Math.random().toString(),
              name: "",
              age: 0,
              gender: "",
              active: false,
            })
          }
        />
        <Button type="submit" label="Submit All" icon="pi pi-check" />
      </div>
    </form>
  );
}
