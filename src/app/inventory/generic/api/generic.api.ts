// generic.api.ts
import apiClient from "@/app/api/api";
import { 
  GenericPaginatedResponse, 
  GenericResponse, 
  GenericSuccessResponse, 
  GenericRequest,
  GenericFilters 
} from "../types/generic.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listGeneric = async (
  page?: number,
  limit?: number,
  filters?: GenericFilters
): Promise<GenericPaginatedResponse> => {
  const res = await apiClient.get<GenericPaginatedResponse>("/generics", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};

export const getGeneric = async (
  id: number
): Promise<GenericResponse> => {
  const res = await apiClient.get<GenericResponse>(`/generics/${id}`);
  return res.data;
}

export const createGeneric = async (
  genericRequest: GenericRequest
): Promise<GenericSuccessResponse> => {
  const res = await apiClient.post<GenericSuccessResponse>("/generics", genericRequest);
  return res.data;
}

export const updateGeneric = async (
  id: number,
  genericRequest: GenericRequest
): Promise<GenericSuccessResponse> => {
  const res = await apiClient.patch<GenericSuccessResponse>(`/generics/${id}`, genericRequest);
  return res.data;
}

export const deleteGeneric = async (
  id: number
): Promise<GenericSuccessResponse> => {
  const res = await apiClient.delete<GenericSuccessResponse>(`/generics/${id}`);
  return res.data;
};