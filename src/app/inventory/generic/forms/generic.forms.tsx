// generic.forms.tsx
"use client";

import {
  Box,
  Button,
  MenuItem,
  TextField,
  Typography,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateGeneric,
  useGetGeneric,
  useUpdateGeneric,
} from "../query/generic.query";
import { useEffect } from "react";
import {
  genericRequestSchema,
  GenericRequest,
} from "@/app/inventory/generic/types/generic.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListDosageForm } from "@/app/inventory/dosage-form/query/dosage-form.query";
import { DosageFormPaginatedResponse, DosageFormResponse } from "@/app/inventory/dosage-form/types/dosage-form.types";
import { useListMolecular } from "@/app/inventory/molecular/query/molecular.query";
import { MolecularPaginatedResponse, MolecularResponse } from "@/app/inventory/molecular/types/molecular.types";

type Props = {
  id?: number;
};
const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];
export default function GenericForm({ id }: Props) {
  const isEditMode = !!id;
  const router = useRouter();
  const { data: genericData, isLoading } = useGetGeneric(id);
  const createMutation = useCreateGeneric();
  const updateMutation = useUpdateGeneric();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<GenericRequest>({
    resolver: zodResolver(genericRequestSchema),
    defaultValues: {
      name: "",
      //medicineName: "",
      description: "",
      dosageFormId: undefined,
      molecularId: undefined,
      unit: "",
      strength: "",
      status: "active",
      organizationId: undefined,
    },
  });

  // Reset with fetched data if editing
  useEffect(() => {
    if (isEditMode && genericData) {
      reset(genericData);
    }
  }, [isEditMode, genericData, reset]);

  const onSubmit = (formData: GenericRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, genericRequest: formData },
        {
          onSuccess: () => {
            router.push("/inventory/generic");
          },
        }
      );
    } else {
      createMutation.mutate(formData, {
        onSuccess: () => {
          reset();
          router.push("/inventory/generic");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Generic Medicine" : "Create Generic Medicine"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Name */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Name"
                  size="small"
                  fullWidth
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  required
                  autoFocus
                />
              )}
            />
          </Grid>

          {/* Dosage Form */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="dosageFormId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<DosageFormResponse, DosageFormPaginatedResponse, number>
                  label="Dosage Form"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListDosageForm}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.dosageFormId}
                  helperText={errors.dosageFormId?.message}
                />
              )}
            />
          </Grid>

          {/* Molecular */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="molecularId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<MolecularResponse, MolecularPaginatedResponse, number>
                  label="Molecular"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListMolecular}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.molecularId}
                  helperText={errors.molecularId?.message}
                />
              )}
            />
          </Grid>

          {/* Unit */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="unit"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Unit"
                  size="small"
                  fullWidth
                  error={!!errors.unit}
                  helperText={errors.unit?.message}
                  required
                />
              )}
            />
          </Grid>

          {/* Strength */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="strength"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Strength"
                  size="small"
                  fullWidth
                  error={!!errors.strength}
                  helperText={errors.strength?.message}
                  required
                />
              )}
            />
          </Grid>

          {/* Status */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            {isEditMode && (
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    {statuses.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            )}
          </Grid>

          {!isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              {!isEditMode && (
                <Controller
                  name="organizationId"
                  control={control}
                  render={({ field }) => (
                    <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                      label="Organization"
                      value={field.value}
                      onChange={field.onChange}
                      useDataQuery={useListOrganization}
                      labelKey="name"
                      valueKey="id"
                      searchable
                      searchKey="name"
                      error={!!errors.organizationId}
                      helperText={errors.organizationId?.message}
                    />
                  )}
                />
              )}
            </Grid>
          )}

          {/* Description */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Description"
                  size="small"
                  multiline
                  rows={4}
                  fullWidth
                  error={!!errors.description}
                  helperText={errors.description?.message}
                />
              )}
            />
          </Grid>
        </Grid>

        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button type="submit" variant="contained" disabled={createMutation.isPending || updateMutation.isPending}>
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}