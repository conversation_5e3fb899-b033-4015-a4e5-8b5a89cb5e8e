// generic.query.ts
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createGeneric,
  deleteGeneric,
  getGeneric,
  listGeneric,
  updateGeneric,
} from "../api/generic.api";
import {
  GenericRequest,
  GenericFilters,
  GenericPaginatedResponse,
  GenericSuccessResponse,
} from "../types/generic.types";
import { toast } from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListGeneric = (
  page?: number,
  limit?: number,
  filters: GenericFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<GenericPaginatedResponse, ApiErrorResponse>({
    queryKey: ["generics", page, limit, filterKey],
    queryFn: () => listGeneric(page, limit, filters),
  });
};
export const useGetGeneric = (id: number | undefined) => {
  return useQuery({
    queryKey: ["generic", id],
    queryFn: () => getGeneric(id!),
    enabled: !!id,
  });
};

export const useCreateGeneric = () => {
  const queryClient = useQueryClient();

  return useMutation<
    GenericSuccessResponse,
    ApiErrorResponse,
    GenericRequest
  >({
    mutationKey: ["createGeneric"],
    mutationFn: createGeneric,
    onSuccess: (data) => {
      toast.success(data.message || "Generic created successfully!");
      queryClient.invalidateQueries({ queryKey: ["generics"] });
    },
    onError: (error) => {
      toast.error(
        `Creation failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateGeneric = () => {
  const queryClient = useQueryClient();

  return useMutation<
    GenericSuccessResponse,
    ApiErrorResponse,
    { id: number; genericRequest: GenericRequest }
  >({
    mutationKey: ["updateGeneric"],
    mutationFn: ({ id, genericRequest }) => updateGeneric(id, genericRequest),
    onSuccess: (data) => {
      toast.success(data.message || "Generic updated successfully!");
      queryClient.invalidateQueries({ queryKey: ["generics"] });
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteGeneric = () => {
  const queryClient = useQueryClient();

  return useMutation<
    GenericSuccessResponse,
    ApiErrorResponse,
    number
  >({
    mutationKey: ["deleteGeneric"],
    mutationFn: deleteGeneric,
    onSuccess: (data) => {
      toast.success(data.message || "Generic deleted successfully!");
      queryClient.invalidateQueries({ queryKey: ["generics"] });
    },
    onError: (error) => {
      toast.error(
        `Deletion failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
