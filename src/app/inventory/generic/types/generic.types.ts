// generic.types.ts
import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
import { localTime } from '@/app/common/utils/serialize.utils';
import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';

export interface GenericResponse extends Record<string, unknown> {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  code: string;
  name: string;
  medicineName: string;
  description: string | null;
  dosageFormId: number;
  molecularId: number;
  unit: string;
  strength: string;
  status: 'active' | 'inactive';
  organizationId: number;
}

export const genericColumns: MRT_ColumnDef<GenericResponse>[] = [
  { accessorKey: 'id', header: 'ID', grow: false, size: 50 },
  { accessorKey: 'code', header: 'Code' },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'medicineName', header: 'Medicine Name' },
  { accessorKey: 'description', header: 'Description' },
  { accessorKey: 'dosageForm.name', header: 'Dosage Form' },
  { accessorKey: 'molecular.name', header: 'Molecular' },
  { accessorKey: 'unit', header: 'Unit' },
  { accessorKey: 'strength', header: 'Strength' },
  { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    }
  },
  { accessorKey: 'organization.name', header: 'Organization' },
  {
      accessorKey: "createdAt",
      header: "Created At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Updated At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
  { accessorKey: 'createdBy', header: 'Created By', grow: false, size: 50 },
  { accessorKey: 'updatedBy', header: 'Updated By', grow: false, size: 50 },
];

export type GenericPaginatedResponse = PaginatedResponse<GenericResponse>;
export type GenericSuccessResponse = SuccessResponse<GenericResponse>;

export const genericRequestSchema = z.object({
  name: z.string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters")
    .transform((val) => val.trim()),
  description: z.string()
    .max(255, "Description must be less than 255 characters")
    .nullable()
    .optional()
    .transform((val) => (val != null ? val.trim() : val)),
  dosageFormId: z.number()
    .int()
    .positive("Dosage form ID must be a positive number"),
  molecularId: z.number()
    .int()
    .positive("Molecular ID must be a positive number"),
  unit: z.string()
    .min(1, "Unit is required")
    .max(10, "Unit must be less than 10 characters")
    .transform((val) => val.trim()),
  strength: z.string()
    .min(1, "Strength is required")
    .max(50, "Strength must be less than 50 characters")
    .transform((val) => val.trim()),
  status: z.enum(['active', 'inactive']),
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number")
    .optional(),
});

export type GenericRequest = z.infer<typeof genericRequestSchema>;

export interface GenericFilters {
  name?: string;
  code?: string;
  medicineName?: string;
  dosageFormId?: number;
  molecularId?: number;
  status?: 'active' | 'inactive';
  organizationId?: number;
  createdBy?: number;
  updatedBy?: number;
  createdAt?: string;
  updatedAt?: string;
}