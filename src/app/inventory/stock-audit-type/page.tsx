"use client";

import { stockAuditTypeColumns, StockAuditTypeResponse } from "./types/stock-audit-type.types";
import { useListStockAuditTypes } from "./query/stock-audit-type.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteStockAuditType } from "./query/stock-audit-type.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";

function StockAuditType() {
  const router = useRouter();
  const deleteMutation = useDeleteStockAuditType();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<StockAuditTypeResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "StockAuditType",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );
  useEffect(() => {
      const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
      setRolePermissionFilters((prev) => ({
        ...prev,
        roleId,
      }));
    }, []);

  const handleOpenDelete = (row: StockAuditTypeResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow?.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<StockAuditTypeResponse>
        title="Stock Audit Types"
        columns={stockAuditTypeColumns}
        useDataQuery={useListStockAuditTypes}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/stock-audit-type/create")}
        onExport={() => console.log("Export Stock Audit Types")}
        onEdit={(row) => router.push(`/inventory/stock-audit-type/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0]?.create}
        isEdit={rolePermissionData?.data[0]?.update}
        isDelete={rolePermissionData?.data[0]?.delete}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete stock audit type{" "}
            <strong>{selectedRow?.name}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default StockAuditType;