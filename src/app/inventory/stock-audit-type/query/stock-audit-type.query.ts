import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createStockAuditType,
  deleteStockAuditType,
  getStockAuditType,
  listStockAuditTypes,
  updateStockAuditType,
} from "../api/stock-audit-type.api";
import { StockAuditTypeRequest, StockAuditTypeFilters, StockAuditTypeSuccessResponse } from "../types/stock-audit-type.types";
import { toast } from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListStockAuditTypes = (
  page?: number,
  limit?: number,
  filters: StockAuditTypeFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery({
    queryKey: ["stock-audit-types", page, limit, filterKey],
    queryFn: () => listStockAuditTypes(page, limit, filters),
  });
};

export const useGetStockAuditType = (id: number | undefined) => {
  return useQuery({
    queryKey: ["stock-audit-type", id],
    queryFn: () => getStockAuditType(id!),
    enabled: !!id,
  });
};

export const useCreateStockAuditType = () => {
  const queryClient = useQueryClient();

  return useMutation<
    StockAuditTypeSuccessResponse,
    ApiErrorResponse,
    StockAuditTypeRequest
  >({
    mutationKey: ["createStockAuditType"],
    mutationFn: createStockAuditType,
    onSuccess: (data) => {
      toast.success(data.message || "Stock Audit Type created successfully!");
      queryClient.invalidateQueries({ queryKey: ["stock-audit-types"] });
    },
    onError: (error) => {
      toast.error(
        `Creation failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateStockAuditType = () => {
  const queryClient = useQueryClient();

  return useMutation<
    StockAuditTypeSuccessResponse,
    ApiErrorResponse,
    { id: number; stockAuditTypeRequest: StockAuditTypeRequest }
  >({
    mutationKey: ["updateStockAuditType"],
    mutationFn: ({ id, stockAuditTypeRequest }) => updateStockAuditType(id, stockAuditTypeRequest),
    onSuccess: (data) => {
      toast.success(data.message || "Stock Audit Type updated successfully!");
      queryClient.invalidateQueries({ queryKey: ["stock-audit-types"] });
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteStockAuditType = () => {
  const queryClient = useQueryClient();

  return useMutation<StockAuditTypeSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteStockAuditType"],
    mutationFn: deleteStockAuditType,
    onSuccess: (data) => {
      toast.success(data.message || "Stock Audit Type deleted successfully!");
      queryClient.invalidateQueries({ queryKey: ["stock-audit-types"] });
    },
    onError: (error) => {
      toast.error(
        `Deletion failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};