import apiClient from "@/app/api/api";
import { 
  StockAuditTypePaginatedResponse, 
  StockAuditTypeResponse, 
  StockAuditTypeSuccessResponse, 
  StockAuditTypeRequest,
  StockAuditTypeFilters 
} from "../types/stock-audit-type.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listStockAuditTypes = async (
  page?: number,
  limit?: number,
  filters?: StockAuditTypeFilters
): Promise<StockAuditTypePaginatedResponse> => {
  const res = await apiClient.get<StockAuditTypePaginatedResponse>("/stock-audit-types", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });
  return res.data;
};

export const getStockAuditType = async (
  id: number
): Promise<StockAuditTypeResponse> => {
  const res = await apiClient.get<StockAuditTypeResponse>(`/stock-audit-types/${id}`);
  return res.data;
}

export const createStockAuditType = async (
  stockAuditTypeRequest: StockAuditTypeRequest
): Promise<StockAuditTypeSuccessResponse> => {
  const res = await apiClient.post<StockAuditTypeSuccessResponse>("/stock-audit-types", stockAuditTypeRequest);
  return res.data;
}

export const updateStockAuditType = async (
  id: number,
  stockAuditTypeRequest: StockAuditTypeRequest
): Promise<StockAuditTypeSuccessResponse> => {
  const res = await apiClient.patch<StockAuditTypeSuccessResponse>(`/stock-audit-types/${id}`, stockAuditTypeRequest);
  return res.data;
}

export const deleteStockAuditType = async (
  id: number
): Promise<StockAuditTypeSuccessResponse> => {
  const res = await apiClient.delete<StockAuditTypeSuccessResponse>(`/stock-audit-types/${id}`);
  return res.data;
};