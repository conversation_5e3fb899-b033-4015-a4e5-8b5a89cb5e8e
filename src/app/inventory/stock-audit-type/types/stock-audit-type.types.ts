import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from '@/app/common/utils/serialize.utils';

export interface StockAuditTypeResponse extends Record<string, unknown> {
  id: number;
  name: string;
  description: string | null;
  status: 'active' | 'inactive';
  organizationId: number;
}

export const stockAuditTypeColumns: MRT_ColumnDef<StockAuditTypeResponse>[] = [
  { accessorKey: 'id', header: 'ID', grow: false, size: 50 },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'description', header: 'Description' },
  { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    } 
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: 'organization.name', header: 'Organization' },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type StockAuditTypePaginatedResponse = PaginatedResponse<StockAuditTypeResponse>;
export type StockAuditTypeSuccessResponse = SuccessResponse<StockAuditTypeResponse>;

export const stockAuditTypeRequestSchema = z.object({
  name: z.string().trim().min(1, "Name is required"),
  description: z.string().trim().nullable().optional(),
  status: z.enum(['active', 'inactive']),
  organizationId: z.number().int().positive("Organization ID must be a positive number").optional(),
});

export type StockAuditTypeRequest = z.infer<typeof stockAuditTypeRequestSchema>;

export interface StockAuditTypeFilters {
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  name?: string;
  status?: "active" | "inactive";
  organizationId?: number;
};