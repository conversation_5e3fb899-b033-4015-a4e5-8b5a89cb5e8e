"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uI<PERSON>,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useCreateStockAuditType, useGetStockAuditType, useUpdateStockAuditType } from "../query/stock-audit-type.query";
import { useRef, useEffect } from "react";
import {
  stockAuditTypeRequestSchema,
  StockAuditTypeRequest,
} from "@/app/inventory/stock-audit-type/types/stock-audit-type.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function StockAuditTypeForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: stockAuditTypeData, isLoading } = useGetStockAuditType(id);
  const createMutation = useCreateStockAuditType();
  const updateMutation = useUpdateStockAuditType();

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<StockAuditTypeRequest>({
    resolver: zodResolver(stockAuditTypeRequestSchema),
    defaultValues: {
      name: "",
      description: null,
      status: "active",
      organizationId: undefined,
    },
  });

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isEditMode && stockAuditTypeData) {
      reset({
        name: stockAuditTypeData.name,
        description: stockAuditTypeData.description,
        status: stockAuditTypeData.status,
        organizationId: stockAuditTypeData.organizationId,
      });
    }
  }, [isEditMode, stockAuditTypeData, reset]);

  const onSubmit = (formData: StockAuditTypeRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, stockAuditTypeRequest: formData },
        {
          onSuccess: () => {
            router.push("/inventory/stock-audit-type");
          },
        }
      );
    } else {
      createMutation.mutate(formData, {
        onSuccess: () => {
          reset();
          router.push("/inventory/stock-audit-type");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

return (
  <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
    <Typography variant="h6" mb={3}>
      {isEditMode ? "Edit Stock Audit Type" : "Create Stock Audit Type"}
    </Typography>

    <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
      <Grid container spacing={2}>
        {/* Name */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                inputRef={nameRef}
                label="Name"
                size="small"
                fullWidth
                {...field}
                error={!!errors.name}
                helperText={errors.name?.message}
                autoFocus
                required
              />
            )}
          />
        </Grid>
        
        {/* Status (Edit Mode Only) */}
        {isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
                <Controller
                name="status"
                control={control}
                render={({ field }) => (
                    <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                    >
                    {statuses.map((opt) => (
                        <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                        </MenuItem>
                    ))}
                    </TextField>
                )}
                />
            </Grid>
            )}

        {/* Organization (Create Mode Only) */}
        {!isEditMode && (
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
            name="organizationId"
            control={control}
            render={({ field }) => (
                <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                label="Organization"
                value={field.value}
                onChange={field.onChange}
                useDataQuery={useListOrganization}
                labelKey="name"
                valueKey="id"
                searchable
                searchKey="name"
                error={!!errors.organizationId}
                helperText={errors.organizationId?.message}
                />
            )}
            />
        </Grid>
        )}

        {/* Description */}
        <Grid size={{ xs: 12 }}>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <TextField
                label="Description"
                size="small"
                multiline
                rows={4}
                fullWidth
                {...field}
                value={field.value || ""}
                error={!!errors.description}
                helperText={errors.description?.message}
              />
            )}
          />
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box
        sx={{
          display: "flex",
          gap: 2,
          justifyContent: "flex-end",
          mt: 3,
        }}
      >
        <Button
          variant="outlined"
          color="inherit"
          onClick={() => router.push("/inventory/stock-audit-type")}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={createMutation.isPending || updateMutation.isPending}
        >
          {isEditMode ? "Update" : "Create"}
        </Button>
      </Box>
    </Box>
  </Card>
);
}