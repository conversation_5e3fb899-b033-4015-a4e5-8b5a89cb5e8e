"use client";

import { useEffect, useState } from "react";
import { Box } from "@mui/material";
import { Check, Pencil, Trash } from "lucide-react";
import { VendorQuotationResponse } from "../types/vendor-quotations.types";
import { VendorQuotationColumns } from "../types/vendor-quotations.fields";
import { useCreateVendorQuotation, useDeleteVendorQuotation, useListVendorQuotations, useUpdateVendorQuotation } from "../query/vendor-quotations.query";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";

function getValueByAccessor<T>(row: T, accessor: string): unknown {
  return accessor.split(".").reduce<unknown>((acc, key) => {
    if (acc && typeof acc === "object" && key in acc) {
      return (acc as Record<string, unknown>)[key];
    }
    return undefined;
  }, row);
}

function VendorQuotationForm({ itemId }: { itemId: number }) {
  const createMutation = useCreateVendorQuotation();
  const [editableRowIndex, setEditableRowIndex] = useState<number | null>(null);
  const updateMutation = useUpdateVendorQuotation(); // assume you already have this mutation set up
  const [deleteIndex, setDeleteIndex] = useState<number | null>(null);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const deleteMutation = useDeleteVendorQuotation();

  // Step 1: Fetch real data from backend
  const { data, isLoading } = useListVendorQuotations(1, 10, { itemId });
 // Page 1, limit 10

  // Step 2: Local editable state
  const [rows, setRows] = useState<VendorQuotationResponse[]>([]);

  // Step 3: Sync API data into local rows
  useEffect(() => {
    if (data && data.data.length > 0) {
      setRows(data.data);
    } else {
      // If no data, show an empty row
      const emptyRow: VendorQuotationResponse = {
        id: 0,
        itemId,
        isDefault: false,
        organizationId: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: 0,
        updatedBy: 0,
        offerTermId: 0,
        vendorId: 0,
        itemUomId: 0,
        purchaseRate: 0,
        ucp: 0,
        status: "active"
      };
      setRows([emptyRow]);
    }
  }, [data, itemId]);

  const columns = VendorQuotationColumns(rows, setRows, createMutation, editableRowIndex);

  if (isLoading) {
    return <p className="text-sm text-gray-500">Loading Item UOMs...</p>;
  }

  return (
    <Box
      sx={{
        backgroundColor: "#f9f9f9",
        borderRadius: 2,
        boxShadow: 2,
        maxWidth: "100%",
        margin: "auto",
        my: 2,
      }}
    >
      <Box className="flex justify-end mb-2">
      <button
        onClick={() => {
          const newRow: VendorQuotationResponse = {
            id: 0,
            itemId,
            isDefault: false,
            organizationId: 0,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: 0,
            updatedBy: 0,
            offerTermId: 0,
            vendorId: 0,
            itemUomId: 0,
            purchaseRate: 0,
            ucp: 0,
            status: "active"
          };
          setRows(prev => [...prev, newRow]);
        }}
        className="bg-[#3A59D1] text-white px-3 py-1 rounded text-xs hover:bg-[#2f48a1]"
      >
        + Add Vendor Quotation
      </button>
    </Box>

    <Box sx={{ overflowX: "auto" }}>
      <table className="min-w-full border text-xs">
        <thead>
          <tr className="bg-[#3A59D1] text-white">
            {columns.map((column, index) => (
              <th key={index} className="border px-2 py-1 text-left">
                {column.header}
              </th>
            ))}
            <th className="border px-2 py-1 text-left">Actions</th>
          </tr>
        </thead>
        <tbody>
          {rows.map((row, rowIndex) => (
            <tr key={rowIndex}>
              {columns.map((column, colIndex) => (
                <td key={colIndex} className="border px-2 py-1">
                  {column.Edit
                    ? column.Edit(rowIndex)
                    : String(getValueByAccessor(row, column.accessorKey) ?? "")}
                </td>
              ))}
              <td className="border px-2 py-1">
                <div className="flex items-center gap-2">
                  {editableRowIndex === rowIndex ? (
                    <button
                      onClick={() => {
                        const row = rows[rowIndex];
                        updateMutation.mutate(
                          {
                            id: row.id,
                            vendorQuotationRequest: {
                              itemId: row.itemId,
                              offerTermId: row.offerTermId,
                              vendorId: row.vendorId,
                              itemUomId: row.itemUomId,
                              purchaseRate: row.purchaseRate,
                              sellingRate: row.sellingRate,
                              leadTimeDuration: row.leadTimeDuration,
                              isDefault: row.isDefault,
                              ucp: row.ucp,
                              preferenceOrder: row.preferenceOrder,
                              status: row.status,
                              organizationId: row.organizationId,
                            }
                          },
                          {
                            onSuccess: () => {
                              setEditableRowIndex(null);
                            },
                          }
                        );
                      }}
                      className="text-green-600 hover:text-green-800"
                      title="Save"
                    >
                      <Check  size = {16} />
                    </button>
                  ) : (
                    <button
                      onClick={() => setEditableRowIndex(rowIndex)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Edit"
                    >
                      <Pencil size = {16} />
                    </button>
                  )}
                  <button
                    onClick={() => {
                      setDeleteIndex(rowIndex);
                      setConfirmOpen(true);
                    }}
                    className="text-red-500 hover:text-red-700"
                    title="Delete"
                  >
                    <Trash size={16} />
                  </button>
                </div>

              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </Box>
    
      <Dialog open={confirmOpen} onClose={() => setConfirmOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this Vendor Quotations mapping?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmOpen(false)}>Cancel</Button>
          <Button
            onClick={() => {
              if (deleteIndex === null) return;

              const row = rows[deleteIndex];
              setConfirmOpen(false);

              if (row.id === 0) {
                setRows(prev => prev.filter((_, i) => i !== deleteIndex));
              } else {
                deleteMutation.mutate(row.id, {
                  onSuccess: () => {
                    setRows(prev => prev.filter((_, i) => i !== deleteIndex));
                  },
                });
              }

              setDeleteIndex(null);
            }}
            color="error"
            variant="contained"
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

    </Box>
  );
}

export default VendorQuotationForm;
