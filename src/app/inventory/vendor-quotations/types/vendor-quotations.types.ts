import { z } from "zod";
import { BaseResponse, PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { OrganizationResponse } from '@/app/organization/organization/types/organization.types';
import { OfferTermsResponse } from '../../offer-terms/types/offer-terms.types';
import { VendorResponse } from '../../vendors/types/vendor.types';
import { ItemUOMResponse } from '../../item-uom/types/item-uom.types';

export interface VendorQuotationProps {
  vendorId?: number;
  itemId?: number;
}

export interface VendorQuotationResponse extends BaseResponse , Record<string, unknown>{
  itemId: number;
  offerTermId: number;
  vendorId: number;
  itemUomId: number;
  purchaseRate: number;
  sellingRate?: number;
  leadTimeDuration?: number;
  isDefault: boolean;
  ucp: number;
  preferenceOrder?: number;
  status: 'active' | 'inactive';
  organizationId: number;
  organization?: OrganizationResponse;
  offerTerm?: OfferTermsResponse;
  vendor?: VendorResponse;
  itemUom?: ItemUOMResponse;
}

export type VendorQuotationPaginatedResponse = PaginatedResponse<VendorQuotationResponse>;
export type VendorQuotationSuccessResponse = SuccessResponse<VendorQuotationResponse>;

export const vendorQuotationRequestSchema = z.object({
  itemId: z.number().int().positive("Item ID must be a positive number"),
  offerTermId: z.number().int().positive("Offer Term ID must be a positive number"),
  vendorId: z.number().int().positive("Vendor ID must be a positive number"),
  purchaseRate: z.number().positive("Purchase rate must be a positive number"),
  sellingRate: z.number().positive("Selling rate must be a positive number").optional(),
  leadTimeDuration: z.number().int().nonnegative("Lead time must be a non-negative integer").optional(),
  isDefault: z.boolean(),
  ucp: z.number().int().nonnegative("Unit cost price must be a non-negative integer"),
  preferenceOrder: z.number().int().nonnegative("Preference order must be a non-negative integer").optional(),
  status: z.enum(['active', 'inactive']),
  organizationId: z.number().int().positive("Organization ID must be a positive number"),
  itemUomId: z.number().int().positive("Item UOM ID must be a positive number"),
});

export type VendorQuotationRequest = z.infer<typeof vendorQuotationRequestSchema>;

export interface VendorQuotationFilters {
  itemId?: number;
  offerTermId?: number;
  vendorId?: number;
  purchaseRateMin?: number;
  purchaseRateMax?: number;
  status?: 'active' | 'inactive';
  organizationId?: number;
  itemUomId?: number;
  createdBy?: number;
  updatedBy?: number;
  createdAt?: string; // Consider using date range in real apps
  updatedAt?: string;
}