"use client";

import { DynamicColumn } from "@/app/common/types/common.types";
import { TextField, MenuItem, Box } from "@mui/material";
import { statusOptions, statusMap, getStatusLabel } from "@/app/common/types/status.types";
import { MRT_ColumnDef } from "material-react-table";
import { localTime } from "@/app/common/utils/serialize.utils";
import { useCreateVendorQuotation } from "../query/vendor-quotations.query";
import { CommonAutoComplete } from "@/app/common/dropdown/CommonAutoComplete";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { VendorQuotationResponse } from "./vendor-quotations.types";
import { OfferTermsResponse } from "../../offer-terms/types/offer-terms.types";
import { useListOfferTerms } from "../../offer-terms/query/offer-terms.query";
import { useListVendors } from "../../vendors/query/vendor.query";
import { VendorResponse } from "../../vendors/types/vendor.types";
import { InputNumber, Switch } from "antd";
import { ItemUOMResponse } from "../../item-uom/types/item-uom.types";
import { useListItemUOM } from "../../item-uom/query/item-uom.query";

export const VendorQuotationColumns = (
  rows: VendorQuotationResponse[] = [], 
  setRows: React.Dispatch<React.SetStateAction<VendorQuotationResponse[]>>=()=>{},
  createMutation: ReturnType<typeof useCreateVendorQuotation>,
  editableRowIndex: number | null
): DynamicColumn[] => {
  const handleChange = (
    index: number,
    field: keyof VendorQuotationResponse,
    value: unknown
  ) => {
    const updated = [...rows];
    updated[index][field] = value;
    setRows(updated);
  };

  function addRows() {
    const VendorQuotation: VendorQuotationResponse = {
        id: 0,
        itemId: 0,
        status: "active",
        organizationId: 0,
        createdAt: "",
        updatedAt: "",
        createdBy: 0,
        updatedBy: 0,
        offerTermId: 0,
        vendorId: 0,
        itemUomId: 0,
        purchaseRate: 0,
        sellingRate: 0,
        isDefault: false,
        ucp: 0,
        itemUom: undefined
    };
    setRows([...rows, VendorQuotation]);
  }

  return [
    { accessorKey: "id", header: "ID", isEditable: false },
    {
          accessorKey: "offerTerm.name",
          header: "Offer Terms",
          Edit: (rowIndex: number) => {
            const row = rows[rowIndex];
            if (row.id !== 0 && editableRowIndex !== rowIndex)
              return <span>{row.offerTerm?.name || ""}</span>;
    
            return (
              <CommonAutoComplete<OfferTermsResponse, number>
                value={row.offerTermId}
                onChange={(val) => handleChange(rowIndex, "offerTermId", val || 0)}
                useDataQuery={useListOfferTerms}
                labelKey="name"
                valueKey="id"
              />
            );
          },
    },
    {
        accessorKey: "vendor.name",
        header: "Vendor",
        Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
            return <span>{row.vendor?.name || ""}</span>;

        return (
            <CommonAutoComplete<VendorResponse, number>
            value={row.vendorId}
            onChange={(val) => handleChange(rowIndex, "vendorId", val || 0)}
            useDataQuery={useListVendors}
            labelKey="name"
            valueKey="id"
            />
        );
        },
    },
    {
        accessorKey: "itemUom.name",
        header: "Item UOM",
        Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
            return <span>{typeof row.itemUom?.name === "string" ? row.itemUom.name : ""}</span>;


        return (
            <CommonAutoComplete<ItemUOMResponse, number>
            value={row.itemUomId}
            onChange={(val) => handleChange(rowIndex, "itemUomId", val || 0)}
            useDataQuery={useListItemUOM}
            labelKey="name"
            valueKey="id"
            />
        );
        },
    },
    {
          accessorKey: "purchaseRate",
          header: "Purchase Rate",
          Edit: (rowIndex: number) => {
            const row = rows[rowIndex];
            if (row.id !== 0 && editableRowIndex !== rowIndex)
              return <span>{row.purchaseRate}</span>;
    
            return (
              <InputNumber
                min={0}
                value={row.purchaseRate}
                onChange={(val) => handleChange(rowIndex, "purchaseRate", val)}
              />
            );
          },
    },
    {
          accessorKey: "sellingRate",
          header: "Selling Rate",
          Edit: (rowIndex: number) => {
            const row = rows[rowIndex];
            if (row.id !== 0 && editableRowIndex !== rowIndex)
              return <span>{row.sellingRate}</span>;
    
            return (
              <InputNumber
                min={0}
                value={row.sellingRate}
                onChange={(val) => handleChange(rowIndex, "sellingRate", val)}
              />
            );
          },
    },
    {
          accessorKey: "leadTimeDuration",
          header: "Lead Time Duration",
          Edit: (rowIndex: number) => {
            const row = rows[rowIndex];
            if (row.id !== 0 && editableRowIndex !== rowIndex)
              return <span>{row.leadTimeDuration}</span>;
    
            return (
              <InputNumber
                min={0}
                value={row.leadTimeDuration}
                onChange={(val) => handleChange(rowIndex, "leadTimeDuration", val)}
              />
            );
          },
    },
    {
          accessorKey: "ucp",
          header: "UCP",
          Edit: (rowIndex: number) => {
            const row = rows[rowIndex];
            if (row.id !== 0 && editableRowIndex !== rowIndex)
              return <span>{row.ucp}</span>;
    
            return (
              <InputNumber
                min={0}
                value={row.ucp}
                onChange={(val) => handleChange(rowIndex, "ucp", val)}
              />
            );
          },
    },
    {
      accessorKey: "isDefault",
      header: "Is Default",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.isDefault ? "Yes" : "No"}</span>;

        return (
          <Switch
            checked={row.isDefault}
            onChange={(checked: unknown) =>
              handleChange(rowIndex, "isDefault", checked)
            }
          />
        );
      },
    },
    {
          accessorKey: "preferenceOrder",
          header: "Preference Order",
          Edit: (rowIndex: number) => {
            const row = rows[rowIndex];
            if (row.id !== 0 && editableRowIndex !== rowIndex)
              return <span>{row.preferenceOrder}</span>;
    
            return (
              <InputNumber
                min={0}
                value={row.preferenceOrder}
                onChange={(val) => handleChange(rowIndex, "preferenceOrder", val)}
              />
            );
          },
    },
    {
      accessorKey: "status",
      header: "Status",
      Edit: (rowIndex: number) => (
        <TextField
          select
          size="small"
          value={rows[rowIndex].status}
          onChange={(e) => handleChange(rowIndex, "status", e.target.value)}
          fullWidth
        >
          {statusOptions.map((val) => (
            <MenuItem key={val} value={val}>
              {statusMap[val]}
            </MenuItem>
          ))}
        </TextField>
      ),
    },
    {
        accessorKey: "organization.name",
        header: "Organization",
        Edit: (rowIndex: number) => {
          const row = rows[rowIndex];
          const isNewRow = row.id === 0;
          const isLastRow = rowIndex === rows.length - 1;

          const isEditable = editableRowIndex === rowIndex || isNewRow;
          if (!isEditable) return <span>{row.organization?.name || ""}</span>;
          
          if (isNewRow) {
            return (
              <Box
                onKeyDown={async (e) => {
                  if (
                    e.key === "Tab" &&
                    !e.shiftKey &&
                    isLastRow &&
                    isNewRow
                  ) 
                  {
                    const payload = {
                      itemId: row.itemId,
                      offerTermId: row.offerTermId,
                      vendorId:row.vendorId,
                      purchaseRate: row.purchaseRate,
                      sellingRate: row.sellingRate,
                      leadTimeDuration: row.leadTimeDuration,
                      isDefault: row.isDefault,
                      ucp: row.ucp,
                      preferenceOrder: row.preferenceOrder,
                      itemUomId: row.itemUomId,
                      status: row.status,
                      organizationId: row.organizationId,
                    };

                    createMutation.mutate(payload, {
                      onSuccess: () => {
                        setTimeout(() => addRows(), 0);
                      },
                    });
                  }
                }}
              >
                <CommonAutoComplete<OrganizationResponse, number>
                  value={row.organizationId}
                  onChange={(val) =>
                    handleChange(rowIndex, "organizationId", val || 0)
                  }
                  useDataQuery={useListOrganization}
                  labelKey="name"
                  valueKey="id"
                />
              </Box>
            );
          }
          return null;
        },
      }
  ];
};

export const vendorQuotationColumns: MRT_ColumnDef<VendorQuotationResponse>[] = [
  { accessorKey: 'id', header: 'ID' ,grow: false, size: 50},
  { accessorKey: 'item.name', header: 'Item' },
  { accessorKey: 'vendor.name', header: 'Vendor' },
  { accessorKey: 'offerTerm.name', header: 'Offer Term' },
  { accessorKey: 'purchaseRate', header: 'Purchase Rate' },
  { accessorKey: 'sellingRate', header: 'Selling Rate' },
  { accessorKey: 'leadTimeDuration', header: 'Lead Time (days)' },
  { accessorKey: 'isDefault', header: 'Is Default' },
  { accessorKey: 'ucp', header: 'Unit Cost Price' },
  { accessorKey: 'preferenceOrder', header: 'Preference Order' },
  { accessorKey: 'itemUom.name', header: 'Item UOM' },
    { 
            accessorKey: 'status', 
            header: 'Status',
            filterVariant: 'select',
            filterSelectOptions: statusOptions.map(value => ({
              value,
              label: statusMap[value], // Capitalize first letter
            })),
            Cell: ({ cell }) => {
              const status = cell.getValue<string>();
              return getStatusLabel(status);
            } 
    },
    {
        accessorKey: "createdAt",
        header: "Created At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
      {
        accessorKey: "updatedAt",
        header: "Updated At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
    { accessorKey: 'organization.name', header: 'Organization' },
];