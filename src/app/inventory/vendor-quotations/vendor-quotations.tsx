"use client";

import { VendorQuotationResponse } from "./types/vendor-quotations.types";
import { vendorQuotationColumns } from "./types/vendor-quotations.fields";
import { useListVendorQuotations } from "./query/vendor-quotations.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteVendorQuotation } from "./query/vendor-quotations.query";

import { VendorQuotationProps } from "./types/vendor-quotations.types";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";

function VendorQuotationsPage({ vendorId }: VendorQuotationProps) {
  const router = useRouter();
  const deleteMutation = useDeleteVendorQuotation();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<VendorQuotationResponse | null>(null);

  const [rolePermissionFilters, setRolePermissionFilters] =
      useState<RolePermissionFilters>({
        moduleName: "Inventory",
        featureName: "VendorQuotation",
        roleId: 0,
      });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
      const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
      setRolePermissionFilters((prev) => ({
        ...prev,
        roleId,
      }));
  }, []);
  
  const handleOpenDelete = (row: VendorQuotationResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<VendorQuotationResponse>
        title="Vendor Quotations"
        columns={vendorQuotationColumns}
        useDataQuery={useListVendorQuotations}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/vendor-quotations/create")}
        onExport={() => console.log("Export Vendor Quotations")}
        onEdit={(row) => router.push(`/inventory/vendor-quotations/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0]?.create}
        isEdit={rolePermissionData?.data[0]?.update}
        isDelete={rolePermissionData?.data[0]?.delete}
        isFilterable={true}
        initialFilters={[{ id: "vendorId", value: vendorId }]}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this Vendor Quotation?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default VendorQuotationsPage;