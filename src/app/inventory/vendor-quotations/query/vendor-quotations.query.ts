import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createVendorQuotation,
  deleteVendorQuotation,
  getVendorQuotation,
  listVendorQuotations,
  updateVendorQuotation,
} from "../api/vendor-quotations.api";
import {
  VendorQuotationFilters,
  VendorQuotationRequest,
  VendorQuotationSuccessResponse,
} from "../types/vendor-quotations.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";


export const useListVendorQuotations = (
  page?: number,
  limit?: number,
  filters: VendorQuotationFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery({
    queryKey: ["vendor-quotations", page, limit, filterKey],
    queryFn: () => listVendorQuotations(page, limit, filters),
  });
};

export const useGetVendorQuotation = (id: number | undefined) => {
  return useQuery({
    queryKey: ["vendor-quotation", id],
    queryFn: () => getVendorQuotation(id!),
    enabled: !!id, // only run if id is defined
  });
};

export const useCreateVendorQuotation = () => {
  const queryClient = useQueryClient();

  return useMutation<
    VendorQuotationSuccessResponse,
    ApiErrorResponse,
    VendorQuotationRequest
  >({
    mutationKey: ["createVendorQuotation"],
    mutationFn: (vendorQuotationRequest: VendorQuotationRequest) => 
      createVendorQuotation(vendorQuotationRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["vendor-quotations"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateVendorQuotation = () => {
  const queryClient = useQueryClient();

  return useMutation<
    VendorQuotationSuccessResponse, // The wrapped response from backend
    ApiErrorResponse,
    { id: number, vendorQuotationRequest: VendorQuotationRequest }
  >({
    mutationKey: ["updateVendorQuotation"],
    mutationFn: ({ id, vendorQuotationRequest }: { id: number; vendorQuotationRequest: VendorQuotationRequest }) =>
      updateVendorQuotation(id, vendorQuotationRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["vendor-quotations"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteVendorQuotation = () => {
  const queryClient = useQueryClient();

  return useMutation<
    VendorQuotationSuccessResponse, // Actual API response type
    ApiErrorResponse,
    number // Mutation input is just the ID
  >({
    mutationKey: ["deleteVendorQuotation"],
    mutationFn: (id: number) => deleteVendorQuotation(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["vendor-quotations"] });
      toast.success(data.message || "Vendor quotation deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
