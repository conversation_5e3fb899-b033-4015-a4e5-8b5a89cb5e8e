import apiClient from "@/app/api/api";
import { 
  VendorQuotationFilters,
  VendorQuotationPaginatedResponse, 
  VendorQuotationResponse, 
  VendorQuotationSuccessResponse, 
  VendorQuotationRequest 
} from "../types/vendor-quotations.types";

import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listVendorQuotations = async (
  page?: number,
  limit?: number,
  filters?: VendorQuotationFilters,
): Promise<VendorQuotationPaginatedResponse> => {
  const res = await apiClient.get<VendorQuotationPaginatedResponse>("/vendor-quotations", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string,unknown>),
    },
  });
  return res.data;
};

export const getVendorQuotation = async (
  id: number
): Promise<VendorQuotationResponse> => {
  const res = await apiClient.get<VendorQuotationResponse>(`/vendor-quotations/${id}`);
  return res.data;
}

export const createVendorQuotation = async (
  vendorQuotationRequest: VendorQuotationRequest
): Promise<VendorQuotationSuccessResponse> => {
  const res = await apiClient.post<VendorQuotationSuccessResponse>("/vendor-quotations", vendorQuotationRequest);
  return res.data;
}

export const updateVendorQuotation = async (
  id: number,
  vendorQuotationRequest: VendorQuotationRequest
): Promise<VendorQuotationSuccessResponse> => {
  const res = await apiClient.patch<VendorQuotationSuccessResponse>(`/vendor-quotations/${id}`, vendorQuotationRequest);
  return res.data;
}

export const deleteVendorQuotation = async (
  id: number
): Promise<VendorQuotationSuccessResponse> => {
  const res = await apiClient.delete<VendorQuotationSuccessResponse>(`/vendor-quotations/${id}`);
  return res.data;
};