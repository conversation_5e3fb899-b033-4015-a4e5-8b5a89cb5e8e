import apiClient from "@/app/api/api";
import { 
  ItemUOMFilters,
  ItemUOMPaginatedResponse, 
  ItemUOMResponse, 
  ItemUOMSuccessResponse, 
  ItemUOMRequest 
} from "../types/item-uom.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listItemUOM = async (
  page?: number,
  limit?: number,
  filters?: ItemUOMFilters,
): Promise<ItemUOMPaginatedResponse> => {
  const res = await apiClient.get<ItemUOMPaginatedResponse>("/item-uoms", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string,unknown>),
    },
  });
  return res.data;
};

export const getItemUOM = async (
  id: number
): Promise<ItemUOMResponse> => {
  const res = await apiClient.get<ItemUOMResponse>(`/item-uoms/${id}`);
  return res.data;
}

export const createItemUOM = async (
  itemUOMRequest: ItemUOMRequest
): Promise<ItemUOMSuccessResponse> => {
  const res = await apiClient.post<ItemUOMSuccessResponse>("/item-uoms", itemUOMRequest);
  return res.data;
}

export const updateItemUOM = async (
  id: number,
  itemUOMRequest: ItemUOMRequest
): Promise<ItemUOMSuccessResponse> => {
  const res = await apiClient.patch<ItemUOMSuccessResponse>(`/item-uoms/${id}`, itemUOMRequest);
  return res.data;
}

export const deleteItemUOM = async (
  id: number
): Promise<ItemUOMSuccessResponse> => {
  const res = await apiClient.delete<ItemUOMSuccessResponse>(`/item-uoms/${id}`);
  return res.data;
};