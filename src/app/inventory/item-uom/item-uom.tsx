"use client";

import { ItemUOMResponse } from "./types/item-uom.types";
import {itemUOMColumns} from "./types/item-uom.fields"
import { useListItemUOM } from "./query/item-uom.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteItemUOM } from "./query/item-uom.query";

import { ItemUomProps } from "./types/item-uom.types";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";

function ItemUOM({ itemId }: ItemUomProps) {
  const router = useRouter();
  const deleteMutation = useDeleteItemUOM();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<ItemUOMResponse | null>(null);

  const [rolePermissionFilters, setRolePermissionFilters] =
      useState<RolePermissionFilters>({
        moduleName: "Inventory",
        featureName: "ItemUOM",
        roleId: 0,
      });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
      const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
      setRolePermissionFilters((prev) => ({
        ...prev,
        roleId,
      }));
  }, []);
  
  
  const handleOpenDelete = (row: ItemUOMResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<ItemUOMResponse>
        title="Item UOM"
        columns={itemUOMColumns}
        useDataQuery={useListItemUOM}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/item-uom/create")}
        onExport={() => console.log("Export Item UOM")}
        onEdit={(row) => router.push(`/inventory/item-uom/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}
        isFilterable={true}
        initialFilters={[{ id: "itemId", value: itemId }]}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this Item UOM mapping?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default ItemUOM;

