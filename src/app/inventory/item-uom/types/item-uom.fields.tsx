"use client";

import { ItemUOMResponse } from "@/app/inventory/item-uom/types/item-uom.types";
import { useListUom } from "../../uom/query/uom.query";
import { UomResponse } from "../../uom/types/uom.types";
import { CommonAutoComplete } from "@/app/common/dropdown/CommonAutoComplete";
import { InputNumber, Switch } from "antd";
import { MRT_ColumnDef } from "material-react-table";
import { DynamicColumn } from "@/app/common/types/common.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { useCreateItemUOM } from "../query/item-uom.query";
import { OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";

export const ItemUOMColumns = (
  rows: ItemUOMResponse[] = [],
  setRows: React.Dispatch<React.SetStateAction<ItemUOMResponse[]>> = () => {},
  createMutation: ReturnType<typeof useCreateItemUOM>,
  editableRowIndex: number | null
): DynamicColumn[] => {

  const handleChange = (
    index: number,
    field: keyof ItemUOMResponse,
    value: unknown
  ) => {
    if (rows) {
      const updated = [...rows];
      updated[index][field] = value;
      setRows(updated);
    }
  };

  function addRows() {
    const itemUOM: ItemUOMResponse = {
      id: 0,
      itemId: 0,
      uomId: 0,
      conversionCount: 0,
      availableCount: 0,
      reOrderCount: 0,
      isDefault: false,
      organizationId: 0,
      createdAt: "",
      updatedAt: "",
      createdBy: 0,
      updatedBy: 0,
    };
    setRows([...rows, itemUOM]);
  }

  return [
    { accessorKey: "id", header: "ID", isEditable: false },

    {
      accessorKey: "uom.name",
      header: "UOM",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.uom?.uomName || ""}</span>;

        return (
          <CommonAutoComplete<UomResponse, number>
            value={row.uomId}
            onChange={(val) => handleChange(rowIndex, "uomId", val || 0)}
            useDataQuery={useListUom}
            labelKey="name"
            valueKey="id"
          />
        );
      },
    },

    {
      accessorKey: "conversionCount",
      header: "Conversion Count",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.conversionCount}</span>;

        return (
          <InputNumber
            min={0}
            value={row.conversionCount}
            onChange={(val) => handleChange(rowIndex, "conversionCount", val)}
          />
        );
      },
    },

    {
      accessorKey: "availableCount",
      header: "Available Count",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.availableCount}</span>;

        return (
          <InputNumber
            min={0}
            value={row.availableCount}
            onChange={(val) => handleChange(rowIndex, "availableCount", val)}
          />
        );
      },
    },

    {
      accessorKey: "isDefault",
      header: "Is Default",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        if (row.id !== 0 && editableRowIndex !== rowIndex)
          return <span>{row.isDefault ? "Yes" : "No"}</span>;

        return (
          <Switch
            checked={row.isDefault}
            onChange={(checked) =>
              handleChange(rowIndex, "isDefault", checked)
            }
          />
        );
      },
    },

    {
      accessorKey: "organization.name",
      header: "Organization",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];

        // Show SELECT (editable) ONLY IF id === 0 (newly added row)
        if (row.id === 0) {
          return (
            <CommonAutoComplete<OrganizationResponse, number>
              value={row.organizationId}
              onChange={(val) =>
                handleChange(rowIndex, "organizationId", val || 0)
              }
              useDataQuery={useListOrganization}
              labelKey="name"
              valueKey="id"
            />
          );
        }

        // Show plain text if editing an existing row
        return <span>{row.organization?.name || ""}</span>;
      },
    },

    {
      accessorKey: "reOrderCount",
      header: "Re-Order Count",
      Edit: (rowIndex: number) => {
        const row = rows[rowIndex];
        const isNewRow = row.id === 0;
        const isEditable = editableRowIndex === rowIndex || isNewRow;

        if (!isEditable) return <span>{row.reOrderCount}</span>;

        return (
          <InputNumber
            min={0}
            value={row.reOrderCount}
            onChange={(val) => handleChange(rowIndex, "reOrderCount", val)}
            onKeyDown={async (e) => {
              const isLastRow = rowIndex === rows.length - 1;
              if (e.key === "Tab" && !e.shiftKey && isLastRow && isNewRow) {
                if (row.uomId > 0 && row.conversionCount >= 0) {
                  const payload = {
                    itemId: row.itemId,
                    uomId: row.uomId,
                    conversionCount: row.conversionCount,
                    availableCount: row.availableCount,
                    reOrderCount: row.reOrderCount,
                    isDefault: row.isDefault,
                    organizationId: row.organizationId,
                  };

                  createMutation.mutate(payload, {
                    onSuccess: () => {
                      setTimeout(() => addRows(), 0);
                    },
                  });
                }
              }
            }}
          />
        );
      },
    },
  ];
};

export const isDefaultOptions = ['true','false'] as const;
export const isDefaultMap = {
  true: 'Yes',
  false: 'No'
} as const;

export function getisDefaultLabel(isDefault: string) {
  return isDefaultMap[isDefault as keyof typeof isDefaultMap] || isDefault;
}
export const itemUOMColumns: MRT_ColumnDef<ItemUOMResponse>[] = [
  { accessorKey: "id", header: "ID" ,grow: false, size: 50},
  { accessorKey: 'uom.name', header: 'UOM' },
  { accessorKey: 'conversionCount', header: 'Conversion Count' },
  { accessorKey: 'availableCount', header: 'Available Count' },
  { accessorKey: 'reOrderCount', header: 'Re-Order Count' },
 { 
    accessorKey: 'isDefault', 
    header: 'Is Default',
    filterVariant: 'select',
    filterSelectOptions: isDefaultOptions.map(value => ({
      value,
      label: isDefaultMap[value], // Capitalize first letter
    })),
    Cell: ({ cell }) => {
      const isDefault = cell.getValue<string>();
      return getisDefaultLabel(isDefault);
    } 
  },
  {
      accessorKey: "createdAt",
      header: "Created At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Updated At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
  { accessorKey: 'organization.name', header: 'Organization' },
    { accessorKey: "createdBy", header: "Created By" },
    { accessorKey: "updatedBy", header: "Updated By" }, 
];
