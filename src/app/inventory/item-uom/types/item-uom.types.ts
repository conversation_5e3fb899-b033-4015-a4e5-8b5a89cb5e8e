import { z } from "zod";
import { BaseResponse, PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { UomResponse } from "../../uom/types/uom.types";
import { statusOptions, statusMap, getStatusLabel } from "@/app/common/types/status.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { MRT_ColumnDef } from "material-react-table";

export interface ItemUomProps {
  itemId?: number;
}

export interface ItemUOMResponse extends BaseResponse, Record<string, unknown> {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  itemId: number;
  uomId: number;
  conversionCount: number;
  availableCount: number;
  reOrderCount: number;
  organizationId: number;
  isDefault: boolean;
  organization?: OrganizationResponse;
  uom?: UomResponse;
}


export const itemUOMColumns: MRT_ColumnDef<ItemUOMResponse>[] = [
  { accessorKey: 'id', header: 'ID' },
  { accessorKey: 'uom.name', header: 'UOM' },
  { accessorKey: 'conversionCount', header: 'Conversion Count' },
  { accessorKey: 'availableCount', header: 'Available Count' },
  { accessorKey: 'reOrderCount', header: 'Re-Order Count' },
  { accessorKey: 'isDefault', header: 'Is Default' },
  { 
          accessorKey: 'status', 
          header: 'Status',
          filterVariant: 'select',
          filterSelectOptions: statusOptions.map(value => ({
            value,
            label: statusMap[value], // Capitalize first letter
          })),
          Cell: ({ cell }) => {
            const status = cell.getValue<string>();
            return getStatusLabel(status);
          } 
  },
  {
      accessorKey: "createdAt",
      header: "Created At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Updated At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
  { accessorKey: 'organization.name', header: 'Organization' },
];


export type ItemUOMPaginatedResponse = PaginatedResponse<ItemUOMResponse>;
export type ItemUOMSuccessResponse = SuccessResponse<ItemUOMResponse>;

export const itemUOMRequestSchema = z.object({
  itemId: z.number().int().positive("Item ID must be a positive number"),
  uomId: z.number().int().positive("UOM ID must be a positive number"),
  conversionCount: z.number().int().nonnegative("Conversion count must be a non-negative integer"),
  availableCount: z.number().int().nonnegative("Available count must be a non-negative integer"),
  reOrderCount: z.number().int().nonnegative("Re-order count must be a non-negative integer"),
  organizationId: z.number().int().positive("Organization ID must be a positive number").optional(),
  isDefault: z.boolean(),
});

export type ItemUOMRequest = z.infer<typeof itemUOMRequestSchema>;

export interface ItemUOMFilters{
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  itemId?: number;
  uomId?: number;
  organizationId?: number;
};
