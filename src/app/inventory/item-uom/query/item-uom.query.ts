// itemUOM.hooks.ts
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createItemUOM,
  deleteItemUOM,
  getItemUOM,
  listItemUOM,
  updateItemUOM,
} from "../api/item-uom.api";
import { ItemUOMRequest, ItemUOMSuccessResponse } from "../types/item-uom.types";
import toast from "react-hot-toast";

import { ItemUOMFilters } from "../types/item-uom.types";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListItemUOM = (
  page?: number,
  limit?: number,
  filters: ItemUOMFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery({
    queryKey: ["item-uoms", page, limit, filterKey],
    queryFn: () => listItemUOM(page, limit, filters),
  });
};

export const useGetItemUOM = (id: number | undefined) => {
  return useQuery({
    queryKey: ["item-uom", id],
    queryFn: () => getItemUOM(id!),
    enabled: !!id,
  });
};

export const useCreateItemUOM = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ItemUOMSuccessResponse,
    ApiErrorResponse,
    ItemUOMRequest
  >({
    mutationFn: createItemUOM,
    mutationKey: ["createItemUOM"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["item-uoms"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateItemUOM = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ItemUOMSuccessResponse,
    ApiErrorResponse,
    { id: number; itemUOMRequest: ItemUOMRequest }
  >({
    mutationKey: ["updateItemUOM"],
    mutationFn: ({ id, itemUOMRequest }) => updateItemUOM(id, itemUOMRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["item-uoms"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteItemUOM = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ItemUOMSuccessResponse,
    ApiErrorResponse,
    number
  >({
    mutationKey: ["deleteItemUOM"],
    mutationFn: (id) => deleteItemUOM(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["item-uoms"] });
      toast.success(data.message || "Item UOM deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
