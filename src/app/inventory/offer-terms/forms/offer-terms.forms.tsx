"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uI<PERSON>,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateOfferTerms,
  useGetOfferTerms,
  useUpdateOfferTerms,
} from "../query/offer-terms.query";
import { useRef, useEffect } from "react";
import {
  OfferTermsRequestSchema,
  OfferTermsRequest,
} from "@/app/inventory/offer-terms/types/offer-terms.types";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import {
  OrganizationPaginatedResponse,
  OrganizationResponse,
} from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { useRouter } from "next/navigation";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function OfferTermsForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: OfferTermsData, isLoading } = useGetOfferTerms(id);
  const createMutation = useCreateOfferTerms();
  const updateMutation = useUpdateOfferTerms();

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<OfferTermsRequest>({
    resolver: zodResolver(OfferTermsRequestSchema),
    defaultValues: {
      name: "",
      status: "active",
      organizationId: undefined,
      purchaseQty: 1,
      freeQty: 0,
    },
  });

  useEffect(() => {
    if (isEditMode && OfferTermsData) {
      reset(OfferTermsData);
    }
  }, [isEditMode, OfferTermsData, reset]);

  const onSubmit = (data: OfferTermsRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, OfferTermsRequest: data },
        {
          onSuccess: () => {
            router.push("/inventory/offer-terms");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          reset();
          router.push("/inventory/offer-terms");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

return (
  <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
    <Typography variant="h6" mb={3}>
      {isEditMode ? "Edit Offer Terms" : "Create Offer Terms"}
    </Typography>

    <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
      <Grid container spacing={2}>
        {/* Name Field */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                inputRef={nameRef}
                label="Name"
                size="small"
                fullWidth
                {...field}
                error={!!errors.name}
                helperText={errors.name?.message}
                autoFocus
                required
              />
            )}
          />
        </Grid>

        {/* Purchase Quantity Field */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="purchaseQty"
            control={control}
            render={({ field }) => (
              <TextField
                label="Purchase Quantity"
                type="number"
                size="small"
                fullWidth
                value={field.value ?? 1}
                onChange={(e) => field.onChange(Number(e.target.value))}
                error={!!errors.purchaseQty}
                helperText={errors.purchaseQty?.message}
                required
              />
            )}
          />
        </Grid>

        {/* Free Quantity Field */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="freeQty"
            control={control}
            render={({ field }) => (
              <TextField
                label="Free Quantity"
                type="number"
                size="small"
                fullWidth
                value={field.value ?? 0}
                onChange={(e) => field.onChange(Number(e.target.value))}
                error={!!errors.freeQty}
                helperText={errors.freeQty?.message}
              />
            )}
          />
        </Grid>

        {/* Status Field (Edit Mode Only) */}
        {isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <TextField
                  select
                  label="Status"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.status}
                  helperText={errors.status?.message}
                >
                  {statuses.map((opt) => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
        )}

        {/* Organization Field (Create Mode Only) */}
        {!isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="organizationId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                  label="Organization"
                  value={field.value ?? undefined}
                  onChange={field.onChange}
                  useDataQuery={useListOrganization}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.organizationId}
                  helperText={errors.organizationId?.message}
                />
              )}
            />
          </Grid>
        )}
      </Grid>

      {/* Action Buttons */}
      <Box
        sx={{
          display: "flex",
          gap: 2,
          justifyContent: "flex-end",
          mt: 3,
        }}
      >
        <Button
          variant="outlined"
          color="inherit"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={createMutation.isPending || updateMutation.isPending}
        >
          {isEditMode ? "Update" : "Create"}
        </Button>
      </Box>
    </Box>
  </Card>
);
}
