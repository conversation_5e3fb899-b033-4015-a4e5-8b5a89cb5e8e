import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createOfferTerms,
  deleteOfferTerms,
  getOfferTerms,
  listOfferTerms,
  updateOfferTerms,
} from "../api/offer-terms.api";
import {
  OfferTermsRequest,
  OfferTermsSuccessResponse,
} from "../types/offer-terms.types";
import toast from "react-hot-toast";
import { OfferTermsFilters } from "../types/offer-terms.types";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListOfferTerms = (
  page?: number,
  limit?: number,
  filters: OfferTermsFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery({
    queryKey: ["offer-terms", page, limit, filterKey],
    queryFn: () => listOfferTerms(page, limit, filters),
  });
};
export const useGetOfferTerms = (id: number | undefined) => {
  return useQuery({
    queryKey: ["offer-terms", id],
    queryFn: () => getOfferTerms(id!),
    enabled: !!id, // only run if id is defined
  });
};

export const useCreateOfferTerms = () => {
  const queryClient = useQueryClient();
  return useMutation<
    OfferTermsSuccessResponse,
    ApiErrorResponse,
    OfferTermsRequest
  >({
    mutationFn: createOfferTerms,
    mutationKey: ["createOfferTerm"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["offer-terms"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateOfferTerms = () => {
  const queryClient = useQueryClient();

  return useMutation<
    OfferTermsSuccessResponse, // API success response
    ApiErrorResponse, // API error response
    { id: number; OfferTermsRequest: OfferTermsRequest } // ✅ mutationFn input type
  >({
    mutationKey: ["updateOfferTerms"],
    mutationFn: ({ id, OfferTermsRequest }) =>
      updateOfferTerms(id, OfferTermsRequest),
    onSuccess: (data) => {
      toast.success(data.message);
      queryClient.invalidateQueries({ queryKey: ["offer-terms"] });
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteOfferTerms = () => {
  const queryClient = useQueryClient();
  return useMutation<
    OfferTermsSuccessResponse, // Actual API response type
    ApiErrorResponse,
    number // Mutation input is just the ID
  >({
    mutationKey: ["deleteOfferTerms"],
    mutationFn: (id) => deleteOfferTerms(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["offer-terms"] });
      toast.success(data.message || "Offer terms deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
