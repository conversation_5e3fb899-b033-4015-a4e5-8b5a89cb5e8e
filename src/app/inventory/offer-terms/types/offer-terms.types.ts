import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
import { BaseResponse, PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from '@/app/common/utils/serialize.utils';


export interface OfferTermsResponse extends BaseResponse, Record<string, unknown> {
  purchaseQty: number;
  freeQty: number;
  name: string;
  status: 'active' | 'inactive';
  organizationId: number;
}

export const OfferTermsColumns: MRT_ColumnDef<OfferTermsResponse>[] = [
  { accessorKey: 'id', header: 'ID' ,grow: false, size: 50 },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'purchaseQty', header: 'Purchase Quantity'},
  { accessorKey: 'freeQty', header: 'Free Quantity'},
  { 
          accessorKey: 'status', 
          header: 'Status',
          filterVariant: 'select',
          filterSelectOptions: statusOptions.map(value => ({
            value,
            label: statusMap[value], // Capitalize first letter
          })),
          Cell: ({ cell }) => {
            const status = cell.getValue<string>();
            return getStatusLabel(status);
          } 
  },
  {
        accessorKey: "createdAt",
        header: "Created At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
      {
        accessorKey: "updatedAt",
        header: "Updated At",
        filterVariant: "date-range",
        Cell: ({ cell }) => {
          const value = cell.getValue<string>();
          return localTime(value);
        },
      },
  { accessorKey: 'organization.name', header: 'Organization' },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type OfferTermsPaginatedResponse = PaginatedResponse<OfferTermsResponse>;
export type OfferTermsSuccessResponse = SuccessResponse<OfferTermsResponse>;


export const OfferTermsRequestSchema = z.object({
  name: z.string()
    .min(1, "Name is required")
    .transform(val => val.trim()),
  purchaseQty: z.number()
    .min(0, "Purchase quantity must be positive"),
  freeQty: z.number()
    .min(0, "Free quantity must be positive"),
  status: z.enum(['active', 'inactive']),
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number")
    .optional(),
});

export type OfferTermsRequest = z.infer<typeof OfferTermsRequestSchema>;

export interface OfferTermsFilters{
  createdAtFrom?: string;
  updatedAtFrom?: string;
  createdBy?: number;
  updatedBy?: number;
  name?: string;
  organizationId?: number;
}