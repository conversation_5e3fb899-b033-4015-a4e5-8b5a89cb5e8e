import apiClient from "@/app/api/api";
import { OfferTermsFilters, OfferTermsPaginatedResponse, OfferTermsResponse, OfferTermsSuccessResponse, OfferTermsRequest } from "../types/offer-terms.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listOfferTerms = async (
  page?: number,
  limit?: number,
  filters?: OfferTermsFilters,
): Promise<OfferTermsPaginatedResponse> => {
  const res = await apiClient.get<OfferTermsPaginatedResponse>("/offer-terms", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};

export const getOfferTerms = async (
  id: number
): Promise<OfferTermsResponse> => {
  const res = await apiClient.get<OfferTermsResponse>(`/offer-terms/${id}`);
  return res.data;
}

export const createOfferTerms = async (
  OfferTermsRequest: OfferTermsRequest
): Promise<OfferTermsSuccessResponse> => {
  const res = await apiClient.post<OfferTermsSuccessResponse>("/offer-terms", OfferTermsRequest);
  return res.data;
}

export const updateOfferTerms = async (
  id: number,
  OfferTermsRequest: OfferTermsRequest
): Promise<OfferTermsSuccessResponse> => {
  const res = await apiClient.patch<OfferTermsSuccessResponse>(`/offer-terms/${id}`, OfferTermsRequest);
  return res.data;
}

export const deleteOfferTerms = async (
  id: number
): Promise<OfferTermsSuccessResponse> => {
  const res = await apiClient.delete<OfferTermsSuccessResponse>(`/offer-terms/${id}`);
  return res.data;
};