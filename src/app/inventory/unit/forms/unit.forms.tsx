"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uItem,
  TextField,
  Typo<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateUnit,
  useGetUnit,
  useUpdateUnit,
} from "../query/unit.query";
import { useRef, useEffect } from "react";
import {
  unitRequestSchema,
  UnitRequest,
} from "../types/unit.types";
import { useRouter } from "next/navigation";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Draft", value: "draft" },
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function UnitForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: unitData, isLoading } = useGetUnit(id);
  const createMutation = useCreateUnit();
  const updateMutation = useUpdateUnit();
  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, []);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<UnitRequest>({
    resolver: zodResolver(unitRequestSchema),
    defaultValues: {
      UnitName: "",
      unitHimsCode: "",
      unitNameDisplay: "",
      unitSnomedCode: "",
      unitDescription: "",
      unitStatus: "draft",
    },
  });

  useEffect(() => {
    if (isEditMode && unitData) {
      reset(unitData);
    }
  }, [isEditMode, unitData, reset]);

  const onSubmit = (data: UnitRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, unitRequest: data },
        {
          onSuccess: () => {
            router.push("/inventory/unit");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          reset();
          router.push("/inventory/unit");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", mb: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Unit" : "Create Unit"}
      </Typography>
      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="UnitName"
              control={control}
              render={({ field }) => (
                <TextField
                  inputRef={nameRef}
                  label="Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.UnitName}
                  helperText={errors.UnitName?.message}
                  autoFocus
                  required
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="unitNameDisplay"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Display Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.unitNameDisplay}
                  helperText={errors.unitNameDisplay?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="unitSnomedCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="SNOMED Code"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.unitSnomedCode}
                  helperText={errors.unitSnomedCode?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="unitDescription"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Description"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.unitDescription}
                  helperText={errors.unitDescription?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="unitStatus"
              control={control}
              render={({ field }) => (
                <TextField
                  select
                  label="Status"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.unitStatus}
                  helperText={errors.unitStatus?.message}
                >
                  {statuses.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
        </Grid>
        <Box mt={3} display="flex" gap={2}>
          <Button type="submit" variant="contained" color="primary" disabled={createMutation.isPending || updateMutation.isPending}>
            {isEditMode ? "Update" : "Create"}
          </Button>
          <Button variant="outlined" color="secondary" onClick={() => router.push("/inventory/unit")}>Cancel</Button>
        </Box>
      </Box>
    </Card>
  );
}
