import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createUnit,
  deleteUnit,
  getUnit,
  listUnit,
  updateUnit,
} from "@/app/inventory/unit/api/unit.api";
import {
  UnitFilters,
  UnitPaginatedResponse,
  UnitRequest,
  UnitResponse,
  UnitSuccessResponse,
} from "../types/unit.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListUnit = (
  page?: number,
  limit?: number,
  filters: UnitFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<UnitPaginatedResponse, ApiErrorResponse>({
    queryKey: ["units", page, limit, filterKey],
    queryFn: () => listUnit(page, limit, filters),
  });
};

export const useGetUnit = (id: number | undefined) => {
  return useQuery<UnitResponse, ApiErrorResponse>({
    queryKey: ["unit", id],
    queryFn: () => getUnit(id!),
    enabled: !!id,
  });
};

export const useCreateUnit = () => {
  const queryClient = useQueryClient();
  return useMutation<
    UnitSuccessResponse,
    ApiErrorResponse,
    UnitRequest
  >({
    mutationKey: ["createUnit"],
    mutationFn: (unitRequest: UnitRequest) => createUnit(unitRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["units"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create unit");
    },
  });
};

export const useUpdateUnit = () => {
  const queryClient = useQueryClient();
  return useMutation<
    UnitSuccessResponse,
    ApiErrorResponse,
    { id: number; unitRequest: UnitRequest }
  >({
    mutationKey: ["updateUnit"],
    mutationFn: ({ id, unitRequest }) => updateUnit(id, unitRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["units"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update unit");
    },
  });
};

export const useDeleteUnit = () => {
  const queryClient = useQueryClient();
  return useMutation<UnitSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteUnit"],
    mutationFn: (id: number) => deleteUnit(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["units"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete unit");
    },
  });
};
