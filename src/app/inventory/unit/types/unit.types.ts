import { MRT_ColumnDef } from "material-react-table";
import { z } from "zod";
import { localTime } from "@/app/common/utils/serialize.utils";
import { PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";

export interface UnitProps {
  unitId?: number;
}

export interface UnitResponse extends UnitRequest, Record<string, unknown> {
  unitId: number;
  unitHimsCode?: string;
  unitName: string;
  unitNameDisplay?: string;
  unitSnomedCode?: string;
  unitDescription?: string;
  unitStatus: "draft" | "active" | "inactive";
  createdAt: string;
  createdBy: number;
  updatedAt: string;
  updatedBy: number;
}


// Status options and mapping for UnitStatus
const unitStatusOptions = [
  "draft",
  "active",
  "inactive",
];

const unitStatusMap: Record<string, string> = {
  draft: "Draft",
  active: "Active",
  inactive: "Inactive",
};

export const getUnitStatusLabel = (status: string): string => {
  return unitStatusMap[status] || "Unknown Status";
};

export const unitColumns: MRT_ColumnDef<UnitResponse>[] = [
  { accessorKey: "unitId", header: "ID", grow: false, size: 50 },
  { accessorKey: "unitHimsCode", header: "HIMS Code" },
  { accessorKey: "unitName", header: "Name" },
  { accessorKey: "unitNameDisplay", header: "Display Name" },
  { accessorKey: "unitSnomedCode", header: "SNOMED Code" },
  { accessorKey: "unitDescription", header: "Description" },
  {
    accessorKey: "unitStatus",
    header: "Status",
    filterVariant: "select",
    filterSelectOptions: unitStatusOptions.map((value) => ({
      value,
      label: unitStatusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getUnitStatusLabel(status);
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
];

export type UnitPaginatedResponse = PaginatedResponse<UnitResponse>;
export type UnitSuccessResponse = SuccessResponse<UnitResponse>;

export const unitRequestSchema = z.object({
  UnitName: z.string()
    .min(1, "Name is required")
    .transform((val) => val.trim()),
  unitHimsCode: z.string().optional().nullable().transform((val) => (val != null ? val.trim() : val)),
  unitNameDisplay: z.string().optional().nullable().transform((val) => (val != null ? val.trim() : val)),
  unitSnomedCode: z.string().optional().nullable().transform((val) => (val != null ? val.trim() : val)),
  unitDescription: z.string().optional().nullable().transform((val) => (val != null ? val.trim() : val)),
  unitStatus: z.enum(["draft", "active", "inactive"]),
  createdBy: z.number().int().optional(),
  updatedBy: z.number().int().optional(),
});

export type UnitRequest = z.infer<typeof unitRequestSchema>;

export interface UnitFilters extends Record<string, unknown> {
  unitId?: number;
  unitHimsCode?: string | null;
  unitName?: string;
  unitNameDisplay?: string | null;
  unitSnomedCode?: string | null;
  unitDescription?: string | null;
  unitStatus?: "draft" | "active" | "inactive";
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
  // Add any additional filter fields here if needed
}
