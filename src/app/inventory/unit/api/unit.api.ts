import apiClient from "@/app/api/api";
import {
  UnitPaginatedResponse,
  UnitResponse,
  UnitSuccessResponse,
  UnitRequest,
  UnitFilters,
} from "../types/unit.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listUnit = async (
  page?: number,
  limit?: number,
  filters?: UnitFilters
): Promise<UnitPaginatedResponse> => {
  const res = await apiClient.get<UnitPaginatedResponse>("/units", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });
  return res.data;
};

export const getUnit = async (
  id: number
): Promise<UnitResponse> => {
  const res = await apiClient.get<UnitResponse>(`/units/${id}`);
  return res.data;
};

export const createUnit = async (
  unitRequest: UnitRequest
): Promise<UnitSuccessResponse> => {
  const res = await apiClient.post<UnitSuccessResponse>("/units", unitRequest);
  return res.data;
};

export const updateUnit = async (
  id: number,
  unitRequest: UnitRequest
): Promise<UnitSuccessResponse> => {
  const res = await apiClient.patch<UnitSuccessResponse>(`/units/${id}`, unitRequest);
  return res.data;
};

export const deleteUnit = async (
  id: number
): Promise<UnitSuccessResponse> => {
  const res = await apiClient.delete<UnitSuccessResponse>(`/units/${id}`);
  return res.data;
};
