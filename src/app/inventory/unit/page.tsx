"use client";

import { unitColumns, UnitResponse } from "./types/unit.types";
import { useListUnit } from "./query/unit.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteUnit } from "./query/unit.query";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";

function UnitPage() {
  const router = useRouter();
  const deleteMutation = useDeleteUnit();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<UnitResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] = useState<RolePermissionFilters>({
    moduleName: "Inventory",
    featureName: "Unit",
    roleId: 0,
  });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);

  const handleOpenDelete = (row: UnitResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.unitId) {
      deleteMutation.mutate(selectedRow.unitId, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<UnitResponse>
        title="Units"
        columns={unitColumns}
        useDataQuery={useListUnit}
        hiddenColumns={[
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/unit/create")}
        onExport={() => console.log("Export Unit")}
        onEdit={(row) => router.push(`/inventory/unit/edit/${row.UnitId}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete unit item {" "}
            <strong>{selectedRow?.unitName}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default UnitPage;
