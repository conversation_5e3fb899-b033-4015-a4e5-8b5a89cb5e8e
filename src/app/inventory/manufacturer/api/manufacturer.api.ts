import apiClient from "@/app/api/api";
import {
  ManufacturerRequest,
  ManufacturerResponse,
  ManufacturerPaginatedResponse,
  ManufacturerSuccessResponse,
} from "../types/manufacturer.types";

// List paginated
export const listManufacturers = async (
  page?: number,
  limit?: number
): Promise<ManufacturerPaginatedResponse> => {
  const res = await apiClient.get<ManufacturerPaginatedResponse>("/manufacturers", {
    params: { page, limit },
  });
  return res.data;
};

// Get single
export const getManufacturer = async (id: number): Promise<ManufacturerResponse> => {
  const res = await apiClient.get<ManufacturerResponse>(`/manufacturers/${id}`);
  return res.data;
};

// Create
export const createManufacturer = async (
  data: ManufacturerRequest
): Promise<ManufacturerSuccessResponse> => {
  const res = await apiClient.post<ManufacturerSuccessResponse>("/manufacturers", data);
  return res.data;
};

// Update
export const updateManufacturer = async (
  id: number,
  data: ManufacturerRequest
): Promise<ManufacturerSuccessResponse> => {
  const res = await apiClient.patch<ManufacturerSuccessResponse>(`/manufacturers/${id}`, data);
  return res.data;
};

// Delete
export const deleteManufacturer = async (id: number): Promise<void> => {
  await apiClient.delete(`/manufacturers/${id}`);
};