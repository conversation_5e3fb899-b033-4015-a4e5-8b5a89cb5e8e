import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  listManufacturers,
  getManufacturer,
  createManufacturer,
  updateManufacturer,
  deleteManufacturer,
} from "../api/manufacturer.api";
import {
  ManufacturerRequest,
  ManufacturerSuccessResponse,
  ManufacturerPaginatedResponse,
  ManufacturerResponse,
} from "../types/manufacturer.types";
import { toast } from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

// List
export const useListManufacturers = (page?: number, limit?: number) =>
  useQuery<ManufacturerPaginatedResponse>({
    queryKey: ["manufacturers", page, limit],
    queryFn: () => listManufacturers(page, limit),
  });

// Single
export const useManufacturer = (id: number) =>
  useQuery<ManufacturerResponse>({
    queryKey: ["manufacturers", id],
    queryFn: () => getManufacturer(id),
    enabled: !!id,
  
  });

// Create
export const useCreateManufacturer = () => {
  const queryClient = useQueryClient();
  return useMutation<ManufacturerSuccessResponse, ApiErrorResponse, ManufacturerRequest>({
    mutationKey: ["createManufacturer"],
    mutationFn: (manufacturerRequest: ManufacturerRequest) => createManufacturer(manufacturerRequest),
    onSuccess: (data) => {
      toast.success(data.message || "Manufacturer created successfully");
      queryClient.invalidateQueries({ queryKey: ["manufacturers"] });
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

// Update
export const useUpdateManufacturer = () => {
  const queryClient = useQueryClient();
  return useMutation<ManufacturerSuccessResponse, ApiErrorResponse, { id: number; data: ManufacturerRequest }>({
    mutationKey: ["updateManufacturer"],
    mutationFn: ({ id, data }) => updateManufacturer(id, data),
    onSuccess: (data) => {
      toast.success(data.message || "Manufacturer updated successfully");
      queryClient.invalidateQueries({ queryKey: ["manufacturers"] });
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

// Delete
export const useDeleteManufacturer = () => {
  const queryClient = useQueryClient();
  return useMutation<void, ApiErrorResponse, number>({
    mutationKey: ["deleteManufacturer"],
    mutationFn: (id: number) => deleteManufacturer(id),
    onSuccess: () => {
      toast.success("Manufacturer deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["manufacturers"] });
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};