"use client";

import { ManufacturerResponse } from "./types/manufacturer.types";
import { useListManufacturers } from "./query/manufacturer.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteManufacturer } from "./query/manufacturer.query";

import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";

// Define the columns for the Manufacturer table
const manufacturerColumns = [
  {
    header: "ID",
    accessorKey: "manufacturerId",
  },
  {
    header: "Name",
    accessorKey: "manufacturerName",
  },
  {
    header: "Name Display",
    accessorKey: "manufacturerNameDisplay",
  },
  {
    header: "SNOMED Code",
    accessorKey: "manufacturerSnomedCode",
  },
  {
    header: "CDC Code",
    accessorKey: "manufacturerCdcCode",
  },
  {
    header: "Description",
    accessorKey: "manufacturerDescription",
  },
  {
    header: "Status",
    accessorKey: "manufacturerStatus",
  },
  {
    header: "Country",
    accessorKey: "manufacturerCountry",
  },
];

function ManufacturerPage() {
  const router = useRouter();
  const deleteMutation = useDeleteManufacturer();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<ManufacturerResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] = useState<RolePermissionFilters>({
    moduleName: "Inventory",
    featureName: "Manufacturer",
    roleId: 0,
  });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);

  // Open modal and set row to delete
  const handleOpenDelete = (row: ManufacturerResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  // Close modal and clear selected row
  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.manufacturerId) {
      deleteMutation.mutate(selectedRow.manufacturerId, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<ManufacturerResponse>
        title="Manufacturers"
        columns={manufacturerColumns}
        useDataQuery={useListManufacturers}
        hiddenColumns={[
          // Add any columns you want to hide
        ]}
        onCreate={() => router.push("/inventory/manufacturer/create")}
        onExport={() => console.log("Export Manufacturer")}
        onEdit={(row) => router.push(`/inventory/manufacturer/edit/${row.manufacturerId}`)}
        onDelete={handleOpenDelete}
        isCreate={rolePermissionData?.data[0]?.create}
        isEdit={rolePermissionData?.data[0]?.update}
        isDelete={rolePermissionData?.data[0]?.delete}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete manufacturer{" "}
            <strong>{selectedRow?.manufacturerName}</strong>? This action cannot be
            undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default ManufacturerPage;