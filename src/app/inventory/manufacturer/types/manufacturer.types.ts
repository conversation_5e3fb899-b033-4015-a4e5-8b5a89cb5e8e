import { z } from "zod";
import { PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";

export interface ManufacturerResponse extends Record<string, unknown> {
  manufacturerId: number;
  manufacturerHimsCode?: string;
  manufacturerName: string;
  manufacturerNameDisplay?: string;
  manufacturerSnomedCode?: string;
  manufacturerCdcCode?: string;
  manufacturerDescription?: string;
  manufacturerStatus: "active" | "inactive";
  manufacturerCountryId?: number;
}

export const ManufacturerRequestSchema = z.object({
  manufacturerName: z.string().min(1, "Name is required"),
  manufacturerNameDisplay: z.string().optional(),
  manufacturerSnomedCode: z.string().optional(),
  manufacturerCdcCode: z.string().optional(),
  manufacturerDescription: z.string().optional(),
  manufacturerStatus: z.enum(["active", "inactive"]),
  manufacturerCountryId: z.number().optional(),
});

export type ManufacturerRequest = z.infer<typeof ManufacturerRequestSchema>;
export type ManufacturerPaginatedResponse = PaginatedResponse<ManufacturerResponse>;
export type ManufacturerSuccessResponse = SuccessResponse<ManufacturerResponse>;