"use client";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Button, Grid, TextField, Typography, MenuItem } from "@mui/material";
import { ManufacturerRequest, ManufacturerRequestSchema } from "../types/manufacturer.types";
import { useCreateManufacturer, useManufacturer, useUpdateManufacturer } from "../query/manufacturer.query";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useListCountry } from "@/app/organization/country/query/country.query";
import { CountryPaginatedResponse, CountryResponse } from "@/app/organization/country/types/country.types";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";

interface ManufacturerFormProps {
  id?: number;
}

export default function ManufacturerForm({ id }: ManufacturerFormProps) {
  const isEditMode = !!id;
  const { data: manufacturerData } = useManufacturer(id!);
  const createMutation = useCreateManufacturer();
  const updateMutation = useUpdateManufacturer();
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ManufacturerRequest>({
    resolver: zodResolver(ManufacturerRequestSchema),
    defaultValues: {
      manufacturerName: "",
      manufacturerNameDisplay: "",
      manufacturerSnomedCode: "",
      manufacturerCdcCode: "",
      manufacturerDescription: "",
      manufacturerStatus: "active",
      manufacturerCountryId: 0,
    },
  });

  useEffect(() => {
    if (manufacturerData) {
      reset(manufacturerData);
    }
  }, [manufacturerData, reset]);

  const onSubmit = (data: ManufacturerRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, data },
        {
          onSuccess: () => router.push("/inventory/manufacturer"),
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => router.push("/inventory/manufacturer"),
      });
    }
  };


  return (
  
      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Typography variant="h6" mb={2}>Manufacturer Details</Typography>
        <Grid container spacing={2}>
          
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="manufacturerName"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Name"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.manufacturerName}
                  helperText={errors.manufacturerName?.message}
                  required
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="manufacturerNameDisplay"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Name Display"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.manufacturerNameDisplay}
                  helperText={errors.manufacturerNameDisplay?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="manufacturerSnomedCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="SNOMED Code"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.manufacturerSnomedCode}
                  helperText={errors.manufacturerSnomedCode?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="manufacturerCdcCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="CDC Code"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.manufacturerCdcCode}
                  helperText={errors.manufacturerCdcCode?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="manufacturerDescription"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Description"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.manufacturerDescription}
                  helperText={errors.manufacturerDescription?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="manufacturerStatus"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Status"
                  select
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.manufacturerStatus}
                  helperText={errors.manufacturerStatus?.message}
                  required
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </TextField>
              )}
            />
          </Grid>
          {!isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="manufacturerCountryId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<
                    CountryResponse,
                    CountryPaginatedResponse,
                    number
                  >
                    label="Country"
                    value={field.value ? Number(field.value) : undefined}
                    onChange={field.onChange}
                    useDataQuery={useListCountry}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.manufacturerCountryId}
                    helperText={errors.manufacturerCountryId?.message}
                  />
                )}
              />
            </Grid>
          )}
        </Grid>
        <Box mt={3} display="flex" justifyContent="flex-end">
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
   
  );
}