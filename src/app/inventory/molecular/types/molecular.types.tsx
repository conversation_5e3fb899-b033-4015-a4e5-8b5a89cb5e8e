"use client";

import { MRT_ColumnDef } from "material-react-table";
import { z } from "zod";
import {
  statusOptions,
  statusMap,
  getStatusLabel,
} from "@/app/common/types/status.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";
export interface MolecularProps {
  itemId?: number;
}

export interface MolecularResponse extends Record<string, unknown> {
  id: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
  code: string;
  name: string;
  description: string;
  pregnancyCategory:
    | "category_a"
    | "category_b"
    | "category_c"
    | "category_d"
    | "category_x"; // extend as needed
  status: "active" | "inactive";
  organizationId: number;
}

const pregnancyCategoryOptions = [
  "category_a",
  "category_b",
  "category_c",
  "category_d",
  "category_x",
];

const pregnancyCategoryMap: Record<string, string> = {
  category_a: "Category A",
  category_b: "Category B",
  category_c: "Category C",
  category_d: "Category D",
  category_x: "Category X",
};

export const getPregnancyCategoryLabel = (category: string): string => {
  return pregnancyCategoryMap[category] || "Unknown Category";
};

export const molecularColumns: MRT_ColumnDef<MolecularResponse>[] = [
  { accessorKey: "id", header: "ID", grow: false, size: 50 },
  { accessorKey: "code", header: "Code" },
  { accessorKey: "name", header: "Name" },
  { accessorKey: "description", header: "Description" },
  {
    accessorKey: "pregnancyCategory",
    header: "Pregnancy Category",
    filterVariant: "select",
    filterSelectOptions: pregnancyCategoryOptions.map((value) => ({
      value,
      label: pregnancyCategoryMap[value],
    })),
    Cell: ({ cell }) => {
      const category = cell.getValue<string>();
      return category;
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    filterVariant: "select",
    filterSelectOptions: statusOptions.map((value) => ({
      value,
      label: statusMap[value], // Capitalize first letter
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: "organization.name", header: "Organization" },
];

export type MolecularPaginatedResponse = PaginatedResponse<MolecularResponse>;
export type MolecularSuccessResponse = SuccessResponse<MolecularResponse>;

export const molecularRequestSchema = z.object({
  name: z.string()
    .min(1, "Name is required")
    .transform((val) => val.trim()),
  description: z.string()
    .nullable()
    .transform((val) => (val != null ? val.trim() : val)),
  pregnancyCategory: z.enum([
    "category_a",
    "category_b",
    "category_c",
    "category_d",
    "category_x",
  ]),
  status: z.enum(["active", "inactive"]),
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number")
    .optional(),
});

export type MolecularRequest = z.infer<typeof molecularRequestSchema>;

export interface MolecularFilters {
  name?: string;
  pregnancyCategory?:
    | "category_a"
    | "category_b"
    | "category_c"
    | "category_d"
    | "category_x";
  status?: "active" | "inactive";
  organizationId?: number;
  createdAt?: string; // ISO date string
  updatedAt?: string; // ISO date string
  createdBy?: number;
  updatedBy?: number;
}
