"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  MenuItem,
  TextField,
  <PERSON>po<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateMolecular,
  useGetMolecular,
  useUpdateMolecular,
} from "../query/molecular.query";
import { useRef, useEffect, useState } from "react";
import {
  molecularRequestSchema,
  MolecularRequest,
} from "@/app/inventory/molecular/types/molecular.types";
import { useRouter } from "next/navigation";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import {
  OrganizationPaginatedResponse,
  OrganizationResponse,
} from "@/app/organization/organization/types/organization.types";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";

type Props = {
  id?: number;
};

const pregnancyCategories = [
  { label: "Category A", value: "category_a" },
  { label: "Category B", value: "category_b" },
  { label: "Category C", value: "category_c" },
  { label: "Category D", value: "category_d" },
  { label: "Category X", value: "category_x" },
];

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function MolecularForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: molecularData, isLoading } = useGetMolecular(id);
  const createMutation = useCreateMolecular();
  const updateMutation = useUpdateMolecular();
  const [defaultOrganizationId, setDefaultOrganizationId] = useState<number>(0);

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
    const organizationId =
      Number(localStorage.getItem("default_organization_id")) || 0;
    setDefaultOrganizationId(organizationId);
  }, []);

  const {
    control,
    handleSubmit,
    reset,
    // watch,
    formState: { errors },
  } = useForm<MolecularRequest>({
    resolver: zodResolver(molecularRequestSchema),
    defaultValues: {
      name: "",
      description: "",
      pregnancyCategory: "category_a",
      status: "active",
      organizationId: defaultOrganizationId ?? 0,
    },
  });

  useEffect(() => {
    reset({
      name: "",
      description: "",
      pregnancyCategory: "category_a",
      status: "active",
      organizationId: defaultOrganizationId ?? 0,
    });
  }, [defaultOrganizationId, reset]);

  useEffect(() => {
    if (isEditMode && molecularData) {
      reset(molecularData);
    }
  }, [isEditMode, molecularData, reset]);

  const onSubmit = (data: MolecularRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, molecularRequest: data },
        {
          onSuccess: () => {
            // After successful edit, navigate back to the list page
            router.push("/inventory/molecular");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          // After successful create, navigate back to the list page
          reset();
          router.push("/inventory/molecular");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", mb: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Molecular" : "Create Molecular"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Name */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  inputRef={nameRef}
                  label="Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  autoFocus
                  required
                />
              )}
            />
          </Grid>

          {/* Pregnancy Category */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="pregnancyCategory"
              control={control}
              render={({ field }) => (
                <TextField
                  select
                  label="Pregnancy Category"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.pregnancyCategory}
                  helperText={errors.pregnancyCategory?.message}
                >
                  {pregnancyCategories.map((opt) => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>

          {/* Status (Edit Mode Only) */}
          {isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    {statuses.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
          )}

          {/* Organization (Create Mode Only) */}
          {!isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="organizationId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<
                    OrganizationResponse,
                    OrganizationPaginatedResponse,
                    number
                  >
                    label="Organization"
                    value={field.value}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
                )}
              />
            </Grid>
          )}

          {/* Description (Full Width) */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Description"
                  size="small"
                  multiline
                  rows={4}
                  fullWidth
                  {...field}
                  error={!!errors.description}
                  helperText={errors.description?.message}
                />
              )}
            />
          </Grid>
        </Grid>

        {/* Action Buttons */}
        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}
