import apiClient from "@/app/api/api";
import { MolecularPaginatedResponse, MolecularResponse, MolecularSuccessResponse, MolecularRequest, MolecularFilters } from "../types/molecular.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listMolecular = async (
  page?: number,
  limit?: number,
  filters?: MolecularFilters
): Promise<MolecularPaginatedResponse> => {
  const res = await apiClient.get<MolecularPaginatedResponse>("/moleculars", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};

export const getMolecular = async (
  id: number
): Promise<MolecularResponse> => {
  const res = await apiClient.get<MolecularResponse>(`/moleculars/${id}`);
  return res.data;
}

export const createMolecular = async (
  molecularRequest: MolecularRequest
): Promise<MolecularSuccessResponse> => {
  const res = await apiClient.post<MolecularSuccessResponse>("/moleculars", molecularRequest);
  return res.data;
}

export const updateMolecular = async (
  id: number,
  molecularRequest: MolecularRequest
): Promise<MolecularSuccessResponse> => {
  const res = await apiClient.patch<MolecularSuccessResponse>(`/moleculars/${id}`, molecularRequest);
  return res.data;
}

export const deleteMolecular = async (
  id: number
): Promise<MolecularSuccessResponse> => {
  const res = await apiClient.delete<MolecularSuccessResponse>(`/moleculars/${id}`);
  return res.data;
};