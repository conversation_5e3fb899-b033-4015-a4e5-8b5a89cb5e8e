import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createMolecular,
  deleteMolecular,
  getMolecular,
  listMolecular,
  updateMolecular,
} from "../api/molecular.api";
import {
  MolecularFilters,
  MolecularPaginatedResponse,
  MolecularRequest,
  MolecularResponse,
  MolecularSuccessResponse,
} from "../types/molecular.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListMolecular = (
  page?: number,
  limit?: number,
  filters: MolecularFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<MolecularPaginatedResponse, ApiErrorResponse>({
    queryKey: ["moleculars", page, limit, filterKey],
    queryFn: () => listMolecular(page, limit, filters),
  });
};

export const useGetMolecular = (id: number | undefined) => {
  return useQuery<MolecularResponse, ApiErrorResponse>({
    queryKey: ["molecular", id],
    queryFn: () => getMolecular(id!),
    enabled: !!id,
  });
};

export const useCreateMolecular = () => {
  const queryClient = useQueryClient();
  return useMutation<
    MolecularSuccessResponse,
    ApiErrorResponse,
    MolecularRequest
  >({
    mutationKey: ["createMolecular"],
    mutationFn: (molecularRequest: MolecularRequest) =>
      createMolecular(molecularRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["moleculars"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create molecular");
    },
  });
};

export const useUpdateMolecular = () => {
  const queryClient = useQueryClient();
  return useMutation<
    MolecularSuccessResponse,
    ApiErrorResponse,
    { id: number; molecularRequest: MolecularRequest }
  >({
    mutationKey: ["updateMolecular"],
    mutationFn: ({
      id,
      molecularRequest,
    }: {
      id: number;
      molecularRequest: MolecularRequest;
    }) => updateMolecular(id, molecularRequest), // Assuming update uses the same API as create
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["moleculars"] }); // Invalidate the list query to refresh data
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update molecular");
    },
  });
};

export const useDeleteMolecular = () => {
  const queryClient = useQueryClient();
  return useMutation<MolecularSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteMolecular"],
    mutationFn: (id: number) => deleteMolecular(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["moleculars"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete molecular");
    },
  });
};
