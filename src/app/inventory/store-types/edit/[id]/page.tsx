"use client";

import { useParams } from "next/navigation";
import StoreTypeForm from "@/app/inventory/store-types/forms/store-types.forms";
import OrganizationPage from "@/app/organization/organization/page";

export default function EditStoreTypePage() {
  const params = useParams();
  const id = Number(params?.id);

  return (
      <>
      <StoreTypeForm id={id} />
      <OrganizationPage />
      </>
    )
  }