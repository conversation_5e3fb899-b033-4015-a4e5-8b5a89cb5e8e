import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
// import { ItemType } from '../../items/types/items.types';
import { localTime } from '@/app/common/utils/serialize.utils';
import { PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';

export interface StoreTypeResponse extends Record<string, unknown> {
  id: number;
  code: string;
  name: string;
  description: string | null;
  status: string;
  organizationId: number;
  createdAt: string;
  updatedAt: string;
  createdBy: number;
  updatedBy: number;
}

export const storeTypeColumns: MRT_ColumnDef<StoreTypeResponse>[] = [
  { accessorKey: 'id', header: 'ID' ,grow: false, size: 50 },
  { accessorKey: 'code', header: 'Code' },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'description', header: 'Description' },
  { 
      accessorKey: 'status', 
      header: 'Status',
      filterVariant: 'select',
      filterSelectOptions: statusOptions.map(value => ({
        value,
        label: statusMap[value], // Capitalize first letter
      })),
      Cell: ({ cell }) => {
        const status = cell.getValue<string>();
        return getStatusLabel(status);
      } 
  },
  { accessorKey: 'organization.name', header: 'Organization' },
  {
      accessorKey: "createdAt",
      header: "Created At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Updated At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type StoreTypePaginatedResponse = PaginatedResponse<StoreTypeResponse>
export type StoreTypeSuccessResponse = SuccessResponse<StoreTypeResponse>

export const storeTypeRequestSchema = z.object({
  name: z.string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name cannot exceed 100 characters")
    .regex(/^[A-Za-z\s]+$/, "Name must contain only alphabets")
    .transform((val) => val.trim()),  // Trim whitespace
  description: z.string()
    .max(255, "Description cannot exceed 255 characters")
    .nullable()
    .optional()
    .transform((val) => (val != null ? val.trim() : val)),  // Trim if not null/undefined
  status: z.enum(["active", "inactive"]),  // No trim needed for enum
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number"),  // No trim for numbers
});

export type StoreTypeRequest = z.infer<typeof storeTypeRequestSchema>;

export interface StoreTypeFilters{
  name?: string;
  status?: string;
  description?: string;
  organizationId?: number;
  createdBy?: number;
  updatedBy?: number;
  createdAt?: string;
  updatedAt?: string;
}