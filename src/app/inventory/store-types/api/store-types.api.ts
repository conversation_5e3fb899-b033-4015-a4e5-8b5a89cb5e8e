import apiClient from "@/app/api/api";
import { StoreTypePaginatedResponse, StoreTypeResponse, StoreTypeSuccessResponse, StoreTypeRequest, StoreTypeFilters } from "../types/store-types.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listStoreTypes = async (
  page?: number,
  limit?: number,
  filters?: StoreTypeFilters
): Promise<StoreTypePaginatedResponse> => {
  const res = await apiClient.get<StoreTypePaginatedResponse>("/store-types", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};
export const getStoreType = async (
  id: number
): Promise<StoreTypeResponse> => {
  const res = await apiClient.get<StoreTypeResponse>(`/store-types/${id}`);
  return res.data;
};

export const createStoreType = async (
  storeTypeRequest: StoreTypeRequest
): Promise<StoreTypeSuccessResponse> => {
  const res = await apiClient.post<StoreTypeSuccessResponse>("/store-types", storeTypeRequest);
  return res.data;
};

export const updateStoreType = async (
  id: number,
  storeTypeRequest: StoreTypeRequest
): Promise<StoreTypeSuccessResponse> => {
  const res = await apiClient.patch<StoreTypeSuccessResponse>(`/store-types/${id}`, storeTypeRequest);
  return res.data;
};

export const deleteStoreType = async (
  id: number
): Promise<StoreTypeSuccessResponse> => {
  const res = await apiClient.delete<StoreTypeSuccessResponse>(`/store-types/${id}`);
  return res.data;
};