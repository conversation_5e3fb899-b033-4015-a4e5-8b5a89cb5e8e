import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createStoreType,
  deleteStoreType,
  getStoreType,
  listStoreTypes,
  updateStoreType,
} from "../api/store-types.api";
import {
  StoreTypeFilters,
  StoreTypePaginatedResponse,
  StoreTypeRequest,
  StoreTypeSuccessResponse,
} from "../types/store-types.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListStoreTypes = (
  page?: number,
  limit?: number,
  filters: StoreTypeFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<StoreTypePaginatedResponse, ApiErrorResponse>({
    queryKey: ["store-types", page, limit, filterKey],
    queryFn: () => listStoreTypes(page, limit, filters),
  });
};

export const useGetStoreType = (id: number | undefined) => {
  return useQuery({
    queryKey: ["store-type", id],
    queryFn: () => getStoreType(id!),
    enabled: !!id, // only run if id is defined
  });
};

export const useCreateStoreType = () => {
  const queryClient = useQueryClient();

  return useMutation<
    StoreTypeSuccessResponse,
    ApiErrorResponse,
    StoreTypeRequest
  >({
    mutationFn: createStoreType,
    mutationKey: ["createStoreType"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["store-types"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateStoreType = () => {
  const queryClient = useQueryClient();

  return useMutation<
    StoreTypeSuccessResponse, // The wrapped response from backend
    ApiErrorResponse,
    { id: number; storeTypeRequest: StoreTypeRequest }
  >({
    mutationKey: ["updateStoreType"],
    mutationFn: ({ id, storeTypeRequest }) =>
      updateStoreType(id, storeTypeRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["store-types"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Edit failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteStoreType = () => {
  const queryClient = useQueryClient();

  return useMutation<
    StoreTypeSuccessResponse, // Actual API response type
    ApiErrorResponse,
    number // Mutation input is just the ID
  >({
    mutationKey: ["deleteStoreType"],
    mutationFn: (id) => deleteStoreType(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["store-types"] });
      toast.success(data.message || "Store type deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
