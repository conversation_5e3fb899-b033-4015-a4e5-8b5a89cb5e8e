"use client";
import { useListSubstances, useDeleteSubstance } from "./query/substance.query";
import { SubstanceResponse } from "./types/substance.types";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Button, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, } from "@mui/material";
import { CommonTable } from "@/app/inventory/common/table/common-table";

export default function SubstancePage() {
  const router = useRouter();
  const deleteMutation = useDeleteSubstance();
  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<SubstanceResponse | null>(null);

  const handleOpenDelete = (row: SubstanceResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.substanceId) {
      deleteMutation.mutate(selectedRow.substanceId, {
        onSuccess: () => handleCloseDelete(),
        onError: () => handleCloseDelete(),
      });
    }
  };

  return (
    <>
      <CommonTable<SubstanceResponse>
        title="substance"
        columns={[
          { accessorKey: "substanceId", header: "ID" },
          { accessorKey: "substanceHimsCode", header: "HIMS Code" },
          { accessorKey: "substanceName", header: "Name" },
          { accessorKey: "substanceNameDisplay", header: "Name Display" },
          { accessorKey: "substanceSnomedCode", header: "SNOMED Code" },
          { accessorKey: "substanceCdcCode", header: "CDC Code" },
          { accessorKey: "substanceDescripiton", header: "Description" },
          { accessorKey: "substancePregnancyCategory", header: "Pregnancy Category" },
          { accessorKey: "substanceTherapeuticClassification", header: "Therapeutic Classification" },
          { accessorKey: "substancePharmaceuticalClassification", header: "Pharmaceutical Classification" },
          { accessorKey: "substanceAtcCode", header: "ATC Code" },
          { accessorKey: "substanceAtcTherapeuticClassification", header: "ATC Therapeutic Classification" },
          { accessorKey: "substanceAtcTherapeuticClassificationCode", header: "ATC Therapeutic Classification Code" },
          { accessorKey: "substanceAtcPharmacologicalClassification", header: "ATC Pharmacological Classification" },
          { accessorKey: "substanceAtcPharmacologicalClassificationCode", header: "ATC Pharmacological Classification Code" },
          { accessorKey: "substanceStatus", header: "Status" },
        ]}
        useDataQuery={useListSubstances}
        onCreate={() => router.push("/inventory/substance/create")}
        onEdit={(row) => router.push(`/inventory/substance/edit/${row.substanceId}`)}
        onDelete={handleOpenDelete}
      />
      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete substance <strong>{selectedRow?.substanceName}</strong>?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">Cancel</Button>
          <Button onClick={handleConfirmDelete} variant="contained" color="error" disabled={deleteMutation.isPending}>Confirm</Button>
        </DialogActions>
      </Dialog>
     
    </>
  );
}
