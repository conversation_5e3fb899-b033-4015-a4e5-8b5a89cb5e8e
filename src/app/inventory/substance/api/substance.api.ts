import apiClient from "@/app/api/api";
import {
  SubstanceRequest,
  SubstanceResponse,
  SubstancePaginatedResponse,
  SubstanceSuccessResponse,
} from "../types/substance.types";

// List paginated
export const listSubstances = async (
  page?: number,
  limit?: number
): Promise<SubstancePaginatedResponse> => {
  const res = await apiClient.get<SubstancePaginatedResponse>("/substances", {
    params: { page, limit },
  });
  return res.data;
};

// Get single
export const getSubstance = async (id: number): Promise<SubstanceResponse> => {
  const res = await apiClient.get<SubstanceResponse>(`/substances/${id}`);
  return res.data;
};

// Create
export const createSubstance = async (
  data: SubstanceRequest
): Promise<SubstanceSuccessResponse> => {
  const res = await apiClient.post<SubstanceSuccessResponse>("/substances", data);
  return res.data;
};

// Update
export const updateSubstance = async (
  id: number,
  data: SubstanceRequest
): Promise<SubstanceSuccessResponse> => {
  const res = await apiClient.patch<SubstanceSuccessResponse>(`/substances/${id}`, data);
  return res.data;
};

// Delete
export const deleteSubstance = async (id: number): Promise<void> => {
  await apiClient.delete(`/substances/${id}`);
};