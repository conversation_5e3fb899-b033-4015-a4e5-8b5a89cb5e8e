import { z } from "zod";

// Zod schema for validation
export const SubstanceRequestSchema = z.object({
  substanceName: z.string().min(1, "Name is required"),
  substanceNameDisplay: z.string().optional(),
  substanceSnomedCode: z.string().optional(),
  substanceCdcCode: z.string().optional(),
  substanceDescripiton: z.string().optional(),
  substancePregnancyCategory: z.enum(
    ["category_a", "category_b", "category_c", "category_d", "category_x"],
    { required_error: "Pregnancy Category is required" }
  ),
  substanceTherapeuticClassification: z.string().optional(),
  substancePharmaceuticalClassification: z.string().optional(),
  substanceAtcCode: z.string().optional(),
  substanceAtcTherapeuticClassification: z.string().optional(),
  substanceAtcTherapeuticClassificationCode: z.string().optional(),
  substanceAtcPharmacologicalClassification: z.string().optional(),
  substanceAtcPharmacologicalClassificationCode: z.string().optional(),
  substanceStatus: z.string().optional(),
});

export type SubstanceRequest = z.infer<typeof SubstanceRequestSchema>;

export interface SubstanceResponse extends SubstanceRequest, Record<string, unknown> {
    substanceId: number;
    substanceHimsCode: string;
    createdAt: string;
    updatedAt: string;
    createdBy: number;
    updatedBy: number;
}

export interface SubstancePaginatedResponse {
  total: number;
  data: SubstanceResponse[];
  meta: {
    total: number;
    page: number;
    limit: number;
  };
}

export interface SubstanceSuccessResponse {
  data: SubstanceResponse;
  message?: string;
}