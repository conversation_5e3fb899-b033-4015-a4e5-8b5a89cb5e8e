import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { createSubstance, updateSubstance, deleteSubstance as apiDeleteSubstance, getSubstance, listSubstances } from "../api/substance.api";
import {
  SubstanceRequest,
  SubstanceSuccessResponse,
  SubstancePaginatedResponse,
  SubstanceResponse,
} from "../types/substance.types";
import { toast } from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";
import { z } from "zod";

// Validation Schema
export const SubstanceRequestSchema = z.object({
  SubstanceHimsCode: z.string().optional(),
  SubstanceName: z.string().min(1, "Name is required"),
  // ...other fields
});

// List
export const useListSubstances = (page?: number, limit?: number) =>
  useQuery<SubstancePaginatedResponse>({
    queryKey: ["substances", page, limit],
    queryFn: () => listSubstances(page, limit),
  });

// Single
export const useSubstance = (id: number) =>
  useQuery<SubstanceResponse>({
    queryKey: ["substance", id],
    queryFn: () => getSubstance(id),
    enabled: !!id
  });

// Create
export const useCreateSubstance = () => {
  const queryClient = useQueryClient();
  return useMutation<SubstanceSuccessResponse, ApiErrorResponse, SubstanceRequest>({
    mutationKey: ["createSubstance"],
    mutationFn: (substanceRequest: SubstanceRequest) => createSubstance(substanceRequest),
    onSuccess: (data) => {
      toast.success(data.message || "Substance created successfully");
      queryClient.invalidateQueries({ queryKey: ["substances"] });
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

// Update
export const useUpdateSubstance = () => {
  const queryClient = useQueryClient();
  return useMutation<SubstanceSuccessResponse, ApiErrorResponse, { id: number; data: SubstanceRequest }>({
    mutationKey: ["updateSubstance"],
    mutationFn: ({ id, data }) => updateSubstance(id, data),
    onSuccess: (data) => {
      toast.success(data.message || "Substance updated successfully");
      queryClient.invalidateQueries({ queryKey: ["substances"] });
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

// Delete
export const useDeleteSubstance = () => {
  const queryClient = useQueryClient();
  return useMutation<void, ApiErrorResponse, number>({
    mutationKey: ["deletesubstance"],
    mutationFn: (id: number) => apiDeleteSubstance(id), // <-- This calls your API
    onSuccess: () => {
      toast.success("Substance deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["substances"] });
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};