"use client";
import { useF<PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Box, Button, Card, Grid, TextField, Typography, MenuItem } from "@mui/material";
import { useEffect } from "react";
import { SubstanceRequest, SubstanceRequestSchema } from "../types/substance.types";
import { useCreateSubstance, useUpdateSubstance, useSubstance } from "../query/substance.query";
import { useRouter } from "next/navigation";
import apiClient from "@/app/api/api";
// Update the import path below to the actual location of your apiClient, for example: 
interface Props {
  id?: number;
}

export default function SubstanceForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: substanceData, isLoading } = useSubstance(id!);

  const createMutation = useCreateSubstance();
  const updateMutation = useUpdateSubstance();
  const router = useRouter();
  
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<SubstanceRequest>({
    resolver: zodResolver(SubstanceRequestSchema),
    defaultValues: {
      substanceName: "",
      substanceNameDisplay: "",
      substanceSnomedCode: "",
      substanceCdcCode: "",
      substanceDescripiton: "",
      substancePregnancyCategory: undefined,
      substanceTherapeuticClassification: "",
      substancePharmaceuticalClassification: "",
      substanceAtcCode: "",
      substanceAtcTherapeuticClassification: "",
      substanceAtcTherapeuticClassificationCode: "",
      substanceAtcPharmacologicalClassification: "",
      substanceAtcPharmacologicalClassificationCode: "",
      substanceStatus: "",
    },
  });
    
  useEffect(() => {
    if (isEditMode && substanceData) {
      reset({
      /*  substanceHimsCode: substanceData.substanceHimsCode ?? "",*/
        substanceName: substanceData.substanceName ?? "",
        substanceNameDisplay: substanceData.substanceNameDisplay ?? "",
        substanceSnomedCode: substanceData.substanceSnomedCode ?? "",
        substanceCdcCode: substanceData.substanceCdcCode ?? "",
        substanceDescripiton: substanceData.substanceDescripiton ?? "",
        substancePregnancyCategory: substanceData.substancePregnancyCategory ?? "",
        substanceTherapeuticClassification: substanceData.substanceTherapeuticClassification ?? "",
        substancePharmaceuticalClassification: substanceData.substancePharmaceuticalClassification ?? "",
        substanceAtcCode: substanceData.substanceAtcCode ?? "",
        substanceAtcTherapeuticClassification: substanceData.substanceAtcTherapeuticClassification ?? "",
        substanceAtcTherapeuticClassificationCode: substanceData.substanceAtcTherapeuticClassificationCode ?? "",
        substanceAtcPharmacologicalClassification: substanceData.substanceAtcPharmacologicalClassification ?? "",
        substanceAtcPharmacologicalClassificationCode: substanceData.substanceAtcPharmacologicalClassificationCode ?? "",
        substanceStatus: substanceData.substanceStatus ?? "",
      });
    }
  }, [isEditMode, substanceData, reset]);

  const onSubmit = (data: SubstanceRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, data },
        {
          onSuccess: () => router.push("/inventory/substance"),
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          reset();
          router.push("/inventory/substance");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", mb: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Substance" : "Create Substance"}
      </Typography>
      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceName"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Name"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceName}
                  helperText={errors.substanceName?.message}
                  required
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceNameDisplay"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Display Name"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceNameDisplay}
                  helperText={errors.substanceNameDisplay?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceSnomedCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="SNOMED Code"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceSnomedCode}
                  helperText={errors.substanceSnomedCode?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceCdcCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="CDC Code"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceCdcCode}
                  helperText={errors.substanceCdcCode?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceDescripiton"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Description"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceDescripiton}
                  helperText={errors.substanceDescripiton?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substancePregnancyCategory"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Pregnancy Category"
                  select
                  fullWidth
                  size="small"
                  {...field}
                  value={field.value ?? ""}
                  error={!!errors.substancePregnancyCategory}
                  helperText={errors.substancePregnancyCategory?.message}
                  required
                >
                  <MenuItem value="">Select Category</MenuItem>
                  <MenuItem value="category_a">Category A</MenuItem>
                  <MenuItem value="category_b">Category B</MenuItem>
                  <MenuItem value="category_c">Category C</MenuItem>
                  <MenuItem value="category_d">Category D</MenuItem>
                  <MenuItem value="category_x">Category X</MenuItem>
                </TextField>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceTherapeuticClassification"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Therapeutic Classification"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceTherapeuticClassification}
                  helperText={errors.substanceTherapeuticClassification?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substancePharmaceuticalClassification"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Pharmaceutical Classification"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substancePharmaceuticalClassification}
                  helperText={errors.substancePharmaceuticalClassification?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceAtcCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="ATC Code"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceAtcCode}
                  helperText={errors.substanceAtcCode?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceAtcTherapeuticClassification"
              control={control}
              render={({ field }) => (
                <TextField
                  label="ATC Therapeutic Classification"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceAtcTherapeuticClassification}
                  helperText={errors.substanceAtcTherapeuticClassification?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceAtcTherapeuticClassificationCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="ATC Therapeutic Classification Code"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceAtcTherapeuticClassificationCode}
                  helperText={errors.substanceAtcTherapeuticClassificationCode?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceAtcPharmacologicalClassification"
              control={control}
              render={({ field }) => (
                <TextField
                  label="ATC Pharmacological Classification"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceAtcPharmacologicalClassification}
                  helperText={errors.substanceAtcPharmacologicalClassification?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="substanceAtcPharmacologicalClassificationCode"
              control={control}
              render={({ field }) => (
                <TextField
                  label="ATC Pharmacological Classification Code"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceAtcPharmacologicalClassificationCode}
                  helperText={errors.substanceAtcPharmacologicalClassificationCode?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
             <Controller
              name="substanceStatus"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Status"
                  select
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.substanceStatus}
                  helperText={errors.substanceStatus?.message}
                  required
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                </TextField>
              )}
            />
          </Grid>
        </Grid>
        <Box mt={3} display="flex" justifyContent="flex-end">
          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}

export const deleteSubstance = (id: number) => {
  return apiClient.delete(`/substances/${id}`);
};