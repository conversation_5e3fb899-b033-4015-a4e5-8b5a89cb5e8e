// import { JSX } from "react";
// import MolecularPage from "@/app/inventory/molecular/page"; // update to actual path
// import RouteOfAdministrationPage from "@/app/inventory/route-of-administration/page";
// import DosageFormPage from "@/app/inventory/dosage-form/page";
// import UomPage from "@/app/inventory/uom/page";
// import CategoryPage from "@/app/inventory/category/page";
// import ItemPage from "../../items/page";
// import VendorPage from "../../vendors/page";

// export interface NavTab {
//   tabName: string;
//   value: string;
//   page: JSX.Element;
// }

// export const navTabs: NavTab[] = [
//   { tabName: "Molecular", value: "molecular", page: <MolecularPage /> },
//   {
//     tabName: "Route of Administration",
//     value: "route-of-administration",
//     page: <RouteOfAdministrationPage />,
//   },
//   { tabName: "Dosage Form", value: "dosage-form", page: <DosageFormPage /> },
//   { tabName: "Category", value: "category", page: <CategoryPage /> },
//   { tabName: "Unit of Measurement", value: "uom", page: <UomPage /> },
//   { tabName: "Vendor", value: "vendor", page: <VendorPage /> },
//   { tabName: "Item", value: "item", page: <ItemPage /> },
// ];
