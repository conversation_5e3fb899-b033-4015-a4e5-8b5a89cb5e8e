"use client";

import Link from "next/link";
import {
  Tag,
  Layers,
  Truck,
  Pill,
  Syringe,
  Circle,
  Receipt,
  BadgeCheck,
  ClipboardList,
  SlidersHorizontal,
  Hash,
  Ruler,
  Atom,
  HandCoins,
  CreditCard,
  Map,
  BarChart2,
  Shuffle,
  Store,
  Archive,
  Building2,
  FileText,
  type LucideIcon,
} from "lucide-react";
import { useMemo, useState } from "react";
import TextField from "@mui/material/TextField";
import Grid from "@mui/material/Grid";
import Paper from "@mui/material/Paper";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";

// Define module type
type ModuleItem = {
  label: string;
  icon: LucideIcon;
  link: string;
};

const modules: ModuleItem[] = [
  { label: "category", icon: Tag, link: "/inventory/category" },
  { label: "combination", icon: Layers, link: "/inventory/combination" },
  { label: "dispatch-item", icon: Truck, link: "/inventory/dispatch-item" },
  { label: "dosage-form", icon: Pill, link: "/inventory/dosage-form" },
  {
    label: "dosage-form-roa",
    icon: Syringe,
    link: "/inventory/dosage-form-roa",
  },
  { label: "generic", icon: Circle, link: "/inventory/generic" },
  { label: "gst", icon: Receipt, link: "/inventory/gst" },
  {
    label: "gst-itc-eligible",
    icon: BadgeCheck,
    link: "/inventory/gst-itc",
  },
  {
    label: "internal-request-item",
    icon: ClipboardList,
    link: "/inventory/internal-request-item",
  },
  {
    label: "inventory-parameter",
    icon: SlidersHorizontal,
    link: "/inventory/inventory-parameter",
  },
  { label: "item", icon: SlidersHorizontal, link: "/inventory/items" },
  {
    label: "item-batch-number",
    icon: Hash,
    link: "/inventory/item-batch",
  },
  { label: "item-uom", icon: Ruler, link: "/inventory/item-uom" },
  { label: "molecular", icon: Atom, link: "/inventory/molecular" },
  { label: "offer-term", icon: HandCoins, link: "/inventory/offer-terms" },
  { label: "payment-term", icon: CreditCard, link: "/inventory/payment-term" },
  { label: "route-of-admin", icon: Map, link: "/inventory/route-of-administration" },
  { label: "stock-level", icon: BarChart2, link: "/inventory/stock-level" },
  {
    label: "stock-transaction-type",
    icon: Shuffle,
    link: "/inventory/stock-transaction-type",
  },
  { label: "store", icon: Store, link: "/inventory/stores" },
  { label: "store-type", icon: Archive, link: "/inventory/store-types" },
  { label: "uom", icon: Ruler, link: "/inventory/uom" },
  { label: "vendor", icon: Building2, link: "/inventory/vendors" },
  {
    label: "vendor-quotation",
    icon: FileText,
    link: "/inventory/vendor-quotations",
  },
];

export default function ModuleGrid() {
  const [search, setSearch] = useState("");

  const filteredModules = useMemo(() => {
    return modules
      .filter((m) => m.label.toLowerCase().includes(search.toLowerCase()))
      .sort((a, b) => a.label.localeCompare(b.label));
  }, [search]);

  return (
    <Box sx={{ p: 2 }}>
      {/* Search Input */}
      <Box sx={{ mb: 3, maxWidth: 400 }}>
        <TextField
          fullWidth
          label="Search modules..."
          variant="outlined"
          size="small"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
      </Box>

      {/* Grid Layout */}
      <Grid
        container
        spacing={3}
        sx={{
          alignItems: "stretch",
          justifyContent: "flex-start",
        }}
      >
        {filteredModules.map(({ label, icon: Icon, link }) => (
          <Grid key={label} size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
            <Link href={link} passHref>
              <Paper
                elevation={3}
                sx={{
                  height: 120,
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  p: 2,
                  borderRadius: 2,
                  transition: "all 0.2s",
                  cursor: "pointer",
                  textDecoration: "none",
                  color: "inherit",
                  "&:hover": {
                    bgcolor: "primary.main",
                    color: "white",
                  },
                }}
              >
                <Icon size={24} />
                <Typography
                  variant="body2"
                  fontWeight={500}
                  textTransform="capitalize"
                  sx={{ mt: 1, textAlign: "center" }}
                >
                  {label.replace(/-/g, " ")}
                </Typography>
              </Paper>
            </Link>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}
