// components/GenericTable.tsx
"use client";

import {
  MaterialReactTable,
  MRT_ColumnDef,
  // MRT_CopyButton,
  // MRT_ExpandAllButton,
  MRT_ShowHideColumnsButton,
  MRT_ToggleDensePaddingButton,
  MRT_ToggleFiltersButton,
  MRT_ToggleFullScreenButton,
  MRT_ToggleGlobalFilterButton,
  useMaterialReactTable,
} from "material-react-table";
import {
  Box,
  // Button,
  createTheme,
  IconButton,
  Tooltip,
  Typography,
  useTheme,
  ThemeProvider,
} from "@mui/material";
// import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { GridAddIcon } from "@mui/x-data-grid";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import { useMemo, useState } from "react";
import type { ColumnFiltersState } from "@tanstack/react-table";
import PrintIcon from "@mui/icons-material/Print";
// import AddBoxIcon from "@mui/icons-material/AddBox";

interface CommonTableProps<TData extends Record<string, unknown>> {
  title: string;
  columns: MRT_ColumnDef<TData, unknown>[];
  useDataQuery: (
    page: number,
    limit: number,
    filters?: Record<string, unknown>
  ) => { data?: { data: TData[]; total: number }; isLoading: boolean };
  hiddenColumns?: string[];
  onCreate?: () => void;
  onExport?: () => void;
  onEdit?: (row: TData) => void;
  onDelete?: (row: TData) => void;
  onFilterChange?: (filters: ColumnFiltersState) => void;
  isAction?: boolean;
  isFilterable?: boolean;
  isRowAction?: boolean;
  isCellAction?: boolean;
  initialFilters?: ColumnFiltersState;
  isCreate?: boolean;
  isEdit?: boolean;
  isDelete?: boolean;
}

export function CommonTable<TData extends Record<string, unknown>>({
  title,
  columns,
  useDataQuery,
  hiddenColumns = [],
  onCreate,
  // onExport,
  onEdit,
  onDelete,
  onFilterChange,
  isAction = false,
  isFilterable = true,
  // isRowAction = true,
  isCellAction = false,
  initialFilters = [],
  isCreate = true,
  isEdit = true,
  isDelete = true,
}: CommonTableProps<TData>) {
  const defaultPageSize = 10;
  const [pageIndex, setPageIndex] = useState(0);
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [columnFilters, setColumnFilters] =
    useState<ColumnFiltersState>(initialFilters);

  const globalTheme = useTheme();

  const tableTheme = useMemo(
    () =>
      createTheme({
        palette: {
          mode: globalTheme.palette.mode, //let's use the same dark/light mode as the global theme
          primary: globalTheme.palette.secondary, //swap in the secondary color as the primary for the table
          info: {
            main: "rgb(255,122,0)", //add in a custom color for the toolbar alert background stuff
          },
          background: {
            default:
              globalTheme.palette.mode === "light"
                ? "rgb(194, 210, 25)" //random light yellow color for the background in light mode
                : "#000", //pure black table in dark mode for fun
            paper: "#ffffff",
          },
        },
        typography: {
          button: {
            textTransform: "none", //customize typography styles for all buttons in table by default
            fontSize: "0.4rem",
          },
          fontSize: 12,
        },
        components: {
          MuiTable: {
            styleOverrides: {
              root: {
                backgroundColor: "#ffffff",
              },
            },
          },
          MuiTableHead: {
            styleOverrides: {
              root: {
                backgroundColor: "#f3f4f6", // header background
              },
            },
          },
          MuiTableCell: {
            styleOverrides: {
              root: {
                backgroundColor: "#ffffff", // body cell background
              },
            },
          },
        },
      }),
    [globalTheme]
  );

  const queryFilters = isFilterable
    ? Object.fromEntries(
        columnFilters.flatMap((f) => {
          const isDateRange =
            typeof f.value === "object" &&
            f.value !== null &&
            "start" in f.value &&
            "end" in f.value;

          if (isDateRange) {
            const { start, end } = f.value as {
              start: Date | string;
              end: Date | string;
            };
            const fieldBase = f.id.replace(/At$/, ""); // e.g., createdAt → created

            return [
              [`${fieldBase}From`, new Date(start).toISOString()],
              [`${fieldBase}To`, new Date(end).toISOString()],
            ];
          }

          return [[f.id, f.value]];
        })
      )
    : undefined;

  const { data, isLoading } = useDataQuery(
    pageIndex + 1,
    pageSize,
    queryFilters
  );

  const table = useMaterialReactTable({
    columns: useMemo(() => columns, [columns]),
    data: data?.data || [],
    enablePagination: true,
    manualPagination: true,
    manualFiltering: isFilterable,
    rowCount: data?.total ?? 0,
    enableEditing: isAction,
    enableRowActions: isEdit || isDelete,
    enableCellActions: isCellAction,
    editDisplayMode: "table",
    positionActionsColumn: "last",
    state: {
      isLoading,
      pagination: {
        pageIndex,
        pageSize,
      },
      ...(isFilterable && { columnFilters }),
    },
    initialState: {
      columnVisibility: Object.fromEntries(
        hiddenColumns.map((col) => [col, false])
      ),
    },
    onColumnFiltersChange: (updaterOrValue) => {
      setColumnFilters((oldFilters) => {
        const newFilters =
          typeof updaterOrValue === "function"
            ? updaterOrValue(oldFilters)
            : updaterOrValue;

        onFilterChange?.(newFilters);
        return newFilters;
      });
    },
    onPaginationChange: (updater) => {
      const newPagination =
        typeof updater === "function"
          ? updater({ pageIndex, pageSize })
          : updater;
      setPageIndex(newPagination.pageIndex);
      setPageSize(newPagination.pageSize);
    },
    renderRowActions: ({ row }) => (
      <Box sx={{ display: "flex", gap: "8px" }}>
        {isEdit && (
          <Tooltip title="Edit">
            <IconButton onClick={() => onEdit?.(row.original)}>
              <EditIcon />
            </IconButton>
          </Tooltip>
        )}
        {isDelete && (
          <Tooltip title="Delete">
            <IconButton color="error" onClick={() => onDelete?.(row.original)}>
              <DeleteIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    ),
    renderTopToolbarCustomActions: () => (
      <Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
        <Typography>{title}</Typography>
      </Box>
    ),
    renderToolbarInternalActions: ({ table }) => (
      <>
        <MRT_ToggleGlobalFilterButton table={table} />
        <MRT_ToggleFiltersButton table={table} />
        <MRT_ToggleDensePaddingButton table={table} />
        <MRT_ShowHideColumnsButton table={table} />
        <MRT_ToggleFullScreenButton table={table} />
        <Tooltip title="Print">
          <IconButton onClick={() => window.print()}>
            <PrintIcon />
          </IconButton>
        </Tooltip>
        {isCreate && (
          <Tooltip title="Add">
            <IconButton onClick={onCreate}>
              <GridAddIcon />
            </IconButton>
          </Tooltip>
        )}
      </>
    ),
    muiTableHeadCellProps: ({ column }) =>
      column.id === "mrt-row-actions"
        ? {
            sx: {
              position: "sticky",
              right: 0,
              zIndex: 11,
              backgroundColor: "#fff",
            },
          }
        : {},
    muiTableBodyCellProps: ({ column }) =>
      column.id === "mrt-row-actions"
        ? {
            sx: {
              position: "sticky",
              right: 0,
              zIndex: 10,
              backgroundColor: "#fff",
            },
          }
        : {},
  });

  return (
    <ThemeProvider theme={tableTheme}>
      <Box sx={{ pb: 2 }}>
        <MaterialReactTable table={table} />
      </Box>
    </ThemeProvider>
  );
}
