import { BaseResponse, PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";
import { ItemBatchResponse } from "@/app/inventory/item-batch/types/item-batch.types";
import { ItemUOMResponse } from "@/app/inventory/item-uom/types/item-uom.types";
import { ItemResponse } from "@/app/inventory/items/types/items.types";
import { StoreResponse } from "@/app/inventory/stores/types/stores.types";
import z from "zod";

export const saleItemSchema = z.object({
  itemId: z
    .number({ invalid_type_error: "Item Name is required" })
    .int()
    .positive({ message: "Item Name must be a valid item ID" }),

  storeId: z
    .number({ invalid_type_error: "Sub Location is required" })
    .int()
    .positive({ message: "Sub Location must be a valid store ID" }),

  itemBatchId: z
    .number({ invalid_type_error: "Batch No is required" })
    .int()
    .positive({ message: "Batch No must be a valid batch ID" }),

  itemUomId: z
    .number({ invalid_type_error: "UOM is required" })
    .int()
    .positive({ message: "UOM must be a valid unit ID" }),
  expiryDate: z.string().regex(/^(0[1-9]|1[0-2])\/\d{2}$/, {
    message: "Expiry Date must be in MM/YY format",
  }),
  qty: z
    .number({ invalid_type_error: "Qty is required" })
    .positive({ message: "Qty must be greater than 0" }),
  mrp: z
    .number({ invalid_type_error: "MRP(SU) is required" })
    .nonnegative({ message: "MRP must be zero or greater" }),
  discount: z.number().nonnegative({ message: "MRP must be zero or greater" }),
  subTotal: z.number().nonnegative({ message: "MRP must be zero or greater" }),
});

export type SaleItemRequest = z.infer<typeof saleItemSchema>;
export interface SaleItemResponse extends BaseResponse, SaleItemRequest, Record<string, unknown>{
  store?: StoreResponse;
  item?: ItemResponse;
  itemBatch?: ItemBatchResponse;
  itemUom?: ItemUOMResponse;
}
export type SaleItemPaginatedResponse =
  PaginatedResponse<SaleItemResponse>;
export type SaleItemSuccessResponse =
  SuccessResponse<SaleItemPaginatedResponse>;

export interface SaleItemFilters {
  id?: number;
}