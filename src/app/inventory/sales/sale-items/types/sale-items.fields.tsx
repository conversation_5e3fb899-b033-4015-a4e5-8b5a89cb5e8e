"use client";

import { StoreResponse } from "@/app/inventory/stores/types/stores.types";
import { useListStores } from "@/app/inventory/stores/query/stores.query";
import { CommonAutoComplete } from "@/app/common/dropdown/CommonAutoComplete";

import { InputNumber, DatePicker } from "antd";
import dayjs from "dayjs";
import { MRT_ColumnDef } from "material-react-table";
import { DynamicColumn } from "@/app/common/types/common.types";
import { ItemResponse } from "@/app/inventory/items/types/items.types";
import { useListItems } from "@/app/inventory/items/query/items.query";
import { ItemBatchResponse } from "@/app/inventory/item-batch/types/item-batch.types";
import { useListItemBatches } from "@/app/inventory/item-batch/query/item-batch.query";
import { ItemUOMResponse } from "@/app/inventory/item-uom/types/item-uom.types";
import { useListItemUOM } from "@/app/inventory/item-uom/query/item-uom.query";
import { SaleItemResponse } from "./sale-items.types";

export const SaleItemColumns = (
  rows: SaleItemResponse[] = [],
  setRows: React.Dispatch<React.SetStateAction<SaleItemResponse[]>> = () => {}
): DynamicColumn[] => {
  const handleChange = (
    index: number,
    field: keyof SaleItemResponse,
    value: unknown
  ) => {
    if (rows) {
      const updated = [...rows];
      updated[index][field] = value;
      setRows(updated);
    }
  };

  function addRows() {
    const saleItems: SaleItemResponse = {
        id: 0,
        storeId: 0,
        itemId: 0,
        itemBatchId: 0,
        itemUomId: 0,
        expiryDate: new Date().toISOString(),
        qty: 0,
        purchasePrice: 0,
        mrp: 0,
        createdAt: "",
        updatedAt: "",
        createdBy: 0,
        updatedBy: 0,
        discount: 0,
        subTotal: 0
    };
    setRows([...rows, saleItems]);
  }

  return [
    { accessorKey: "id", header: "ID", isEditable: false },
    {
      accessorKey: "item.name",
      header: "Item",
      Edit: (rowIndex: number) => {
        console.log(rows[rowIndex]);
        return rows[rowIndex] ? (
          <CommonAutoComplete<ItemResponse, number>
            value={rows[rowIndex].itemId}
            onChange={(val: number | "") =>
              handleChange(rowIndex, "itemId", val || 0)
            }
            useDataQuery={useListItems}
            labelKey="name"
            valueKey="id"
          />
        ) : null;
      },
    },
    {
      accessorKey: "store.name",
      header: "Sub Location",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <CommonAutoComplete<StoreResponse, number>
            value={rows[rowIndex].storeId}
            onChange={(val: number | "") =>
              handleChange(rowIndex, "storeId", val || 0)
            }
            useDataQuery={useListStores}
            labelKey="name"
            valueKey="id"
          />
        ) : null,
    },
    {
      accessorKey: "itembatch.batchNumber",
      header: "Item Batch",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <CommonAutoComplete<ItemBatchResponse, number>
            value={rows[rowIndex].itemBatchId}
            onChange={(val: number | "") =>
              handleChange(rowIndex, "itemBatchId", val || 0)
            }
            useDataQuery={useListItemBatches}
            labelKey="batchNumber"
            valueKey="id"
          />
        ) : null,
    },
    {
      accessorKey: "uom.name",
      header: "UoM",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <CommonAutoComplete<ItemUOMResponse, number>
            value={rows[rowIndex].itemUomId}
            onChange={(val: number | "") =>
              handleChange(rowIndex, "itemUomId", val || 0)
            }
            useDataQuery={useListItemUOM}
            labelKey="uom.name"
            valueKey="id"
          />
        ) : null,
    },
    {
      accessorKey: "expiryDate",
      header: "Expiry Date",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <DatePicker
            picker="month"
            format="MM/YY"
            minDate={dayjs().startOf("month")}
            value={
              rows[rowIndex].expiryDate
                ? dayjs(rows[rowIndex].expiryDate)
                : null
            }
            onChange={(date) =>
              handleChange(rowIndex, "expiryDate", date?.toISOString() || "")
            }
            allowClear
          />
        ) : null,
    },
    {
      accessorKey: "qty",
      header: "Qty",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <InputNumber
            min={0}
            value={rows[rowIndex].qty}
            onChange={(val) => handleChange(rowIndex, "qty", val)}
          />
        ) : null,
    },
    {
      accessorKey: "ucp",
      header: "UCP (PU)",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <InputNumber
            min={0}
            value={rows[rowIndex].mrp}
            onChange={(val) => handleChange(rowIndex, "ucp", val)}
          />
        ) : null,
    },
    {
      accessorKey: "mrp",
      header: "MRP",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <InputNumber
            min={0}
            value={rows[rowIndex].mrp}
            onChange={(val) => handleChange(rowIndex, "mrp", val)}
            onKeyDown={(e) => {
              const isLastRow = rowIndex === rows.length - 1;
              if (e.key === "Tab" && !e.shiftKey && isLastRow) {
                setTimeout(() => addRows(), 0); // ensure row is added after focus moves
              }
            }}
          />
        ) : null,
    },
    {
      accessorKey: "discount",
      header: "Discount",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <InputNumber
            min={0}
            value={rows[rowIndex].mrp}
            onChange={(val) => handleChange(rowIndex, "mrp", val)}
            onKeyDown={(e) => {
              const isLastRow = rowIndex === rows.length - 1;
              if (e.key === "Tab" && !e.shiftKey && isLastRow) {
                setTimeout(() => addRows(), 0); // ensure row is added after focus moves
              }
            }}
          />
        ) : null,
    },
    {
      accessorKey: "subTotal",
      header: "Sub Total",
      Edit: (rowIndex: number) =>
        rows[rowIndex] ? (
          <InputNumber
            min={0}
            value={rows[rowIndex].mrp}
            onChange={(val) => handleChange(rowIndex, "mrp", val)}
            onKeyDown={(e) => {
              const isLastRow = rowIndex === rows.length - 1;
              if (e.key === "Tab" && !e.shiftKey && isLastRow) {
                setTimeout(() => addRows(), 0); // ensure row is added after focus moves
              }
            }}
          />
        ) : null,
    },
  ];
};

export const openingStockItemColumns: MRT_ColumnDef<SaleItemResponse>[] = [
  { accessorKey: "store.name", header: "Store Name" },
  { accessorKey: "item.name", header: "Item Name" },
  { accessorKey: "itembatch.batchNumber", header: "Batch Number" },
  { accessorKey: "uom.name", header: "UoM" },
  { accessorKey: "expiryDate", header: "Expiry Date" },
  { accessorKey: "qty", header: "Qty" },
  { accessorKey: "purchasePrice", header: "Purchase Price" },
  { accessorKey: "mrp", header: "MRP" },
  { accessorKey: "createdAt", header: "Created At" },
  { accessorKey: "updatedAt", header: "Updated At" },
  { accessorKey: "createdBy", header: "Created By" },
  { accessorKey: "updatedBy", header: "Updated By" },
];
