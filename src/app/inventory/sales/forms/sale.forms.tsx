"use client";

import React from "react";
import { use<PERSON><PERSON>, Controller, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  saleSchema,
  SaleRequest,
} from "../types/sale.types";
// import {
//   useCreateSale,
//   useUpdateSale,
// } from "../query/opening-stock.query";
import { Form, Input, Typography, InputNumber } from "antd";
//import dayjs from "dayjs";
import { Grid, Box } from "@mui/material";
//import { useListStores } from "../../stores/query/stores.query";
//import { StoreResponse } from "../../stores/types/stores.types";
//import { CommonAutoComplete } from "@/app/common/dropdown/CommonAutoComplete";
import SaleItemsForm from "../sale-items/forms/sale-items.forms";

/* ---- add these imports near the top of SaleForm.tsx ---- */
import {
  DollarSign, CreditCard, QrCode,
  FileText, Printer,
  Share2, Bell,
  User, Hospital, Pill,
  FileClock, CheckCircle2, XCircle, RotateCcw,
} from "lucide-react";
import { Tooltip } from "@mui/material";



export default function SaleForm() {
  // const createSaleMutation = useCreateSale();
  // const updateSaleMutation = useUpdateSale();

  const {
    control,
    handleSubmit,
    // reset,
    formState: { errors },
  } = useForm<SaleRequest>({
    resolver: zodResolver(saleSchema),
    defaultValues: {
      patientName: "",
      age: "",
      uhid: "",
      mobile: "",
      encounterType: "",
      encounterId: "",
      payer: "",
      payee: "",
      date: "",
      billNumber: "",
      status: "",
      storeId: "",
      billCounter: "",
      dr: "",
      gross: 0,
      discount: 0,
      roundOff: 0,
      netAmount: 0,
      paidAmount: 0,
      dueAmount: 0,
    },
  });

  const onSubmit: SubmitHandler<SaleRequest> = (data) => {
    console.log("Form Submitted:", data);
  };

  // const handleCancel = () => {
  //   reset();
  // };

  return (
    <>
      <Box
        component="form"
        onSubmit={handleSubmit(onSubmit)}
        noValidate
        sx={{
          p: 3,
          backgroundColor: "#f9f9f9",
          borderRadius: 2,
          boxShadow: 2,
          maxWidth: "100%",
          margin: "auto",
          my: 2,
        }}
      >
        <Typography.Text style={{ fontWeight: "bold", fontSize: "1rem" }}>
          Create Sale
        </Typography.Text>

        <Grid container spacing={3}>
          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="patientName"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.patientName ? "error" : ""}
                  help={errors.patientName?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>
                    Patient Name
                  </div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="age"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.age ? "error" : ""}
                  help={errors.age?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>Age/Sex</div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="uhid"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.uhid ? "error" : ""}
                  help={errors.uhid?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>
                    UHID
                  </div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="mobile"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.mobile ? "error" : ""}
                  help={errors.mobile?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>
                    Mobile Number
                  </div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="encounterType"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.encounterType ? "error" : ""}
                  help={errors.encounterType?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>
                    Encounter Type
                  </div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="encounterId"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.encounterId ? "error" : ""}
                  help={errors.encounterId?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>
                    Encounter Id
                  </div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="payer"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.payer ? "error" : ""}
                  help={errors.payer?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>
                    Payer
                  </div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="payee"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.payee ? "error" : ""}
                  help={errors.payee?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>
                    Payee
                  </div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="date"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.date ? "error" : ""}
                  help={errors.date?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>
                    Date
                  </div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="billNumber"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.billNumber ? "error" : ""}
                  help={errors.billNumber?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>
                    Bill Number
                  </div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, sm: 6, md: 4, lg: 2 }}>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Form.Item
                  validateStatus={errors.status ? "error" : ""}
                  help={errors.status?.message}
                  style={{ marginBottom: 0 }}
                >
                  <div>
                    Status
                  </div>
                  <Input {...field} />
                </Form.Item>
              )}
            />
          </Grid>
        </Grid>
      </Box>

      <SaleItemsForm />
      <Grid
        size={{ xs: 12 }}
        sx={{ mt: 2, position: "relative" }}
      >
        <Box
          sx={{
            position: "absolute",
            bottom: 8,
            left: 8,
            display: "flex",
            gap: 1,
            flexWrap: "wrap",
            alignItems: "center",
            "& svg": { cursor: "pointer" },
          }}
        >
          {/* payment type */}
          <Tooltip title="Cash"><DollarSign size={18} /></Tooltip>
          <Tooltip title="Digital"><CreditCard size={18} /></Tooltip>
          <Tooltip title="Dynamic QR"><QrCode size={18} /></Tooltip>

          {/* credit bill */}
          <Tooltip title="Credit Bill"><FileText size={18} /></Tooltip>

          {/* print (thermal / A4 / A5) */}
          <Tooltip title="Print – Thermal"><Printer size={18} /></Tooltip>
          <Tooltip title="Print – A4"><Printer size={18}/></Tooltip>
          <Tooltip title="Print – A5"><Printer size={18}/></Tooltip>

          {/* share / notify */}
          <Tooltip title="Share"><Share2 size={18} /></Tooltip>
          <Tooltip title="Notify"><Bell size={18} /></Tooltip>

          {/* due amounts */}
          <Tooltip title="OP Due"><User size={18} /></Tooltip>
          <Tooltip title="IP Due"><Hospital size={18} /></Tooltip>
          <Tooltip title="Pharma Due"><Pill size={18} /></Tooltip>

          {/* bill status */}
          <Tooltip title="Draft"><FileClock size={18} /></Tooltip>
          <Tooltip title="Final"><CheckCircle2 size={18} /></Tooltip>
          <Tooltip title="Cancel"><XCircle size={18} /></Tooltip>
          <Tooltip title="Refund"><RotateCcw size={18} /></Tooltip>
        </Box>
      </Grid>

      <Box
        sx={{
          width: "20%",
          ml: "auto", // push to right
          mt: 4,
          backgroundColor: "#fff",
          p: 2,
          borderRadius: 2,
          boxShadow: 1,
        }}
      >
        <Grid container direction="column" spacing={2}>
          {[
            { name: "gross", label: "Gross" },
            { name: "discount", label: "Discount" },
            { name: "roundOff", label: "Round Off" },
            { name: "netAmount", label: "Net" },
            { name: "paidAmount", label: "Paid" },
            { name: "dueAmount", label: "Due" },
          ].map(({ name, label }) => (
            <Grid key={name}>
              <Controller
                name={name as keyof SaleRequest}
                control={control}
                render={({ field }) => (
                  <Form.Item
                    label={label}
                    style={{ marginBottom: 0 }}
                    validateStatus={errors?.[name as keyof SaleRequest] ? "error" : ""}
                    help={errors?.[name as keyof SaleRequest]?.message}
                    labelCol={{ span: 12 }}
                    wrapperCol={{ span: 12 }}
                  >
                    <InputNumber
                      {...field}
                      style={{ width: "100%" }}
                      formatter={(value) =>
                        `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                      }
                      parser={(value) =>
                        parseFloat(value?.replace(/₹\s?|(,*)/g, "") || "0")
                      }
                    />
                  </Form.Item>
                )}
              />
            </Grid>
          ))}
        </Grid>
      </Box>
    </>
  );
}