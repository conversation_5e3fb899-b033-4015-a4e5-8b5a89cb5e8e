import {
    BaseResponse,
    PaginatedResponse,
    SuccessResponse,
  } from "@/app/common/types/common.types";
  import { MRT_ColumnDef } from "material-react-table";
  import { z } from "zod";
  
  export const saleSchema = z.object({
    patientName: z.string(),
    age: z.string(),
    uhid: z.string(),
    mobile: z.string(),
    encounterType: z.string(),
    encounterId: z.string(),
    payer: z.string(),
    payee: z.string(),
    date: z.string(),
    billNumber: z.string(),
    status: z.string(),
    storeId: z.string(),
    billCounter: z.string(),
    dr: z.string(),

    gross: z.number(),
  discount: z.number(),
  roundOff: z.number(),
  netAmount: z.number(),
  paidAmount: z.number(),
  dueAmount: z.number(),
  });
  
  export type SaleRequest = z.infer<typeof saleSchema>;
  export type SaleResponse = BaseResponse &
    SaleRequest &
    Record<string, unknown>;
  export type SalePaginatedResponse =
    PaginatedResponse<SaleResponse>;
  export type SaleSuccessResponse =
    SuccessResponse<SalePaginatedResponse>;
  
  export const saleColumns: MRT_ColumnDef<SaleResponse>[] = [
    { accessorKey: "id", header: "ID", grow: false, size: 50 },
    { accessorKey: "entryTime", header: "Entry Time" },
    { accessorKey: "referenceNo", header: "Referenec No" },
    { accessorKey: "remarks", header: "Remarks" },
    { accessorKey: "store.name", header: "Store" },
    { accessorKey: "actions", header: "Actions" },
  ];
  
  export interface SaleFilters {
    id?: number
  }
  