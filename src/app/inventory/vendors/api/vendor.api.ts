import apiClient from "@/app/api/api";
import { VendorPaginatedResponse, VendorResponse, VendorSuccessResponse, VendorRequest, VendorFilters } from "../types/vendor.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listVendors = async (
  page?: number,
  limit?: number,
  filters?: VendorFilters
): Promise<VendorPaginatedResponse> => {
  const res = await apiClient.get<VendorPaginatedResponse>("/vendors", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};
export const getVendor = async (
  id: number
): Promise<VendorResponse> => {
  const res = await apiClient.get<VendorResponse>(`/vendors/${id}`);
  return res.data;
};

export const createVendor = async (
  vendorRequest: VendorRequest
): Promise<VendorSuccessResponse> => {
  const res = await apiClient.post<VendorSuccessResponse>("/vendors", vendorRequest);
  return res.data;
};

export const updateVendor = async (
  id: number,
  vendorRequest: VendorRequest
): Promise<VendorSuccessResponse> => {
  const res = await apiClient.patch<VendorSuccessResponse>(`/vendors/${id}`, vendorRequest);
  return res.data;
};

export const deleteVendor = async (
  id: number
): Promise<VendorSuccessResponse> => {
  const res = await apiClient.delete<VendorSuccessResponse>(`/vendors/${id}`);
  return res.data;
};