// vendor.hooks.ts
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createVendor,
  deleteVendor,
  getVendor,
  listVendors,
  updateVendor,
} from "../api/vendor.api";
import {
  VendorFilters,
  VendorPaginatedResponse,
  VendorRequest,
  VendorSuccessResponse,
} from "../types/vendor.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListVendors = (
  page?: number,
  limit?: number,
  filters: VendorFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<VendorPaginatedResponse, ApiErrorResponse>({
    queryKey: ["vendors", page, limit, filterKey],
    queryFn: () => listVendors(page, limit, filters),
  });
};

export const useGetVendor = (id: number | undefined) => {
  return useQuery({
    queryKey: ["vendor", id],
    queryFn: () => getVendor(id!),
    enabled: !!id,
  });
};

export const useCreateVendor = () => {
  const queryClient = useQueryClient();
  return useMutation<
    VendorSuccessResponse,
    ApiErrorResponse,
    VendorRequest
  >({
    mutationFn: createVendor,
    mutationKey: ["createVendor"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["vendors"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateVendor = () => {
  const queryClient = useQueryClient();
  return useMutation<
    VendorSuccessResponse,
    ApiErrorResponse,
    { id: number; vendorRequest: VendorRequest }
  >({
    mutationKey: ["updateVendor"],
    mutationFn: ({ id, vendorRequest }) => updateVendor(id, vendorRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["vendors"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteVendor = () => {
  const queryClient = useQueryClient();
  return useMutation<VendorSuccessResponse, ApiErrorResponse, number>(
    {
      mutationKey: ["deleteVendor"],
      mutationFn: (id) => deleteVendor(id),
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: ["vendors"] });
        toast.success(data.message || "Vendor deleted successfully!");
      },
      onError: (error) => {
        toast.error(
          `Delete failed: ${error?.response?.data?.message || error.message}`
        );
      },
    }
  );
};
