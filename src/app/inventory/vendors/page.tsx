"use client";

import { vendorColumns, VendorResponse } from "./types/vendor.types";
import { useListVendors } from "./query/vendor.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteVendor } from "./query/vendor.query";

import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";

function VendorPage() {
  const router = useRouter();
  const deleteMutation = useDeleteVendor();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<VendorResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "Vendor",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);

  const handleOpenDelete = (row: VendorResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable
        title="Vendors"
        columns={vendorColumns}
        useDataQuery={useListVendors}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/inventory/vendors/create")}
        onExport={() => console.log("Export Vendors")}
        onEdit={(row) => router.push(`/inventory/vendors/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete vendor{" "}
            <strong>{selectedRow?.name}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default VendorPage;
