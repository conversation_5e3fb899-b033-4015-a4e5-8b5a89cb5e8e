import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
import { localTime } from '@/app/common/utils/serialize.utils';
import { BaseResponse, PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';

export interface VendorResponse extends BaseResponse, Record<string, unknown> {
  code: string;
  name: string;
  description: string | null;
  status: 'active' | 'inactive';
  organizationId: number;
}

export const vendorColumns: MRT_ColumnDef<VendorResponse>[] = [
  { accessorKey: 'id', header: 'ID' },
  { accessorKey: 'code', header: 'Code' },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'description', header: 'Description' },
  { 
      accessorKey: 'status', 
      header: 'Status',
      filterVariant: 'select',
      filterSelectOptions: statusOptions.map(value => ({
        value,
        label: statusMap[value], // Capitalize first letter
      })),
      Cell: ({ cell }) => {
        const status = cell.getValue<string>();
        return getStatusLabel(status);
      } 
    },
  { accessorKey: 'organization.name', header: 'Organization' },
  {
      accessorKey: "createdAt",
      header: "Created At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Updated At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type VendorPaginatedResponse = PaginatedResponse<VendorResponse>;
export type VendorSuccessResponse = SuccessResponse<VendorResponse>;

export const vendorRequestSchema = z.object({
  name: z.string()
    .min(1, "Name is required")
    .transform((val) => val.trim()),  // Trim whitespace
  description: z.string()
    .nullable()
    .transform((val) => (val != null ? val.trim() : val)),  // Trim if not null/undefined
  status: z.enum(['active', 'inactive']),  // No trim needed for enum
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number")
    .optional(),  // No trim for numbers
});
export type VendorRequest = z.infer<typeof vendorRequestSchema>;
export interface VendorFilters {
  name?: string;
  code?: string;
  description?: string;
  status?: 'active' | 'inactive';
  organizationId?: number;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
}
