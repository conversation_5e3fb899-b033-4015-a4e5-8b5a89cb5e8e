"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uItem,
  <PERSON><PERSON>ield,
  <PERSON>po<PERSON>,
  Card,
  Grid
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateVendor,
  useGetVendor,
  useUpdateVendor,
} from "../query/vendor.query";
import { useRef, useEffect } from "react";
import {
  vendorRequestSchema,
  VendorRequest,
} from "@/app/inventory/vendors/types/vendor.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function VendorForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: vendorData, isLoading } = useGetVendor(id);
  const createMutation = useCreateVendor();
  const updateMutation = useUpdateVendor();

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    // watch,
    formState: { errors },
  } = useForm<VendorRequest>({
    resolver: zodResolver(vendorRequestSchema),
    defaultValues: {
      name: "",
      description: "",
      status: "active",
      //organizationId: 0,
    },
  });

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isEditMode && vendorData) {
      reset({
        name: vendorData.name,
        description: vendorData.description,
        status: vendorData.status,
        organizationId: vendorData.organizationId,
      });
    }
  }, [isEditMode, vendorData, reset]);

  const onSubmit = (data: VendorRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate({ id, vendorRequest: data },
        {
          onSuccess: () => {
            // After successful edit, navigate back to the list page
            router.push("/inventory/vendors");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          // After successful create, navigate back to the list page
          reset();
          router.push("/inventory/vendors");
        },
      });
    }
  };
  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

return (
  <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
    <Typography variant="h6" mb={3}>
      {isEditMode ? "Edit Vendor" : "Create Vendor"}
    </Typography>

    <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
      <Grid container spacing={2}>
        {/* Code (Edit Mode Only) */}
        {isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <TextField
              label="Code"
              size="small"
              fullWidth
              value={vendorData?.code || ""}
              disabled
            />
          </Grid>
        )}

        {/* Name */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                inputRef={nameRef}
                label="Name"
                size="small"
                fullWidth
                {...field}
                error={!!errors.name}
                helperText={errors.name?.message}
                autoFocus
                required
              />
            )}
          />
        </Grid>

        {/* Status (Edit Mode Only) */}
        {isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <TextField
                  select
                  label="Status"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.status}
                  helperText={errors.status?.message}
                >
                  {statuses.map((opt) => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
        )}

        {/* Organization (Create Mode Only) */}
        {!isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="organizationId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                  label="Organization"
                  value={field.value}
                  onChange={field.onChange}
                  useDataQuery={useListOrganization}
                  labelKey="name"
                  valueKey="id"
                  searchable
                  searchKey="name"
                  error={!!errors.organizationId}
                  helperText={errors.organizationId?.message}
                />
              )}
            />
          </Grid>
        )}

        {/* Description (Full Width) */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <TextField
                label="Description"
                size="small"
                multiline
                rows={4}
                fullWidth
                {...field}
                value={field.value || ""}
                error={!!errors.description}
                helperText={errors.description?.message}
              />
            )}
          />
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box
        sx={{
          display: "flex",
          gap: 2,
          justifyContent: "flex-end",
          mt: 3,
        }}
      >
        <Button
          variant="outlined"
          color="inherit"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={createMutation.isPending || updateMutation.isPending}
        >
          {isEditMode ? "Update" : "Create"}
        </Button>
      </Box>
    </Box>
  </Card>
);
}