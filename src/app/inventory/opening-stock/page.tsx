"use client";
import { useRouter } from "next/navigation";
import { CommonTable } from "../common/table/common-table";
import { useListOpeningStock } from "./query/opening-stock.query";
import { openingStockColumns, OpeningStockResponse } from "./types/opening-stock.types";

function OpeningStockPage() {
  const router = useRouter();
  return (
    <>
      <CommonTable<OpeningStockResponse>
        title="Opening Stocks"
        columns={openingStockColumns}
        useDataQuery={useListOpeningStock}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => {
          router.push("/inventory/opening-stock/create");
        }}
        onExport={() => console.log("Export Molecular")}
        onEdit={() => {}}
        onDelete={() => {}}
        // isCreate={rolePermissionData?.data[0].create}
        // isEdit={rolePermissionData?.data[0].update}
        // isDelete={rolePermissionData?.data[0].delete}
      />
    </>
  );
}

export default OpeningStockPage;
