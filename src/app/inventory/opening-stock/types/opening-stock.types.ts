import {
  BaseResponse,
  PaginatedResponse,
  SuccessResponse,
} from "@/app/common/types/common.types";
import { MRT_ColumnDef } from "material-react-table";
import { z } from "zod";

export const openingStockSchema = z.object({
  entryTime: z.coerce.date().max(new Date(), { message: "Entry time cannot be in the future" }),

  referenceNo: z
    .string()
    .trim()
    .min(1, { message: "Reference No. is required" })
    .regex(/^[A-Za-z0-9_-]+$/, {
      message: "Reference No. must be alphanumeric (letters, numbers, _ or -)",
    }),

  remarks: z
    .string()
    .trim()
    .max(500, { message: "Remarks cannot exceed 500 characters" })
    .optional(),

  storeId: z
    .number()
    .int()
    .positive({ message: "Store ID must be a positive integer" }),

  actions: z
    .enum(["draft", "submit", "review", "approve", "reject", "cancel"])
    .optional(),
});

export type OpeningStockRequest = z.infer<typeof openingStockSchema>;
export type OpeningStockResponse = BaseResponse &
  OpeningStockRequest &
  Record<string, unknown>;
export type OpeningStockPaginatedResponse =
  PaginatedResponse<OpeningStockResponse>;
export type OpeningStockSuccessResponse =
  SuccessResponse<OpeningStockPaginatedResponse>;

export const openingStockColumns: MRT_ColumnDef<OpeningStockResponse>[] = [
  { accessorKey: "id", header: "ID", grow: false, size: 50 },
  { accessorKey: "entryTime", header: "Entry Time" },
  { accessorKey: "referenceNo", header: "Referenec No" },
  { accessorKey: "remarks", header: "Remarks" },
  { accessorKey: "store.name", header: "Store" },
  { accessorKey: "actions", header: "Actions" },
];

export interface OpeningStockFilters {
  id?: number
}
