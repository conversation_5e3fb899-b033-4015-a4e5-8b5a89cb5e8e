"use client";

import React from "react";
import { useF<PERSON>, Controller, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  openingStockSchema,
  OpeningStockRequest,
} from "../types/opening-stock.types";
// import {
//   useCreateOpeningStock,
//   useUpdateOpeningStock,
// } from "../query/opening-stock.query";
import { Form, Input, DatePicker, Typography } from "antd";
import dayjs from "dayjs";
import { Grid, Box } from "@mui/material";
import { useListStores } from "../../stores/query/stores.query";
import { StoreResponse } from "../../stores/types/stores.types";
import { CommonAutoComplete } from "@/app/common/dropdown/CommonAutoComplete";

export default function OpeningStockForm() {
  // const createOpeningStockMutation = useCreateOpeningStock();
  // const updateOpeningStockMutation = useUpdateOpeningStock();

  const {
    control,
    handleSubmit,
    // reset,
    formState: { errors },
  } = useForm<OpeningStockRequest>({
    resolver: zodResolver(openingStockSchema),
    defaultValues: {
      entryTime: new Date(),
      referenceNo: "",
      remarks: "",
      storeId: 0,
      actions: "draft",
    },
  });

  const onSubmit: SubmitHandler<OpeningStockRequest> = (data) => {
    console.log("Form Submitted:", data);
  };

  // const handleCancel = () => {
  //   reset();
  // };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit(onSubmit)}
      noValidate
      sx={{
        p: 3,
        backgroundColor: "#f9f9f9",
        borderRadius: 2,
        boxShadow: 2,
        maxWidth: "100%",
        margin: "auto",
        my: 2,
      }}
    >
      <Typography.Text style={{ fontWeight: "bold", fontSize: "1rem" }}>
        Create Opening Stock
      </Typography.Text>

      <Grid container spacing={3}>
        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Controller
            name="storeId"
            control={control}
            render={({ field }) => (
              <Form.Item
                validateStatus={errors.storeId ? "error" : ""}
                help={errors.storeId?.message}
                style={{ marginBottom: 0 }}
              >
                <div>Store</div>
                <CommonAutoComplete<StoreResponse, number>
                  useDataQuery={useListStores}
                  {...field}
                  labelKey="name"
                  valueKey="id"
                />
              </Form.Item>
            )}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Controller
            name="entryTime"
            control={control}
            render={({ field }) => (
              <Form.Item
                validateStatus={errors.entryTime ? "error" : ""}
                help={errors.entryTime?.message}
                style={{ marginBottom: 0 }}
              >
                <div>
                  Entry Time
                </div>
                <DatePicker
                  showTime
                  style={{ width: "100%" }}
                  {...field}
                  value={dayjs(field.value)}
                  onChange={(date) => field.onChange(date?.toDate())}
                />
              </Form.Item>
            )}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Controller
            name="referenceNo"
            control={control}
            render={({ field }) => (
              <Form.Item
                validateStatus={errors.referenceNo ? "error" : ""}
                help={errors.referenceNo?.message}
                style={{ marginBottom: 0 }}
              >
                <div>
                  Reference No
                </div>
                <Input {...field} />
              </Form.Item>
            )}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Controller
            name="remarks"
            control={control}
            render={({ field }) => (
              <Form.Item
                validateStatus={errors.remarks ? "error" : ""}
                help={errors.remarks?.message}
                style={{ marginBottom: 0 }}
              >
                <div>Remarks</div>
                <Input {...field} />
              </Form.Item>
            )}
          />
        </Grid>

        {/* <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Controller
            name="actions"
            control={control}
            render={({ field }) => (
              <Form.Item
                validateStatus={errors.actions ? "error" : ""}
                help={errors.actions?.message}
                style={{ marginBottom: 0 }}
              >
                <div style={{ marginBottom: 4, fontWeight: 500 }}>Action</div>
                <Select {...field} value={field.value || "draft"}>
                  {[
                    "draft",
                    "submit",
                    "review",
                    "approve",
                    "reject",
                    "cancel",
                  ].map((action) => (
                    <Option key={action} value={action}>
                      {action.charAt(0).toUpperCase() + action.slice(1)}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}
          />
        </Grid> */}

        {/* <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
          <Box display="flex" justifyContent="flex-end" gap={2} mt={2}>
            <Button type="primary" htmlType="submit">
              Submit
            </Button>
            <Button htmlType="button" onClick={handleCancel}>
              Cancel
            </Button>
          </Box>
        </Grid> */}
      </Grid>
    </Box>
  );
}