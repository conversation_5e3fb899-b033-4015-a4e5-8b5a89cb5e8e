import apiClient from "@/app/api/api";
import { OpeningStockPaginatedResponse, OpeningStockResponse, OpeningStockSuccessResponse, OpeningStockRequest, OpeningStockFilters } from "../types/opening-stock.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listOpeningStock = async (
  page?: number,
  limit?: number,
  filters?: OpeningStockFilters
): Promise<OpeningStockPaginatedResponse> => {
  const res = await apiClient.get<OpeningStockPaginatedResponse>("/opening-stocks", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};

export const getOpeningStock = async (
  id: number
): Promise<OpeningStockResponse> => {
  const res = await apiClient.get<OpeningStockResponse>(`/opening-stocks/${id}`);
  return res.data;
}

export const createOpeningStock = async (
  openingStockRequest: OpeningStockRequest
): Promise<OpeningStockSuccessResponse> => {
  const res = await apiClient.post<OpeningStockSuccessResponse>("/opening-stocks", openingStockRequest);
  return res.data;
}

export const updateOpeningStock = async (
  id: number,
  openingStockRequest: OpeningStockRequest
): Promise<OpeningStockSuccessResponse> => {
  const res = await apiClient.patch<OpeningStockSuccessResponse>(`/opening-stocks/${id}`, openingStockRequest);
  return res.data;
}

export const deleteOpeningStock = async (
  id: number
): Promise<OpeningStockSuccessResponse> => {
  const res = await apiClient.delete<OpeningStockSuccessResponse>(`/opening-stocks/${id}`);
  return res.data;
};