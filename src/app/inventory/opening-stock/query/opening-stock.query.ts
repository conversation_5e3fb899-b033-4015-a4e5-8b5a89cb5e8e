import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createOpeningStock,
  deleteOpeningStock,
  getOpeningStock,
  listOpeningStock,
  updateOpeningStock,
} from "../api/opening-stock.api";
import {
  OpeningStockFilters,
  OpeningStockPaginatedResponse,
  OpeningStockRequest,
  OpeningStockResponse,
  OpeningStockSuccessResponse,
} from "../types/opening-stock.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";
import { serialize } from "@/app/common/utils/serialize.utils";

export const useListOpeningStock = (
  page?: number,
  limit?: number,
  filters: OpeningStockFilters = {}
) => {
  const filterKey = serialize(filters as Record<string, unknown>);
  return useQuery<OpeningStockPaginatedResponse, ApiErrorResponse>({
    queryKey: ["opening-stocks", page, limit, filterKey],
    queryFn: () => listOpeningStock(page, limit, filters),
  });
};

export const useGetOpeningStock = (id: number | undefined) => {
  return useQuery<OpeningStockResponse, ApiErrorResponse>({
    queryKey: ["opening-stock", id],
    queryFn: () => getOpeningStock(id!),
    enabled: !!id,
  });
};

export const useCreateOpeningStock = () => {
  const queryClient = useQueryClient();
  return useMutation<
    OpeningStockSuccessResponse,
    ApiErrorResponse,
    OpeningStockRequest
  >({
    mutationKey: ["createOpeningStock"],
    mutationFn: (openingStockRequest: OpeningStockRequest) =>
      createOpeningStock(openingStockRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["opening-stocks"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create opening-stock");
    },
  });
};

export const useUpdateOpeningStock = () => {
  const queryClient = useQueryClient();
  return useMutation<
    OpeningStockSuccessResponse,
    ApiErrorResponse,
    { id: number; openingStockRequest: OpeningStockRequest }
  >({
    mutationKey: ["updateOpeningStock"],
    mutationFn: ({
      id,
      openingStockRequest,
    }: {
      id: number;
      openingStockRequest: OpeningStockRequest;
    }) => updateOpeningStock(id, openingStockRequest), // Assuming update uses the same API as create
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["opening-stocks"] }); // Invalidate the list query to refresh data
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update opening-stock");
    },
  });
};

export const useDeleteOpeningStock = () => {
  const queryClient = useQueryClient();
  return useMutation<OpeningStockSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteOpeningStock"],
    mutationFn: (id: number) => deleteOpeningStock(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["opening-stocks"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete opening-stock");
    },
  });
};
