"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uItem,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
  Card
} from "@mui/material";
import Grid from '@mui/material/Grid';
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateCategory,
  useGetCategory,
  useUpdateCategory,
} from "../query/category.query";
import { useEffect } from "react";
import {
  categoryRequestSchema,
  CategoryRequest,
} from "@/app/inventory/category/types/category.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";

type Props = {
  id?: number;
};


const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function CategoryForm({ id }: Props) {
  const isEditMode = !!id;
  const router = useRouter();
  const { data: categoryData, isLoading } = useGetCategory(id);
  const createMutation = useCreateCategory();
  const updateMutation = useUpdateCategory();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CategoryRequest>({
    resolver: zodResolver(categoryRequestSchema),
    defaultValues: {
      name: "",
      description: "",
      status: "active",
      organizationId: undefined,
    },
  });

  // Reset with fetched data if editing
  useEffect(() => {
    if (isEditMode && categoryData) {
      const formattedData = {
        ...categoryData,
        organizationId: categoryData.organizationId === null ? undefined : categoryData.organizationId,
      };
      reset(formattedData);
    }
  }, [isEditMode, categoryData, reset]);

  const onSubmit = (formData: CategoryRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, categoryRequest: formData },
        {
          onSuccess: () => {
            router.push("/inventory/category");
          },
        }
      );
    } else {
      createMutation.mutate(formData, {
        onSuccess: () => {
          reset();
          router.push("/inventory/category");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Category" : "Create Category"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Name */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Name"
                  size="small"
                  fullWidth
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  required
                  autoFocus
                />
              )}
            />
          </Grid>

          {isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    {statuses.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
          )}

          {!isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
    <Controller
      name="organizationId"
      control={control}
      render={({ field }) => (
        <CommonDropdown<
          OrganizationResponse,
          OrganizationPaginatedResponse,
          number
          >
          label="Organization"
          value={field.value === null ? undefined : field.value}
          onChange={field.onChange}
          useDataQuery={useListOrganization}
          labelKey="name"
          valueKey="id"
          searchable
          searchKey="name"
          error={!!errors.organizationId}
          helperText={errors.organizationId?.message}
        />
      )}
    />
  </Grid>
)}


          {/* Description */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Description"
                  size="small"
                  multiline
                  rows={4}
                  fullWidth
                  error={!!errors.description}
                  helperText={errors.description?.message}
                />
              )}
            />
          </Grid>
        </Grid>

        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}