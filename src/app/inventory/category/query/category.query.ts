import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createCategory,
  deleteCategory,
  getCategory,
  listCategory,
  updateCategory,
} from "../api/category.api";
import { toast } from "react-hot-toast";
import {
  SuccessResponse,
} from "@/app/common/types/common.types";
import {
  CategoryPaginatedResponse,
  CategoryRequest,
  CategoryResponse,
  CategoryFilters,
} from "@/app/inventory/category/types/category.types";
import { AxiosError } from "axios";
import { ApiError } from "next/dist/server/api-utils";

export const useListCategory = (
  page?: number,
  limit?: number,
  filters: CategoryFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery<CategoryPaginatedResponse, AxiosError<ApiError>>({
    queryKey: ["categories", page, limit, filterKey],
    queryFn: () => listCategory(page, limit, filters),
  });
};

export const useGetCategory = (id: number | undefined) => {
  return useQuery<CategoryResponse, AxiosError<ApiError>>({
    queryKey: ["category", id],
    queryFn: () => getCategory(id!),
    enabled: !!id,
  });
};

export const useCreateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<CategoryResponse>,
    AxiosError<ApiError>,
    CategoryRequest
  >({
    mutationKey: ["createCategory"],
    mutationFn: (categoryRequest) => createCategory(categoryRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      toast.success(response.message || "Category created successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create category");
    },
  });
};


export const useUpdateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<CategoryResponse>,
    AxiosError<ApiError>,
    { id: number; categoryRequest: CategoryRequest }
  >({
    mutationKey: ["updateCategory"],
    mutationFn: ({ id, categoryRequest }) =>
      updateCategory(id, categoryRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      toast.success(response.message || "Category updated successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update category");
    },
  });
};

export const useDeleteCategory = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<CategoryResponse>,
    AxiosError<ApiError>,
    number
  >({
    mutationKey: ["deleteCategory"],
    mutationFn: (id) => deleteCategory(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      toast.success(response.message || "Category deleted successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete category");
    },
  });
};

