import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";
import { statusOptions, statusMap, getStatusLabel } from '@/app/common/types/status.types';
import { SuccessResponse, PaginatedResponse, BaseResponse } from "@/app/common/types/common.types";
import { localTime } from '@/app/common/utils/serialize.utils';

export interface CategoryResponse extends BaseResponse, Record<string, unknown> {
  code: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
  organizationId: number | null;
}

export const categoryColumns: MRT_ColumnDef<CategoryResponse>[] = [
  { accessorKey: 'id', header: 'ID', grow: false, size: 50 },
  { accessorKey: 'code', header: 'Code' },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'description', header: 'Description' },
  { 
    accessorKey: 'status', 
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions.map(value => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    }
  },
  {
      accessorKey: "createdAt",
      header: "Created At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },  {
      accessorKey: "updatedAt",
      header: "Updated At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
    { accessorKey: 'organization.name', header: 'Organization'},
  { accessorKey: 'createdBy', header: 'Created By', grow: false, size: 50 },
  { accessorKey: 'updatedBy', header: 'Updated By', grow: false, size: 50 },
];

export type CategoryPaginatedResponse = PaginatedResponse<CategoryResponse>;
export type CategorySuccessResponse = SuccessResponse<CategoryResponse>;

export const categoryRequestSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(100, "Name must be less than 100 characters")
    .transform((val) => val.trim()),

  description: z
    .string()
    .max(255, "Description must be less than 255 characters")
    .nullable()
    .transform((val) => (val === null ? null : val.trim())),

  status: z.enum(['active', 'inactive']),

  organizationId: z
    .number()
    .int()
    .positive("Organization ID must be a positive number")
    .optional(),
});


export type CategoryRequest = z.infer<typeof categoryRequestSchema>;

export interface CategoryFilters {
  name?: string;
  code?: string;
  status?: 'active' | 'inactive';
  organizationId?: number | null;
  createdBy?: number;
  updatedBy?: number;
  createdAt?: string;
  updatedAt?: string;
}