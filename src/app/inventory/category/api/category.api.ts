import apiClient from "@/app/api/api";
import { 
  CategoryPaginatedResponse, 
  CategoryResponse, 
  CategorySuccessResponse, 
  CategoryRequest,
  CategoryFilters 
} from "../types/category.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listCategory = async (
  page?: number,
  limit?: number,
  filters?: CategoryFilters
): Promise<CategoryPaginatedResponse> => {
  const res = await apiClient.get<CategoryPaginatedResponse>("/categories", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string,unknown>)
    },
  });
  return res.data;
};

export const getCategory = async (
  id: number
): Promise<CategoryResponse> => {
  const res = await apiClient.get<CategoryResponse>(`/categories/${id}`);
  return res.data;
}

export const createCategory = async (
  categoryRequest: CategoryRequest
): Promise<CategorySuccessResponse> => {
  const res = await apiClient.post<CategorySuccessResponse>("/categories", categoryRequest);
  return res.data;
}

export const updateCategory = async (
  id: number,
  categoryRequest: CategoryRequest
): Promise<CategorySuccessResponse> => {
  const res = await apiClient.patch<CategorySuccessResponse>(`/categories/${id}`, categoryRequest);
  return res.data;
}

export const deleteCategory = async (
  id: number
): Promise<CategorySuccessResponse> => {
  const res = await apiClient.delete<CategorySuccessResponse>(`/categories/${id}`);
  return res.data;
};