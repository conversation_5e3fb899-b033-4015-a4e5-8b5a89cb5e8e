"use client";

import { categoryColumns, CategoryResponse } from "./types/category.types";
import { useListCategory } from "./query/category.query";
import { CommonTable } from "../common/table/common-table";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { useDeleteCategory } from "./query/category.query";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";

function CategoryPage() {
  const router = useRouter();
  const deleteMutation = useDeleteCategory();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<CategoryResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Inventory",
      featureName: "Category",
      roleId: 0,
    });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev: RolePermissionFilters) => ({
      ...prev,
      roleId,
    }));
  }, []);

  // Open modal and set row to delete
  const handleOpenDelete = (row: CategoryResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  // Close modal and clear selected row
  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          // Optionally, refetch list or show success message
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          // Optionally show error notification
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<CategoryResponse>
        title="Category"
        columns={categoryColumns}
        useDataQuery={useListCategory}
        hiddenColumns={["organizationId", "createdAt", "updatedAt" , "createdBy" , "updatedBy" ]}
        onCreate={() => router.push("/inventory/category/create")}
        onExport={() => console.log("Export Category")}
        onEdit={(row) => router.push(`/inventory/category/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}      
        />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete category item{" "}
            <strong>{selectedRow?.name}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
      <br />
    </>
  );
}

export default CategoryPage;
