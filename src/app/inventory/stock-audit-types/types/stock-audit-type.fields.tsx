import { Input, Select } from "antd";
import { StockAuditTypeResponse } from "./stock-audit-type.types";
import { DynamicColumn } from "@/app/common/types/common.types";
import { TableCrudColumnDef } from "@/app/common/table-crud/types/table-crud.types";

export const StockAuditColumns = (
  rows: StockAuditTypeResponse[],
  setRows: React.Dispatch<React.SetStateAction<StockAuditTypeResponse[]>>,
  errors: Record<number, Partial<Record<keyof StockAuditTypeResponse, string>>>,
  filters: Partial<StockAuditTypeResponse>,
  setFilters: React.Dispatch<
    React.SetStateAction<Partial<StockAuditTypeResponse>>
  >
): DynamicColumn[] => {
  const handleChange = (
    index: number,
    field: keyof StockAuditTypeResponse,
    value: unknown
  ) => {
    setRows((prev) => {
      const updated = [...prev];
      updated[index] = { ...updated[index], [field]: value };
      return updated;
    });
  };

  const handleFilterChange = (
    field: keyof StockAuditTypeResponse,
    value: unknown
  ) => {
    setFilters((prev) => ({ ...prev, [field]: value }));
  };

  return [
    { accessorKey: "id", header: "ID" },
    {
      accessorKey: "name",
      header: "Name",
      Edit: (index) => (
        <div className="flex flex-col">
          <Input
            value={rows[index].name}
            onChange={(e) => handleChange(index, "name", e.target.value)}
            size="small"
          />
          {errors[index]?.name && (
            <span className="text-red-500 text-[10px]">
              {errors[index]?.name}
            </span>
          )}
        </div>
      ),
      Filter: () => (
        <Input
          value={filters.name || ""}
          onChange={(e) => handleFilterChange("name", e.target.value)}
          size="small"
          placeholder="Search name"
        />
      ),
    },
    {
      accessorKey: "description",
      header: "Description",
      Edit: (index) => (
        <div className="flex flex-col">
          <Input
            value={rows[index].description}
            onChange={(e) => handleChange(index, "description", e.target.value)}
            size="small"
          />
          {errors[index]?.description && (
            <span className="text-red-500 text-[10px]">
              {errors[index]?.description}
            </span>
          )}
        </div>
      ),
      Filter: () => (
        <Input
          value={filters.description || ""}
          onChange={(e) => handleFilterChange("description", e.target.value)}
          size="small"
          placeholder="Search description"
        />
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      Edit: (index) => (
        <Select
          value={rows[index].status}
          onChange={(value) => handleChange(index, "status", value)}
          size="small"
          options={[
            { label: "Active", value: "active" },
            { label: "Inactive", value: "inactive" },
          ]}
        />
      ),
      Filter: () => (
        <Select
          value={filters.status}
          onChange={(value) => handleFilterChange("status", value)}
          size="small"
          allowClear
          placeholder="Filter status"
          options={[
            { label: "Active", value: "active" },
            { label: "Inactive", value: "inactive" },
          ]}
        />
      ),
    },
    { accessorKey: "createdAt", header: "Created At" },
    { accessorKey: "updatedAt", header: "Updated At" },
    { accessorKey: "createdBy", header: "Created By" },
    { accessorKey: "updatedBy", header: "Updated By" },
  ];
};


export const stockAuditTypeColumns: TableCrudColumnDef<StockAuditTypeResponse>[] = [
  {
    accessorKey: "name",
    header: "Name",
    type: "text",
  },
  {
    accessorKey: "description",
    header: "Description",
    type: "text",
  },
  {
    accessorKey: "status",
    header: "Status",
    type: "select",
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
    ],
  },
];

