import { BaseResponse } from "@/app/common/types/common.types";

// types/stock-audit-type.zod.ts
import { z } from "zod";

export const stockAuditTypeSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  status: z.enum(["active", "inactive"], { required_error: "Status is required" }),
});

export type StockAuditTypeRequest = z.infer<typeof stockAuditTypeSchema>;

export type StockAuditTypeResponse = StockAuditTypeRequest & BaseResponse;
