"use client";

import React, { useState } from "react";
import { z } from "zod";
import CommonCrudTable from "@/app/common/table-crud/forms/table-crud.forms";
import type { TableCrudColumnDef } from "@/app/common/table-crud/types/table-crud.types";

const auditStoreSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  status: z.enum(["active", "inactive"]).optional(),
});

type AuditStoreType = z.infer<typeof auditStoreSchema> & { id?: number };

const columns: TableCrudColumnDef<AuditStoreType>[] = [
  { accessorKey: "name", header: "Name", type: "text", width: "200px" },
  {
    accessorKey: "description",
    header: "Description",
    type: "text",
    width: "300px",
  },
  {
    accessorKey: "status",
    header: "Status",
    type: "select",
    width: "150px",
    options: [
      { label: "Active", value: "active" },
      { label: "Inactive", value: "inactive" },
    ],
  },
];

export default function AuditStorePage() {
  const [rows, setRows] = useState<AuditStoreType[]>([]);

  return (
    <CommonCrudTable
      columns={columns}
      data={rows}
      setData={setRows}
      schema={auditStoreSchema}
      onCreate={(r) => setRows((prev) => [...prev, { ...r, id: Date.now() }])}
      onUpdate={(r, i) =>
        setRows((prev) => prev.map((x, idx) => (idx === i ? r : x)))
      }
      onDelete={(_, i) => setRows((prev) => prev.filter((_, idx) => idx !== i))}
      onFilter={(f) => console.log("Filters:", f)}
    />
  );
}
