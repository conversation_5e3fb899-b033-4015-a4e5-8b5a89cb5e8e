// items.hooks.ts
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createItem,
  deleteItem,
  getItem,
  listItems,
  updateItem,
} from "../api/items.api";
import { ItemFilters, ItemRequest, ItemSuccessResponse } from "../types/items.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListItems = (
  page?: number,
  limit?: number,
  filters: ItemFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery({
    queryKey: ["items", page, limit, filterKey],
    queryFn: () => listItems(page, limit, filters),
  });
};
export const useGetItem = (id: number | undefined) => {
  return useQuery({
    queryKey: ["item", id],
    queryFn: () => getItem(id!),
    enabled: !!id,
  });
};

export const useCreateItem = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ItemSuccessResponse,
    ApiErrorResponse,
    ItemRequest
  >({
    mutationFn: createItem,
    mutationKey: ["createItem"],
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["items"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Create failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useUpdateItem = () => {
  const queryClient = useQueryClient();
  return useMutation<
    ItemSuccessResponse,
    ApiErrorResponse,
    { id: number; itemRequest: ItemRequest }
  >({
    mutationKey: ["updateItem"],
    mutationFn: ({ id, itemRequest }) => updateItem(id, itemRequest),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["items"] });
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(
        `Update failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};

export const useDeleteItem = () => {
  const queryClient = useQueryClient();
  return useMutation<ItemSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteItem"],
    mutationFn: (id) => deleteItem(id),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["items"] });
      toast.success(data.message || "Item deleted successfully!");
    },
    onError: (error) => {
      toast.error(
        `Delete failed: ${error?.response?.data?.message || error.message}`
      );
    },
  });
};
