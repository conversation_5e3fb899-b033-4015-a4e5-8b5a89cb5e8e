"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  MenuItem,
  <PERSON>Field,
  <PERSON>po<PERSON>,
  Card,
  Grid
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateItem,
  useGetItem,
  useUpdateItem,
} from "../query/items.query";
import { useRef, useEffect } from "react";
import {
  itemRequestSchema,
  ItemRequest,
  ItemType
} from "@/app/inventory/items/types/items.types";
import { useRouter } from "next/navigation";
// import DosageForm from "../../dosage-form/forms/dosage-form.forms";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import {DosageFormPaginatedResponse, DosageFormResponse} from "../../dosage-form/types/dosage-form.types";
import {useListDosageForm} from "../../dosage-form/query/dosage-form.query";
import {CategoryPaginatedResponse, CategoryResponse} from "../../category/types/category.types"
import {useListCategory} from "../../category/query/category.query"
import {useListOrganization} from "@/app/organization/organization/query/organization.query"
import {OrganizationPaginatedResponse, OrganizationResponse} from "@/app/organization/organization/types/organization.types"
type Props = {
  id?: number;
};

const itemTypes: { label: string; value: ItemType }[] = [
  { label: "Service", value: "service" },
  { label: "Asset", value: "asset" },
  { label: "Medicine", value: "medicine" },
];

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function ItemForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: itemData, isLoading } = useGetItem(id);
  const createMutation = useCreateItem();
  const updateMutation = useUpdateItem();

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ItemRequest>({
    resolver: zodResolver(itemRequestSchema),
    defaultValues: {
      name: "",
      description: "",
      itemType: "medicine",
      dosageFormId: 0,
      categoryId: 0,
      unit: "",
      strength: 0,
      hsnCode: "",
      status: "active",
      organizationId: undefined,
    },
  });

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (isEditMode && itemData) {
      reset({
        name: itemData.name,
        description: itemData.description,
        itemType: itemData.itemType,
        dosageFormId: itemData.dosageFormId,
        categoryId: itemData.categoryId,
        unit: itemData.unit,
        strength: itemData.strength,
        hsnCode: itemData.hsnCode,
        status: itemData.status,
        organizationId: itemData.organizationId,
      });
    }
  }, [isEditMode, itemData, reset]);

  const onSubmit = (data: ItemRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate({ id, itemRequest: data },
        {
          onSuccess: () => {
            // After successful edit, navigate back to the list page
            router.push("/inventory/items");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          // After successful create, navigate back to the list page
          router.push("/inventory/items");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
  <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
    <Typography variant="h6" mb={3}>
      {isEditMode ? "Edit Item" : "Create Item"}
    </Typography>

    <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
      <Grid container spacing={2}>
        {/* Name */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="name"
            control={control}
            render={({ field }) => (
              <TextField
                inputRef={nameRef}
                label="Name"
                size="small"
                fullWidth
                {...field}
                error={!!errors.name}
                helperText={errors.name?.message}
                autoFocus
                required
              />
            )}
          />
        </Grid>

        {/* Item Type */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="itemType"
            control={control}
            render={({ field }) => (
              <TextField
                select
                label="Item Type"
                size="small"
                fullWidth
                {...field}
                error={!!errors.itemType}
                helperText={errors.itemType?.message}
              >
                {itemTypes.map((opt) => (
                  <MenuItem key={opt.value} value={opt.value}>
                    {opt.label}
                  </MenuItem>
                ))}
              </TextField>
            )}
          />
        </Grid>

        {/* Dosage Form */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="dosageFormId"
            control={control}
            render={({ field }) => (
              <CommonDropdown<DosageFormResponse, DosageFormPaginatedResponse, number>
                label="Dosage Form"
                value={field.value}
                onChange={field.onChange}
                useDataQuery={useListDosageForm}
                labelKey="name"
                valueKey="id"
                searchable
                searchKey="name"
                error={!!errors.dosageFormId}
                helperText={errors.dosageFormId?.message}
              />
            )}
          />
        </Grid>

        {/* Category */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="categoryId"
            control={control}
            render={({ field }) => (
              <CommonDropdown<CategoryResponse, CategoryPaginatedResponse, number>
                label="Category"
                value={field.value}
                onChange={field.onChange}
                useDataQuery={useListCategory}
                labelKey="name"
                valueKey="id"
                searchable
                searchKey="name"
                error={!!errors.categoryId}
                helperText={errors.categoryId?.message}
              />
            )}
          />
        </Grid>

        {/* Unit */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="unit"
            control={control}
            render={({ field }) => (
              <TextField
                label="Unit"
                size="small"
                fullWidth
                {...field}
                error={!!errors.unit}
                helperText={errors.unit?.message}
              />
            )}
          />
        </Grid>

        {/* Strength */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="strength"
            control={control}
            render={({ field }) => (
              <TextField
                label="Strength"
                type="number"
                size="small"
                fullWidth
                {...field}
                value={field.value ?? 0}
                onChange={(e) => field.onChange(Number(e.target.value))}
                error={!!errors.strength}
                helperText={errors.strength?.message}
              />
            )}
          />
        </Grid>

        {/* HSN Code */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="hsnCode"
            control={control}
            render={({ field }) => (
              <TextField
                label="HSN Code"
                size="small"
                fullWidth
                {...field}
                error={!!errors.hsnCode}
                helperText={errors.hsnCode?.message}
              />
            )}
          />
        </Grid>

        {/* Status (Edit Mode Only) */}
        {isEditMode && (
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <TextField
                  select
                  label="Status"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.status}
                  helperText={errors.status?.message}
                >
                  {statuses.map((opt) => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>
        )}
      
        {/* Organization */}
        {!isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="organizationId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<OrganizationResponse, OrganizationPaginatedResponse, number>
                    label="Organization"
                    value={field.value}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
                )}
              />
            </Grid>
          )}

        {/* Description (Full Width) */}
        <Grid size={{ xs: 12, md: 6, lg: 4 }}>
          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <TextField
                label="Description"
                size="small"
                multiline
                rows={4}
                fullWidth
                {...field}
                value={field.value || ""}
                error={!!errors.description}
                helperText={errors.description?.message}
              />
            )}
          />
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box
        sx={{
          display: "flex",
          gap: 2,
          justifyContent: "flex-end",
          mt: 3,
        }}
      >
        <Button
          variant="outlined"
          color="inherit"
          onClick={() => router.back()}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          variant="contained"
          disabled={createMutation.isPending || updateMutation.isPending}
        >
          {isEditMode ? "Update" : "Create"}
        </Button>
      </Box>
    </Box>
  </Card>
);
}