import { BaseResponse, PaginatedResponse, SuccessResponse } from '@/app/common/types/common.types';
import { localTime } from '@/app/common/utils/serialize.utils';
import { MRT_ColumnDef } from 'material-react-table';
import { z } from "zod";

export type ItemType = "service" | "asset" | "medicine";

export interface ItemResponse extends BaseResponse, Record<string, unknown> {
  id: number;
  name: string;
  description: string | null;
  itemType: ItemType;
  dosageFormId: number;
  categoryId: number;
  unit: string;
  strength: number;
  hsnCode: string;
  status: string;
  organizationId: number;
}
export const itemTypeOptions = [
  { value: 'service', label: 'Service' },
  { value: 'asset', label: 'Asset' },
  { value: 'medicine', label: 'Medicine' }
];

export const statusOptions = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' }
];

export const getItemTypeLabel = (type: string): string =>
  itemTypeOptions.find(option => option.value === type)?.label || 'Unknown';

export const getStatusLabel = (status: string): string =>
  statusOptions.find(option => option.value === status)?.label || 'Unknown';
export const unitOptions = [
  'ml', 'l', 'g', 'mg', 'µg', 'lbs', 'cc', 'gtt', '%', 'mg/mL', 'g/mL', 'mol/L', 'mEq/L', 'mmHg'
];

export const unitSelectOptions = unitOptions.map(unit => ({
  value: unit,
  label: unit
}));

export const itemColumns: MRT_ColumnDef<ItemResponse>[] = [
  { accessorKey: 'id', header: 'ID' },
  { accessorKey: 'name', header: 'Name' },
  { accessorKey: 'description', header: 'Description' },
  {
    accessorKey: 'itemType',
    header: 'Type',
    filterVariant: 'select',
    filterSelectOptions: itemTypeOptions,
    Cell: ({ cell }) => getItemTypeLabel(cell.getValue<string>())
  },
  { accessorKey: 'dosageForm.name', header: 'Dosage Form' },
  { accessorKey: 'category.name', header: 'Category' },
  {
    accessorKey: 'unit',
    header: 'Unit',
    filterVariant: 'select',
    filterSelectOptions: unitSelectOptions,
    Cell: ({ cell }) => cell.getValue<string>() || '-'
  },

  { accessorKey: 'strength', header: 'Strength' },
  { accessorKey: 'hsnCode', header: 'HSN Code' },
  {
    accessorKey: 'status',
    header: 'Status',
    filterVariant: 'select',
    filterSelectOptions: statusOptions,
    Cell: ({ cell }) => getStatusLabel(cell.getValue<string>())
  },
  { accessorKey: 'organization.name', header: 'Organization' },
  {
      accessorKey: "createdAt",
      header: "Created At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
    },
    {
      accessorKey: "updatedAt",
      header: "Updated At",
      filterVariant: "date-range",
      Cell: ({ cell }) => {
        const value = cell.getValue<string>();
        return localTime(value);
      },
  },
  { accessorKey: 'createdBy', header: 'Created By' },
  { accessorKey: 'updatedBy', header: 'Updated By' },
];

export type ItemPaginatedResponse = PaginatedResponse<ItemResponse>
export type ItemSuccessResponse = SuccessResponse<ItemResponse>

export const itemRequestSchema = z.object({
  name: z.string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name cannot exceed 100 characters")
    .transform(val => val.trim()),
  description: z.string()
    .max(255, "Description cannot exceed 255 characters")
    .nullable()
    .optional()
    .transform(val => val?.trim() ?? val),
  itemType: z.enum(["service", "asset", "medicine"]),
  dosageFormId: z.number()
    .int()
    .positive("Dosage Form ID must be a positive number"),
  categoryId: z.number()
    .int()
    .positive("Category ID must be a positive number"),
  unit: z.string()
    .max(10, "Unit cannot exceed 10 characters")
    .transform(val => val.trim()),
  strength: z.number()
    .int()
    .positive("Strength must be a positive number"),
  hsnCode: z.string()
    .min(1, "HSN Code is required")
    .transform(val => val.trim()),
  status: z.string()
    .min(1, "Status is required")
    .transform(val => val.trim()),
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number"),
});

export type ItemRequest = z.infer<typeof itemRequestSchema>;

export interface ItemFilters {
  name?: string;
  itemType?: ItemType;
  status?: string;
  unit?: string;
  categoryId?: number;
  dosageFormId?: number;
  organizationId?: number;
  createdBy?: number;
  updatedBy?: number;
  createdAt?: string;
  updatedAt?: string;
}
