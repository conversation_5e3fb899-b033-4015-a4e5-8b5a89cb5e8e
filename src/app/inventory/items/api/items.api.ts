import apiClient from "@/app/api/api";
import { 
  ItemPaginatedResponse, 
  ItemResponse, 
  ItemSuccessResponse, 
  ItemRequest, 
  ItemFilters
} from "../types/items.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listItems = async (
  page?: number,
  limit?: number,
  filters?: ItemFilters,
): Promise<ItemPaginatedResponse> => {
  const res = await apiClient.get<ItemPaginatedResponse>("/items", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string,unknown>)
    },
  });
  return res.data;
};


export const getItem = async (
  id: number
): Promise<ItemResponse> => {
  const res = await apiClient.get<ItemResponse>(`/items/${id}`);
  return res.data;
};

export const createItem = async (
  itemRequest: ItemRequest
): Promise<ItemSuccessResponse> => {
  const res = await apiClient.post<ItemSuccessResponse>("/items", itemRequest);
  return res.data;
};

export const updateItem = async (
  id: number,
  itemRequest: ItemRequest
): Promise<ItemSuccessResponse> => {
  const res = await apiClient.patch<ItemSuccessResponse>(`/items/${id}`, itemRequest);
  return res.data;
};

export const deleteItem = async (
  id: number
): Promise<ItemSuccessResponse> => {
  const res = await apiClient.delete<ItemSuccessResponse>(`/items/${id}`);
  return res.data;
};