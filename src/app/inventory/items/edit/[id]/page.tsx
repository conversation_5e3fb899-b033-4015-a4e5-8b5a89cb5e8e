"use client";

import { useParams } from "next/navigation";
import { useState } from "react";
import {
  Box,
  Tabs,
  Tab,
  Card,
  CardContent,
  Divider,
} from "@mui/material";

import ItemForm from "@/app/inventory/items/forms/items.forms";
import ItemUOMForm from "@/app/inventory/item-uom/forms/item-uom.forms";
import ItemBatchForm from "@/app/inventory/item-batch/forms/item-batch.forms";
import VendorQuotationForm from "@/app/inventory/vendor-quotations/forms/vendor-quotations.forms";

export default function EditItemPage() {
  const params = useParams();
  const id = Number(params?.id);

  const [tabIndex, setTabIndex] = useState(0);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabIndex(newValue);
  };

  return (
    <Box sx={{ mx: 2, my: 3 }}>
      {/* Always visible ItemForm */}
      <ItemForm id={id} />

      <Divider sx={{ my: 3 }} />

      {/* Tabs for UOM and Batch */}
      <Card>
        <Tabs
          value={tabIndex}
          onChange={handleTabChange}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Item UOM" />
          <Tab label="Item Batch" />
          <Tab label="Vendor Quotations" />
        </Tabs>

        <CardContent>
          {tabIndex === 0 && <ItemUOMForm itemId={id} />}
          {tabIndex === 1 && <ItemBatchForm itemId={id} />}
          {tabIndex === 2 && <VendorQuotationForm itemId={id}/>}
        </CardContent>
      </Card>
    </Box>
  );
}
