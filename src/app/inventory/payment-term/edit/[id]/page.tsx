"use client";

import { useParams } from "next/navigation";
import PaymentTermForm from "@/app/inventory/payment-term/forms/payment-term.forms";
import OrganizationPage from "@/app/organization/organization/page";

export default function EditPaymentTermPage() {
  const params = useParams();
  const id = Number(params?.id);

  return (
    <>
    <PaymentTermForm id={id} />
    <OrganizationPage />
    </>
  )
}
