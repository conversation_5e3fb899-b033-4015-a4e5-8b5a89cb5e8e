import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createPaymentTerm,
  deletePaymentTerm,
  getPaymentTerm,
  listPaymentTerm,
  updatePaymentTerm,
} from "../api/payment-term.api";
import { toast } from "react-hot-toast";
import {
  SuccessResponse,
} from "@/app/common/types/common.types";
import {
  PaymentTermPaginatedResponse,
  PaymentTermRequest,
  PaymentTermResponse,
  PaymentTermFilters,
} from "@/app/inventory/payment-term/types/payment-term.types";
import { AxiosError } from "axios";
import { ApiError } from "next/dist/server/api-utils";

export const useListPaymentTerm = (
  page?: number,
  limit?: number,
  filters: PaymentTermFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  const filterKey = JSON.stringify(normalizedFilters);

  return useQuery<PaymentTermPaginatedResponse, AxiosError<ApiError>>({
    queryKey: ["payment-terms", page, limit, filterKey],
    queryFn: () => listPaymentTerm(page, limit, filters),
  });
};

export const useGetPaymentTerm = (id: number | undefined) => {
  return useQuery<PaymentTermResponse, AxiosError<ApiError>>({
    queryKey: ["payment-term", id],
    queryFn: () => getPaymentTerm(id!),
    enabled: !!id,
  });
};

export const useCreatePaymentTerm = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<PaymentTermResponse>,
    AxiosError<ApiError>,
    PaymentTermRequest
  >({
    mutationKey: ["createPaymentTerm"],
    mutationFn: (paymentTermRequest) => createPaymentTerm(paymentTermRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["payment-terms"] });
      toast.success(response.message || "Payment term created successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create payment term");
    },
  });
};

export const useUpdatePaymentTerm = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<PaymentTermResponse>,
    AxiosError<ApiError>,
    { id: number; paymentTermRequest: PaymentTermRequest }
  >({
    mutationKey: ["updatePaymentTerm"],
    mutationFn: ({ id, paymentTermRequest }) =>
      updatePaymentTerm(id, paymentTermRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["payment-terms"] });
      toast.success(response.message || "Payment term updated successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update payment term");
    },
  });
};

export const useDeletePaymentTerm = () => {
  const queryClient = useQueryClient();

  return useMutation<
    SuccessResponse<PaymentTermResponse>,
    AxiosError<ApiError>,
    number
  >({
    mutationKey: ["deletePaymentTerm"],
    mutationFn: (id) => deletePaymentTerm(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["payment-terms"] });
      toast.success(response.message || "Payment term deleted successfully!");
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete payment term");
    },
  });
};