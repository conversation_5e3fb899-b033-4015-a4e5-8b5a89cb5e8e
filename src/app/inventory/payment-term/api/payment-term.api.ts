import apiClient from "@/app/api/api";
import { 
  PaymentTermPaginatedResponse, 
  PaymentTermResponse, 
  PaymentTermSuccessResponse, 
  PaymentTermRequest,
  PaymentTermFilters 
} from "../types/payment-term.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

export const listPaymentTerm = async (
  page?: number,
  limit?: number,
  filters?: PaymentTermFilters
): Promise<PaymentTermPaginatedResponse> => {
  const res = await apiClient.get<PaymentTermPaginatedResponse>("/payment-terms", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string,unknown>)
    },
  });
  return res.data;
};

export const getPaymentTerm = async (
  id: number
): Promise<PaymentTermResponse> => {
  const res = await apiClient.get<PaymentTermResponse>(`/payment-terms/${id}`);
  return res.data;
}

export const createPaymentTerm = async (
  paymentTermRequest: PaymentTermRequest
): Promise<PaymentTermSuccessResponse> => {
  const res = await apiClient.post<PaymentTermSuccessResponse>("/payment-terms", paymentTermRequest);
  return res.data;
}

export const updatePaymentTerm = async (
  id: number,
  paymentTermRequest: PaymentTermRequest
): Promise<PaymentTermSuccessResponse> => {
  const res = await apiClient.patch<PaymentTermSuccessResponse>(`/payment-terms/${id}`, paymentTermRequest);
  return res.data;
}

export const deletePaymentTerm = async (
  id: number
): Promise<PaymentTermSuccessResponse> => {
  const res = await apiClient.delete<PaymentTermSuccessResponse>(`/payment-terms/${id}`);
  return res.data;
};