"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>uItem,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreatePaymentTerm,
  useGetPaymentTerm,
  useUpdatePaymentTerm,
} from "../query/payment-term.query";
import { useEffect } from "react";
import {
  PaymentTermRequest,
  paymentTermRequestSchema,
} from "@/app/inventory/payment-term/types/payment-term.types";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import {
  OrganizationPaginatedResponse,
  OrganizationResponse,
} from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";

type Props = {
  id?: number;
};

const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function PaymentTermForm({ id }: Props) {
  const isEditMode = !!id;
  const router = useRouter();
  const { data: paymentTermData, isLoading } = useGetPaymentTerm(id);
  const createMutation = useCreatePaymentTerm();
  const updateMutation = useUpdatePaymentTerm();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<PaymentTermRequest>({
    resolver: zodResolver(paymentTermRequestSchema),
    defaultValues: {
      name: "",
      description: "",
      status: "active",
      organizationId: undefined,
    },
  });

  useEffect(() => {
    if (isEditMode && paymentTermData) {
      const formattedData = {
        ...paymentTermData,
        organizationId:
          paymentTermData.organizationId === null
            ? undefined
            : paymentTermData.organizationId,
      };
      reset(formattedData);
    }
  }, [isEditMode, paymentTermData, reset]);

  const onSubmit = (formData: PaymentTermRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, paymentTermRequest: formData },
        {
          onSuccess: () => {
            router.push("/inventory/payment-term");
          },
        }
      );
    } else {
      createMutation.mutate(formData, {
        onSuccess: () => {
          reset();
          router.push("/inventory/payment-term");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", my: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Payment Term" : "Create Payment Term"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Name */}
          <Grid  size={{md: 6, xs: 12}}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Name"
                  size="small"
                  fullWidth
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  required
                  autoFocus
                />
              )}
            />
          </Grid>

          {/* Status (only in edit mode) */}
          {isEditMode && (
            <Grid  size={{md: 6, xs: 12}}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    {statuses.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
          )}

          {/* Organization (only in create mode) */}
          {!isEditMode && (
            <Grid  size={{md: 6, xs: 12}}>
              <Controller
                name="organizationId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<
                    OrganizationResponse,
                    OrganizationPaginatedResponse,
                    number
                  >
                    label="Organization"
                    value={field.value === null ? undefined : field.value}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
                )}
              />
            </Grid>
          )}

          {/* Description */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Description"
                  size="small"
                  multiline
                  rows={4}
                  fullWidth
                  error={!!errors.description}
                  helperText={errors.description?.message}
                />
              )}
            />
          </Grid>
        </Grid>

        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}
