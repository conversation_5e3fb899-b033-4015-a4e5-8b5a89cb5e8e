'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Typography, InputAdornment, IconButton, Card } from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { loginSchema, LoginRequest } from "@/app/auth/types/login.fields";
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from "next/navigation";
import { useLogin } from "@/app/auth/query/login.query";
import { useGetCurrentUser } from "@/app/organization/users/query/user.query";
import { useListOrganizationUsers } from "@/app/organization/organization-users/query/organizationUser.query";
import toast from "react-hot-toast";
import { OrganizationUserFilters } from '@/app/organization/organization-users/types/organizationUser.types';

export default function LoginForm() {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<LoginRequest>({
    resolver: zod<PERSON><PERSON>olver(loginSchema),
    mode: 'onBlur'
  });
  const [showPassword, setShowPassword] = useState(false);
  const { mutate, isPending: isLoginPending } = useLogin();
  const [organizationUserFilters, setOrganizationUserFilters] = useState<OrganizationUserFilters | undefined>({});
  const [shouldFetchOrganizationUser, setShouldFetchOrganizationUser] = useState(false);
  const { refetch: refetchCurrentUser, isFetching: isCurrentUserFetching } = useGetCurrentUser();
  const { refetch: refetchCurrentOrganizationUser, isFetching: isCurrentOrganizationUserFetching } = useListOrganizationUsers(1, 10, organizationUserFilters, shouldFetchOrganizationUser);
  const router = useRouter();

  const getCurrentOrganizationUser = useCallback(async () => {
    const organizationUser = await refetchCurrentOrganizationUser();
    console.log(organizationUser.isSuccess);
    console.log(organizationUser.data);
    if (organizationUser.isSuccess && organizationUser.data) {
      localStorage.setItem(
        "default_role_id",
        organizationUser.data.data[0].roleId.toString()
      );
      reset();
      router.push("/organization/organization");
    } else {
      toast.error(
        (organizationUser.error?.response?.data.message ??
          organizationUser.error?.message) ?? "Organization user missing"
      );
    }
  }, [refetchCurrentOrganizationUser, reset, router]);
  

  useEffect(() => {
    if (
      shouldFetchOrganizationUser &&
      organizationUserFilters?.userId &&
      organizationUserFilters?.organizationId
    ) {
      getCurrentOrganizationUser();
      setShouldFetchOrganizationUser(false); // reset to avoid repeated calls
    }
  }, [organizationUserFilters, shouldFetchOrganizationUser, getCurrentOrganizationUser]);

  const onSubmit = (data: LoginRequest) => {
    mutate(data, {
      onSuccess: async () => {
        const result = await refetchCurrentUser();

        if (result.isSuccess && result.data) {
          localStorage.setItem("default_organization_id", result.data.data.defaultOrganizationId.toString());

          const filters: OrganizationUserFilters = {
            userId: result.data.data.id,
            organizationId: result.data.data.defaultOrganizationId,
          };

          setOrganizationUserFilters(filters)
          setShouldFetchOrganizationUser(true);

        } else {
          toast.error((result.error?.response?.data.message ?? result.error?.message) ?? "Current User Data is Missing");
        }
      }
    });
  };


  const togglePasswordVisibility = () => {
    setShowPassword((show) => !show);
  };

  return (
    <Box
      sx={{
        display: 'flex',
        height: '100vh',
        flexDirection: { xs: 'column', md: 'row' },
      }}
    >
      {/* Left Side */}
      <Box
        sx={{
          width: { xs: 0, md: '50%' },
          bgcolor: 'primary.light',
          display: { xs: 'none', md: 'flex' },
          justifyContent: 'center',
          alignItems: 'center',
          color: 'white',
          fontSize: 24,
          fontWeight: 'bold',
        }}
      >
        Welcome to HiFi-Medic
      </Box>

      {/* Right Side */}
      <Box
        sx={{
          width: { xs: '100%', md: '50%' },
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          px: { xs: 2, md: 4 },
          py: { xs: 4, md: 0 },
        }}
      >
        <Card sx={{ width: { xs: '100%', sm: '80%' }, p: 4 }}>
          <Box
            component="form"
            onSubmit={handleSubmit(onSubmit)}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
            }}
          >
            <Typography variant="h5" mb={2} align="left">
              Login
            </Typography>
            <Typography mb={2} align="left">
              Please provide your credentials to login.
            </Typography>

            <TextField
              label="Email"
              type="email"
              {...register('email')}
              error={!!errors.email}
              helperText={errors.email?.message}
              fullWidth
            />

            <TextField
              label="Password"
              type={showPassword ? 'text' : 'password'}
              {...register('password')}
              error={!!errors.password}
              helperText={errors.password?.message}
              fullWidth
              defaultValue=""
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label={showPassword ? 'Hide password' : 'Show password'}
                        onClick={togglePasswordVisibility}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }
              }}
            />

            <Button type="submit" variant="contained" size="large" disabled={isLoginPending || isCurrentUserFetching || isCurrentOrganizationUserFetching}>
              Login
            </Button>
          </Box>
        </Card>
      </Box>
    </Box>
  );
}