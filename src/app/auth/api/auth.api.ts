import apiClient from "@/app/api/api";
import { LoginResponse, LogoutResponse } from "@/app/auth/types/login.types";
import { LoginRequest } from "@/app/auth/types/login.fields";

export const login = async (loginRequest: LoginRequest): Promise<LoginResponse> => {
  const res = await apiClient.post<LoginResponse>("/signin", loginRequest);
  return res.data;
};

export const logout = async (): Promise<LogoutResponse> => {
  const res = await apiClient.post<LogoutResponse>("/signout");
  return res.data;
}
