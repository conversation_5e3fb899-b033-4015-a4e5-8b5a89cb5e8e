"use client";

import { useMutation } from "@tanstack/react-query";
import { LoginResponse, LogoutResponse } from "@/app/auth/types/login.types";
import { login, logout } from "@/app/auth/api/auth.api";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";
import { LoginRequest } from "@/app/auth/types/login.fields";

export const useLogin = () => {
  return useMutation<LoginResponse, ApiErrorResponse, LoginRequest>({
    mutationFn: login,
    mutationKey: ["login"],
    onSuccess: (data) => {
      localStorage.setItem("access_token", data.data.access_token);
      localStorage.setItem("refresh_token", data.data.refresh_token);
      toast.success(data.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message ?? error.message);
    },
  });
};


export const useLogout = () => {
  return useMutation<LogoutResponse, ApiErrorResponse, null>({
    mutationKey: ["logout"],
    mutationFn: logout,
    onSuccess: (response) => {
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message ?? error.message);
    }
  })
}