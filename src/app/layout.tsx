import "swiper/css";
import "swiper/css/bundle";
import "remixicon/fonts/remixicon.css";
import "react-datetime-picker/dist/DateTimePicker.css";
import "react-calendar/dist/Calendar.css";
import "react-clock/dist/Clock.css";

import "@/styles/front-pages.css";
import "@/styles/control-panel.css";
import "@/styles/left-sidebar-menu.css";
import "@/styles/top-navbar.css";
import "@/styles/crypto-dashboard.css";
import "@/styles/chat.css";
import "@/styles/globals.css";
import "@/styles/dark.css"; // Dark Mode CSS
import "@/styles/rtl.css"; // RTL Mode CSS


import { PrimeReactProvider } from "primereact/api";
import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON>ei<PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import DateProvider from "@/app/providers/DateProvider";
import { AppRouterCacheProvider } from "@mui/material-nextjs/v14-appRouter";
import ThemeRegistry from "@/app/providers/ThemeRegistry";
// import ClientProviders from "@/app/providers/ClientProviders";
import { Toaster } from "react-hot-toast";
import AppQueryProvider from "@/app/providers/QueryClientProvider";
import SidebarLayout from "./sidebar/sidebar-layout";
// import roa from "@/app/inventory/roa";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "optional",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "optional",
});


export const metadata: Metadata = {
  title: "HiFi-Medic",
  description: "Generated by Sanrado Tech LLP",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link
          rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0&display=optional"
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AppRouterCacheProvider options={{ enableCssLayer: true }}>
          <ThemeRegistry>
            {/* <ClientProviders> */}
            <PrimeReactProvider>
              <AppQueryProvider>
                <SidebarLayout>
                  <DateProvider>
                    {children}
                    <Toaster position="top-right" />
                  </DateProvider>
                </SidebarLayout>
              </AppQueryProvider>
            </PrimeReactProvider>
            {/* </ClientProviders> */}
            {/* <roa/> */}
          </ThemeRegistry>
        </AppRouterCacheProvider>
      </body>
    </html>
  );
}
