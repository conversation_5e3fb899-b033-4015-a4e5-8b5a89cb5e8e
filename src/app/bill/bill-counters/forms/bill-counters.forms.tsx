"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  MenuItem,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
  Card,
  Grid,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCreateBillCounter,
  useGetBillCounter,
  useUpdateBillCounter,
} from "../query/bill-counters.query";
import { useRef, useEffect, useState } from "react";
import {
  billCounterRequestSchema,
  BillCounterRequest,
} from "@/app/bill/bill-counters/types/bill-counters.types";
import { useRouter } from "next/navigation";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";
import {
  OrganizationPaginatedResponse,
  OrganizationResponse,
} from "@/app/organization/organization/types/organization.types";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";

type Props = {
id?:number;

};



const statuses = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export default function BillCounterForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: billCounterData, isLoading } = useGetBillCounter(id);
  const createMutation = useCreateBillCounter();
  const updateMutation = useUpdateBillCounter();
  const [defaultOrganizationId, setDefaultOrganizationId] = useState<number>(0);

  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
    const organizationId =
      Number(localStorage.getItem("default_organization_id")) || 0;
    setDefaultOrganizationId(organizationId);
  }, []);

  const {
    control,
    handleSubmit,
    reset,
    // watch,
    formState: { errors },
  } = useForm<BillCounterRequest>({
    resolver: zodResolver(billCounterRequestSchema),
    defaultValues: {
      name: "",
      location:"",
      status: "active",
      organizationId: defaultOrganizationId ?? 0,
    },
  });

  useEffect(() => {
    reset({
      name: "",
      location: "", 
      status: "active",
      organizationId: defaultOrganizationId ?? 0,
    });
  }, [defaultOrganizationId, reset]);

  useEffect(() => {
    if (isEditMode && billCounterData) {
      reset(billCounterData);
    }
  }, [isEditMode, billCounterData, reset]);

  const onSubmit = (data: BillCounterRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, BillCounterRequest: data },
        {
          onSuccess: () => {
            // After successful edit, navigate back to the list page
            router.push("/bill/bill-counters");
          },
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          // After successful create, navigate back to the list page
          reset();
          router.push("/bill/bill-counters");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", mb: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit BillCounter" : "Create BillCounter"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          {/* Name */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  inputRef={nameRef}
                  label="Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  autoFocus
                  required
                />
              )}
            />
          </Grid>

          {/* Pregnancy Category */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="location"
              control={control}
              render={({ field }) => (
                <TextField
                  
                  label="location"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.location}
                  helperText={errors.location?.message}
                > 
                </TextField>
              )}
            />
          </Grid>

          {/* Status (Edit Mode Only) */}
          {isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <TextField
                    select
                    label="Status"
                    size="small"
                    fullWidth
                    {...field}
                    error={!!errors.status}
                    helperText={errors.status?.message}
                  >
                    {statuses.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </TextField>
                )}
              />
            </Grid>
          )}

          {/* Organization (Create Mode Only) */}
          {!isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="organizationId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<
                    OrganizationResponse,
                    OrganizationPaginatedResponse,
                    number
                  >
                    label="Organization"
                    value={field.value}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
                )}
              />
            </Grid>
          )}

         </Grid>

        {/* Action Buttons */}
        <Box
          sx={{
            display: "flex",
            gap: 2,
            justifyContent: "flex-end",
            mt: 3,
          }}
        >
          <Button
            variant="outlined"
            color="inherit"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}
