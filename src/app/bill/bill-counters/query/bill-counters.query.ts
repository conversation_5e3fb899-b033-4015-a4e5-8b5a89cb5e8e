import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createBillCounter,
  deleteBillCounter,
  getBillCounter,
  listBillCounter,
  updateBillCounter,
} from "../api/bill-counters.api";
import {
  BillCounterFilters,
  BillCounterPaginatedResponse,
  BillCounterRequest,
  BillCounterResponse,
  BillCounterSuccessResponse,
} from "../types/bill-counters.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListBillCounter = (
  page?: number,
  limit?: number,
  filters: BillCounterFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<BillCounterPaginatedResponse, ApiErrorResponse>({
    queryKey: ["BillCounters", page, limit, filterKey],
    queryFn: () => listBillCounter(page, limit, filters),
  });
};

export const useGetBillCounter = (id: number | undefined) => {
  return useQuery<BillCounterResponse, ApiErrorResponse>({
    queryKey: ["BillCounter", id],
    queryFn: () => getBillCounter(id!),
    enabled: !!id,
  });
};

export const useCreateBillCounter = () => {
  const queryClient = useQueryClient();
  return useMutation<
    BillCounterSuccessResponse,
    ApiErrorResponse,
    BillCounterRequest
  >({
    mutationKey: ["createBillCounter"],
    mutationFn: (BillCounterRequest: BillCounterRequest) =>
      createBillCounter(BillCounterRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["BillCounters"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create BillCounter");
    },
  });
};

export const useUpdateBillCounter = () => {
  const queryClient = useQueryClient();
  return useMutation<
    BillCounterSuccessResponse,
    ApiErrorResponse,
    { id: number; BillCounterRequest: BillCounterRequest }
  >({
    mutationKey: ["updateBillCounter"],
    mutationFn: ({
      id,
      BillCounterRequest,
    }: {
      id: number;
      BillCounterRequest: BillCounterRequest;
    }) => updateBillCounter(id, BillCounterRequest), // Assuming update uses the same API as create
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["BillCounters"] }); // Invalidate the list query to refresh data
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update BillCounter");
    },
  });
};

export const useDeleteBillCounter = () => {
  const queryClient = useQueryClient();
  return useMutation<BillCounterSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteBillCounter"],
    mutationFn: (id: number) => deleteBillCounter(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["BillCounters"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete BillCounter");
    },
  });
};
