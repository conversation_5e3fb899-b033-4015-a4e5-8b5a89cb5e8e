import { MRT_ColumnDef } from "material-react-table";
import { z } from "zod";
import {
  statusOptions,
  statusMap,
  getStatusLabel,
} from "@/app/common/types/status.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { BaseResponse, PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";

export const billCounterRequestSchema = z.object({
  name: z.string()
    .min(1, "Name is required")
    .transform((val) => val.trim()),
  location: z.string()
    .optional()
    .transform((val) => (val != null ? val.trim() : val)),
  status: z.enum(["active", "inactive"]),
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number")
    .optional(),
});

export type BillCounterRequest = z.infer<typeof billCounterRequestSchema>;

export type BillCounterResponse = BillCounterRequest & BaseResponse & Record<string, unknown>;

export const billCounterColumns: MRT_ColumnDef<BillCounterResponse>[] = [
  { accessorKey: "id", header: "ID", grow: false, size: 50 },
//   { accessorKey: "code", header: "Code" },
  { accessorKey: "name", header: "Name" },
  { accessorKey: "location", header: "Location" },
//   { accessorKey: "description", header: "Description" },
  {
    accessorKey: "status",
    header: "Status",
    filterVariant: "select",
    filterSelectOptions: statusOptions.map((value) => ({
      value,
      label: statusMap[value], // Capitalize first letter
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: "organization.name", header: "Organization" },
];

export type BillCounterPaginatedResponse = PaginatedResponse<BillCounterResponse>;
export type BillCounterSuccessResponse = SuccessResponse<BillCounterResponse>;

export interface BillCounterFilters {
  name?: string;
  location?: string;
  status?: "active" | "inactive";
  organizationId?: number;
  createdAt?: string; // ISO date string
  updatedAt?: string; // ISO date string
  createdBy?: number;
  updatedBy?: number;
}
