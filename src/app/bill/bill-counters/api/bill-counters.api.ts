import apiClient from "@/app/api/api";
import { serializeFilters } from "@/app/common/utils/serialize.utils";
import { BillCounterFilters, BillCounterPaginatedResponse, BillCounterRequest, BillCounterResponse, BillCounterSuccessResponse } from "../types/bill-counters.types";

export const listBillCounter = async (
  page?: number,
  limit?: number,
  filters?: BillCounterFilters
): Promise<BillCounterPaginatedResponse> => {
  const res = await apiClient.get<BillCounterPaginatedResponse>("/bill-counters", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};

export const getBillCounter = async (
  id: number
): Promise<BillCounterResponse> => {
  const res = await apiClient.get<BillCounterResponse>(`/bill-counters/${id}`);
  return res.data;
}

export const createBillCounter = async (
  BillCounterRequest: BillCounterRequest
): Promise<BillCounterSuccessResponse> => {
  const res = await apiClient.post<BillCounterSuccessResponse>("/bill-counters", BillCounterRequest);
  return res.data;
}

export const updateBillCounter = async (
  id: number,
  BillCounterRequest: BillCounterRequest
): Promise<BillCounterSuccessResponse> => {
  const res = await apiClient.patch<BillCounterSuccessResponse>(`/bill-counters/${id}`, BillCounterRequest);
  return res.data;
}

export const deleteBillCounter = async (
  id: number
): Promise<BillCounterSuccessResponse> => {
  const res = await apiClient.delete<BillCounterSuccessResponse>(`/bill-counters/${id}`);
  return res.data;
};