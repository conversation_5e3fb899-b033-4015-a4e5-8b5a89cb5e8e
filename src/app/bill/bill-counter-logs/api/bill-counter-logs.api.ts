import apiClient from "@/app/api/api";
import { serializeFilters } from "@/app/common/utils/serialize.utils";
import { BillCounterLogsFilters, typeBillCounterLogsPaginatedResponse, BillCounterLogsRequest, BillCounterLogsResponse, typeBillCounterLogsSuccessResponse } from "../types/bill-counter-logs.types";

export const listBillCounterLogs = async (
  page?: number,
  limit?: number,
  filters?: BillCounterLogsFilters
): Promise<typeBillCounterLogsPaginatedResponse> => {
  const res = await apiClient.get<typeBillCounterLogsPaginatedResponse>("/bill-counter-logs", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });

  return res.data;
};

export const getBillCounterLogs = async (id: number): Promise<BillCounterLogsResponse> => {
  const res = await apiClient.get<BillCounterLogsResponse>(`/bill-counter-logs/${id}`);
  return res.data;
}

export const createBillCounterLogs = async (
  BillCounterLogsRequest: BillCounterLogsRequest
): Promise<typeBillCounterLogsSuccessResponse> => {
  const res = await apiClient.post<typeBillCounterLogsSuccessResponse>("/bill-counter-logs", BillCounterLogsRequest);
  return res.data;
}

export const updateBillCounterLogs = async (
  id: number,
  BillCounterLogsRequest: BillCounterLogsRequest
): Promise<typeBillCounterLogsSuccessResponse> => {
  const res = await apiClient.patch<typeBillCounterLogsSuccessResponse>(`/bill-counter-logs/${id}`, BillCounterLogsRequest);
  return res.data;
}

export const deleteBillCounterLogs = async (id: number): Promise<typeBillCounterLogsSuccessResponse> => {
  const res = await apiClient.delete<typeBillCounterLogsSuccessResponse>(`/bill-counter-logs/${id}`);
  return res.data;
};