"use client";

// Make sure the file exists at this path, or update the path if needed

import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
} from "@mui/material";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import { CommonTable } from "@/app/inventory/common/table/common-table";
import { useDeleteBillCounterLogs, useListBillCounterLogs } from "./query/bill-counter-logs.query";
import { BillCounterLogsResponse } from "./types/bill-counter-logs.types";

// Define columns for the CommonTable
const BillCounterLogsColumns = [
  { header: "ID", accessorKey: "id" },
  { header: "Name", accessorKey: "name" },
  {header: "BillCounterId", accessorkey: "billCounterId"},
  { header: "status", accessorKey: "status" },
  {header: "Start Time", accessoryKey: "startTime"},
  {header: "End Time", accessoryKey: "endTime"}    
  // Add more columns as needed based on BillCounterLogsResponse structure
];

function BillCounterLogsPage() {
  const router = useRouter();
  const deleteMutation = useDeleteBillCounterLogs();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<BillCounterLogsResponse | null>(
    null
  );
  const [rolePermissionFilters, setRolePermissionFilters] =
    useState<RolePermissionFilters>({
      moduleName: "Bill",
      featureName: "BillCounterLogs",
      roleId: 0,
      });

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);

  // Open modal and set row to delete
  const handleOpenDelete = (row: BillCounterLogsResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  // Close modal and clear selected row
  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          // Optionally, refetch list or show success message
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          // Optionally show error notification
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
          <CommonTable<BillCounterLogsResponse>
            title="BillCounterLogss"
            columns={BillCounterLogsColumns}
            useDataQuery={useListBillCounterLogs}
            hiddenColumns={[
              "organization.name",
              "createdAt",
              "updatedAt",
              "createdBy",
              "updatedBy",
            ]}
            onCreate={() => router.push("/bill/bill-counter-logs/create")}
            onExport={() => console.log("Export Category")}
            onEdit={(row: { id:number }) => router.push(`/bill/bill-counter-logs/edit/${row.id}`)}
            onDelete={(row) => handleOpenDelete(row)}
          
            isCreate={rolePermissionFilters?.create ?? false}
            isEdit={rolePermissionFilters?.edit ?? false} 
          />

          <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
            <DialogTitle>Confirm Delete</DialogTitle>
            <DialogContent>
              <DialogContentText>
                Are you sure you want to delete bill-counter-logs item{" "}
                <strong>{String(selectedRow?.name ?? "")}</strong>? This action cannot be
                undone.
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDelete} variant="outlined">
                Cancel
              </Button>
              <Button
                onClick={handleConfirmDelete}
                variant="contained"
                color="error"
                disabled={deleteMutation.isPending}
              >
                Confirm
              </Button>
            </DialogActions>
          </Dialog>
      
    </>
  );
}

export default BillCounterLogsPage;
