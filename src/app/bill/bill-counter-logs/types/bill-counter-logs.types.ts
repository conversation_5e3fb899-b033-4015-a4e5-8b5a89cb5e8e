import { MRT_ColumnDef } from "material-react-table";
import { z } from "zod";
import {
  statusOptions,
  statusMap,
  getStatusLabel,
} from "@/app/common/types/status.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { BaseResponse, PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";
export interface BillCounterLogsProps {
  itemId?: number;
}
// import { BillCounterLogsProps } from "./bill-counter-logs.prperties";



export type BillCounterLogsRequest = z.infer<typeof BillCounterLogsSchema>;
export type BillCounterLogsResponse = BillCounterLogsProps & BaseResponse & Record<string, unknown>;
export const BillCounterLogsSchema = z.object({


  billCounterId: z
    .number()
    .int()
    .positive("Bill Counter ID must be a positive number")
    .optional(),


  startTime: z.string().optional(),
  endTime: z.string().optional(),

  status: z.enum(["active", "inactive"]).optional(),

  orgbillCounterId: z.number().nonnegative("Bill Counter ID must be zero or positive").optional(),


  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),

  createdBy: z
    .number()
    .int()
    .positive("Created By must be a positive number")
    .optional(),

  updatedBy: z
    .number()
    .int()
    .positive("Updated By must be a positive number")
    .optional(),
});

 

export const billcounterlogsColumns: MRT_ColumnDef<BillCounterLogsResponse>[] = [
  { accessorKey: "id", header: "ID", grow: false, size: 50 },
  { accessorKey: "billCounterId", header: "BillCounterID"},
  {accessorKey: "startTime", header: "Start Time"},
  {accessorKey: "endTime", header: "End Time"},
  
  {
    accessorKey: "status",
    header: "Status",
    filterVariant: "select",
    filterSelectOptions: statusOptions.map((value) => ({
      value,
      label: statusMap[value], // Capitalize first letter
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    },
  },
  {
    accessorKey: "startTime",
    header: "StartTime",
    filterVariant: "select",
    filterSelectOptions: statusOptions.map((value) => ({
      value,
      label: statusMap[value], // Capitalize first letter
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  { accessorKey: "organization.name", header: "Organization" },
 

];


export type typeBillCounterLogsPaginatedResponse = PaginatedResponse<BillCounterLogsResponse>;
export type typeBillCounterLogsSuccessResponse = SuccessResponse<BillCounterLogsResponse>;

export interface BillCounterLogsFilters {
  name?: string;
  status?: "active" | "inactive";
  organizationId?: number;
  createdAt?: string; // ISO date string
  updatedAt?: string; // ISO date string
  createdBy?: number;
  updatedBy?: number;
}
