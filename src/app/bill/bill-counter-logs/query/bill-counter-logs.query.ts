import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  createBillCounterLogs,
  deleteBillCounterLogs,
  getBillCounterLogs,
  listBillCounterLogs,
  updateBillCounterLogs,
} from "../api/bill-counter-logs.api";
import {
  BillCounterLogsFilters,
  typeBillCounterLogsPaginatedResponse,
  BillCounterLogsRequest,
  BillCounterLogsResponse,
  typeBillCounterLogsSuccessResponse,
// Update the import path below if the file was moved or renamed
} from "../types/bill-counter-logs.types";
import toast from "react-hot-toast";
import { ApiErrorResponse } from "@/app/common/types/common.types";

export const useListBillCounterLogs = (
  page?: number,
  limit?: number,
  filters: BillCounterLogsFilters = {}
) => {
  const normalizedFilters = filters ?? {};
  // Serialize filters in stable way
  const filterKey = JSON.stringify(normalizedFilters);
  return useQuery<typeBillCounterLogsPaginatedResponse, ApiErrorResponse>({
    queryKey: ["billCounterLogs", page, limit, filterKey],
    queryFn: () => listBillCounterLogs(page, limit, filters),
  });
};

export const useGetBillCounterLogs = (id: number | undefined) => {
  return useQuery<BillCounterLogsResponse, ApiErrorResponse>({
    queryKey: ["billCounterLogs", id],
    queryFn: () => getBillCounterLogs(id!),
    enabled: !!id,
  });
};

export const useCreateBillCounterLogs = () => {
  const queryClient = useQueryClient();
  return useMutation<
    typeBillCounterLogsSuccessResponse,
    ApiErrorResponse,
    BillCounterLogsRequest
  >({
    mutationKey: ["create billCounterLogs"],
    mutationFn: (billcounterlogsRequest: BillCounterLogsRequest) =>
      createBillCounterLogs(billcounterlogsRequest),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["billCounterLogs"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to create billCounterLogs");
    },
  });
};

export const useUpdateBillCounterLogs = () => {
  const queryClient = useQueryClient();
  return useMutation<
    typeBillCounterLogsSuccessResponse,
    ApiErrorResponse,
    { id: number; billcounterlogsRequest: BillCounterLogsRequest }
  >({
    mutationKey: ["update billCounterLogs"],
    mutationFn: ({
      id,
      billcounterlogsRequest,
    }: {
      id: number;
      billcounterlogsRequest: BillCounterLogsRequest;
    }) => updateBillCounterLogs(id, billcounterlogsRequest), // Assuming update uses the same API as create
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["billCounterLogs"] }); // Invalidate the list query to refresh data
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to update billCounterLogs");
    },
  });
};

export const useDeleteBillCounterLogs = () => {
  const queryClient = useQueryClient();
  return useMutation<typeBillCounterLogsSuccessResponse, ApiErrorResponse, number>({
    mutationKey: ["deleteBillCounterLogs"],
    mutationFn: (id: number) => deleteBillCounterLogs(id),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["billcounterlogs"] });
      toast.success(response.message);
    },
    onError: (error) => {
      toast.error(error.response?.data.message || "Failed to delete billCounterLogs");
    },
  });
};
