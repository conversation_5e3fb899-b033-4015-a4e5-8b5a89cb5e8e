"use client";

import {
  <PERSON>,
  But<PERSON>,
  Card,
  Grid,
  MenuItem,
  TextField,
  Typography,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRef, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";;
import {
  useCreateBillCounterLogs,
  useGetBillCounterLogs,
  useUpdateBillCounterLogs,
} from "../query/bill-counter-logs.query";

import {
  useListOrganization,
} from "@/app/organization/organization/query/organization.query";
import {
  OrganizationResponse,
  OrganizationPaginatedResponse,
} from "@/app/organization/organization/types/organization.types";
import { useListBillCounter } from "../../bill-counters/query/bill-counters.query";
import { BillCounterPaginatedResponse, BillCounterResponse } from "../../bill-counters/types/bill-counters.types";

// ✅ Zod Schema and Types
const BillCounterLogsRequestSchema = z.object({
  name: z.string().min(1, "Name is required").transform((val) => val.trim()),
  location: z.string().min(1, "Location is required").transform((val) => val.trim()),
  billCounterId: z
    .number({ invalid_type_error: "Bill Counter ID must be a number" })
    .int("Bill Counter ID must be an integer")
    .nonnegative("Bill Counter ID must be 0 or more"),
  startTime: z.string().min(1, "Start Time is required"),
  endTime: z.string().min(1, "End Time is required"),
  status: z.enum(["active", "inactive"], {
    required_error: "Status is required",
  }),
  organizationId: z
    .number({ invalid_type_error: "Organization ID must be a number" })
    .int("Organization ID must be an integer")
    .positive("Organization ID must be a positive number"),
});

type BillCounterLogsRequest = z.infer<typeof BillCounterLogsRequestSchema>;

// ✅ Component Props
type Props = {
  id?: number;
};

const statuses = [
  { label: "Draft", value: "draft" },
  { label: "Finalized", value: "finalized" },
];

export default function BillCounterLogsForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: billCounterLogsData, isLoading } = useGetBillCounterLogs(id);
  const createMutation = useCreateBillCounterLogs();
  const updateMutation = useUpdateBillCounterLogs();
  const [defaultOrganizationId, setDefaultOrganizationId] = useState<number>(0);
  const nameRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    if (nameRef.current) {
      nameRef.current.focus();
    }
    const organizationId = Number(localStorage.getItem("default_organization_id")) || 0;
    setDefaultOrganizationId(organizationId);
  }, []);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<BillCounterLogsRequest>({
    resolver: zodResolver(BillCounterLogsRequestSchema),
    defaultValues: {
      name: "",
      location: "",
      billCounterId: 0,
      startTime: "",
      endTime: "",
      status: "active",
      organizationId: defaultOrganizationId ?? 0,
    },
  });

  useEffect(() => {
    reset({
      name: "",
      location: "",
      billCounterId: 0,
      startTime: "",
      endTime: "",
      status: "active",
      organizationId: defaultOrganizationId ?? 0,
    });
  }, [defaultOrganizationId, reset]);

  useEffect(() => {
    if (isEditMode && billCounterLogsData) {
      reset(BillCounterLogsRequestSchema.parse(billCounterLogsData));
    }
  }, [isEditMode, billCounterLogsData, reset]);

  const onSubmit = (data: BillCounterLogsRequest) => {
    // Convert to full ISO string
    const toISO = (val: string) => val ? new Date(val).toISOString() : "";

    const payload = {
      ...data,
      startTime: toISO(data.startTime),
      endTime: toISO(data.endTime),
    };

    if (isEditMode && id) {
      updateMutation.mutate(
        { id, billcounterlogsRequest: payload },
        {
          onSuccess: () => router.push("/bill/bill-counter-logs"),
        }
      );
    } else {
      createMutation.mutate(payload, {
        onSuccess: () => {
          reset();
          router.push("/bill/bill-counter-logs");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", mb: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit BillCounterLogs" : "Create BillCounterLogs"}
      </Typography>

      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  inputRef={nameRef}
                  label="Name"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  autoFocus
                  required
                />
              )}
            />
          </Grid>


          {/* Incorporation Date */}
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="startTime"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Start Time"
                  type="datetime-local"
                  size="small"
                  slotProps={{ inputLabel: { shrink: true } }}
                  fullWidth
                  error={!!errors.startTime}
                  helperText={errors.startTime?.message}
                  required
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="endTime"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="EndTime"
                  type="datetime-local"
                  size="small"
                  slotProps={{ inputLabel: { shrink: true } }}
                  fullWidth
                  error={!!errors.endTime}
                  helperText={errors.endTime?.message}
                  required
                />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="location"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Location"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.location}
                  helperText={errors.location?.message}
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="billCounterId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<
                    BillCounterResponse,
                    BillCounterPaginatedResponse,
                    number
                  >
                    label="Bill Counter"
                    value={field.value}
                    onChange={field.onChange}
                    useDataQuery={useListBillCounter}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
              )}
            />
          </Grid>

          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <TextField
                  select
                  label="Status"
                  size="small"
                  fullWidth
                  {...field}
                  error={!!errors.status}
                  helperText={errors.status?.message}
                >
                  {statuses.map((opt) => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </TextField>
              )}
            />
          </Grid>

          {!isEditMode && (
            <Grid size={{ xs: 12, md: 6, lg: 4 }}>
              <Controller
                name="organizationId"
                control={control}
                render={({ field }) => (
                  <CommonDropdown<
                    OrganizationResponse,
                    OrganizationPaginatedResponse,
                    number
                  >
                    label="Organization"
                    value={field.value}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
                )}
              />
            </Grid>
          )}
        </Grid>

        <Box sx={{
          display: "flex",
          gap: 2,
          justifyContent: "flex-end",
          mt: 3
        }}>
          <Button variant="outlined"
            color="inherit"
            onClick={() => router.back()}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={createMutation.isPending || updateMutation.isPending}
          >
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}
