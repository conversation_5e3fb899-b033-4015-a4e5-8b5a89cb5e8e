import apiClient from "@/app/api/api";
import {
  BillGatewaysPaginatedResponse,
  BillGatewaysResponse,
  BillGatewaysSuccessResponse,
  BillGatewaysRequest,
  BillGatewaysFilters,
} from "../types/bill-gateways.types";
import { serializeFilters } from "@/app/common/utils/serialize.utils";

// List paginated
export const listBillGateways = async (
  page?: number,
  limit?: number,
  filters?: BillGatewaysFilters
): Promise<BillGatewaysPaginatedResponse> => {
  const res = await apiClient.get<BillGatewaysPaginatedResponse>("/billgateways", {
    params: {
      ...(page !== undefined && { page }),
      ...(limit !== undefined && { limit }),
      ...serializeFilters(filters as Record<string, unknown>),
    },
  });
  return res.data;
};

// Get single
export const getBillGateway = async (
  id: number
): Promise<BillGatewaysResponse> => {
  const res = await apiClient.get<BillGatewaysResponse>(`/billgateways/${id}`);
  return res.data;
};

// Create
export const createBillGateway = async (
  billGatewayRequest: BillGatewaysRequest
): Promise<BillGatewaysSuccessResponse> => {
  const res = await apiClient.post<BillGatewaysSuccessResponse>("/billgateways", billGatewayRequest);
  return res.data;
};

// Update
export const updateBillGateway = async (
  id: number,
  billGatewayRequest: BillGatewaysRequest
): Promise<BillGatewaysSuccessResponse> => {
  const res = await apiClient.patch<BillGatewaysSuccessResponse>(`/billgateways/${id}`, billGatewayRequest);
  return res.data;
};

// Delete
export const deleteBillGateway = async (
  id: number
): Promise<BillGatewaysSuccessResponse> => {
  const res = await apiClient.delete<BillGatewaysSuccessResponse>(`/billgateways/${id}`);
  return res.data;
};