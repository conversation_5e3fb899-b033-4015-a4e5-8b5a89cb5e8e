import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  listBillGateways,
  getBillGateway,
  createBillGateway,
  updateBillGateway,
  deleteBillGateway,
} from "../api/bill-gateways.api";
import { BillGatewaysRequest, BillGatewaysFilters } from "../types/bill-gateways.types";

// List
export const useBillGateways = (page?: number, limit?: number, filters?: BillGatewaysFilters) =>
  useQuery({
    queryKey: ["bill-gateways", page, limit, filters],
    queryFn: () => listBillGateways(page, limit, filters),
  });

// Single
export const useBillGateway = (id: number) =>
  useQuery({
    queryKey: ["bill-gateway", id],
    queryFn: () => getBillGateway(id),
    enabled: !!id,
  });

// Create
export const useCreateBillGateway = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: BillGatewaysRequest) => createBillGateway(data),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["bill-gateways"] }),
  });
};

// Update
export const useUpdateBillGateway = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: BillGatewaysRequest }) =>
      updateBillGateway(id, data),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["bill-gateways"] }),
  });
};

// Delete
export const useDeleteBillGateway = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: number) => deleteBillGateway(id),
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ["bill-gateways"] }),
  });
};