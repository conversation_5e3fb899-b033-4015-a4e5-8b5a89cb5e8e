import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { BillGatewaysRequest, BillGatewaysRequestSchema } from "../types/bill-gateways.types";
import { useCreateBillGateway, useUpdateBillGateway, useBillGateway } from "../query/bill-gateways.query";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Box, Button, Card, Grid, TextField, Typography, MenuItem } from "@mui/material";
import { CommonDropdown } from "@/app/common/dropdown/CommonDropdown";
import { OrganizationPaginatedResponse, OrganizationResponse } from "@/app/organization/organization/types/organization.types";
import { useListOrganization } from "@/app/organization/organization/query/organization.query";

interface Props {
  id?: number;
}

export default function BillGatewaysForm({ id }: Props) {
  const isEditMode = !!id;
  const { data: billGatewayData, isLoading } = useBillGateway(id!);
  const createMutation = useCreateBillGateway();
  const updateMutation = useUpdateBillGateway();
  const router = useRouter();

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<BillGatewaysRequest>({
    resolver: zodResolver(BillGatewaysRequestSchema),
    defaultValues: {
      name: "",
      username: "",
      password: "",
      type: "online",
      status: "active",
      organizationId: 0,
    },
  });

  useEffect(() => {
    if (isEditMode && billGatewayData) {
      reset({
        name: billGatewayData.name,
        username: billGatewayData.username,
        password: "", // Don't prefill password for security
        type: billGatewayData.type,
        status: billGatewayData.status,
        organizationId: billGatewayData.organizationId,
      });
    }
  }, [isEditMode, billGatewayData, reset]);

  const onSubmit = (data: BillGatewaysRequest) => {
    if (isEditMode && id) {
      updateMutation.mutate(
        { id, data },
        {
          onSuccess: () => router.push("/bill/bill-gateways"),
        }
      );
    } else {
      createMutation.mutate(data, {
        onSuccess: () => {
          reset();
          router.push("/bill/bill-gateways");
        },
      });
    }
  };

  if (isEditMode && isLoading) {
    return <Typography>Loading...</Typography>;
  }

  return (
    <Card sx={{ maxWidth: "100%", margin: "auto", mb: 4, p: 3 }}>
      <Typography variant="h6" mb={3}>
        {isEditMode ? "Edit Bill Gateway" : "Create Bill Gateway"}
      </Typography>
      <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
        <Grid container spacing={2}>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Name"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.name}
                  helperText={errors.name?.message}
                  required
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="username"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Username"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.username}
                  helperText={errors.username?.message}
                  required
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Password"
                  type="password"
                  fullWidth
                  size="small"
                  {...field}
                  error={!!errors.password}
                  helperText={errors.password?.message}
                  autoComplete="new-password"
                  required
                />
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="type"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Type"
                  select
                  fullWidth
                  size="small"
                  {...field}
                  value={field.value ?? ""}
                  error={!!errors.type}
                  helperText={errors.type?.message}
                  required
                >
                  <MenuItem value="online">Online</MenuItem>
                  <MenuItem value="offline">Offline</MenuItem>
                </TextField>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <TextField
                  label="Status"
                  select
                  fullWidth
                  size="small"
                  {...field}
                  value={field.value ?? ""}
                  error={!!errors.status}
                  helperText={errors.status?.message}
                  required
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </TextField>
              )}
            />
          </Grid>
          <Grid size={{ xs: 12, md: 6, lg: 4 }}>
            <Controller
              name="organizationId"
              control={control}
              render={({ field }) => (
                <CommonDropdown<
                    OrganizationResponse,
                    OrganizationPaginatedResponse,
                    number
                  >
                    label="Organization"
                    value={field.value}
                    onChange={field.onChange}
                    useDataQuery={useListOrganization}
                    labelKey="name"
                    valueKey="id"
                    searchable
                    searchKey="name"
                    error={!!errors.organizationId}
                    helperText={errors.organizationId?.message}
                  />
              )}
            />
          </Grid>
        </Grid>
        <Box mt={3} display="flex" justifyContent="flex-end">
          <Button type="submit" variant="contained" color="primary" disabled={createMutation.isPending || updateMutation.isPending}>
            {isEditMode ? "Update" : "Create"}
          </Button>
        </Box>
      </Box>
    </Card>
  );
}