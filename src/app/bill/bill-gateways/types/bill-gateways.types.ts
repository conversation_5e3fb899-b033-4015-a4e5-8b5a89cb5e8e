import { MRT_ColumnDef } from "material-react-table";
import { z } from "zod";
import {
  statusOptions,
  statusMap,
  getStatusLabel,
} from "@/app/common/types/status.types";
import { localTime } from "@/app/common/utils/serialize.utils";
import { BaseResponse, PaginatedResponse, SuccessResponse } from "@/app/common/types/common.types";

export interface BillGatewaysProps {
  itemId?: number;
}

// --- REQUEST SCHEMA & TYPE ---
export const BillGatewaysRequestSchema = z.object({
  name: z.string()
    .min(1, "Name is required")
    .transform((val) => val.trim()),
  username: z.string()
    .min(1, "Username is required")
    .transform((val) => val.trim()),
  password: z.string()
    .min(6, "Password must be at least 6 characters"),
  type: z.enum(["online", "offline"], {
    required_error: "Type is required",
  }),
  status: z.enum(["active", "inactive"], {
    required_error: "Status is required",
  }),
  organizationId: z.number()
    .int()
    .positive("Organization ID must be a positive number")
    .optional(),
});

export type BillGatewaysRequest = z.infer<typeof BillGatewaysRequestSchema>;

// --- RESPONSE TYPE ---
export type BillGatewaysResponse = BillGatewaysRequest & BaseResponse & Record<string, unknown>;

// --- PAGINATED & SUCCESS RESPONSES ---
export type BillGatewaysPaginatedResponse = PaginatedResponse<BillGatewaysResponse>;
export type BillGatewaysSuccessResponse = SuccessResponse<BillGatewaysResponse>;

// --- FILTERS ---
export interface BillGatewaysFilters {
  name?: string;
  username?: string;
  type?: "online" | "offline";
  status?: "active" | "inactive";
  organizationId?: number;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: number;
  updatedBy?: number;
}

// --- COLUMNS ---
export const billgatewaysColumns: MRT_ColumnDef<BillGatewaysResponse>[] = [
  { accessorKey: "id", header: "ID", grow: false, size: 50 },
  { accessorKey: "name", header: "Name" },
  { accessorKey: "username", header: "Username" },
  { accessorKey: "type", header: "Type" },
  {
    accessorKey: "status",
    header: "Status",
    filterVariant: "select",
    filterSelectOptions: statusOptions.map((value) => ({
      value,
      label: statusMap[value],
    })),
    Cell: ({ cell }) => {
      const status = cell.getValue<string>();
      return getStatusLabel(status);
    },
  },
  {
    accessorKey: "password",
    header: "Password",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return "•".repeat(value?.length || 8);
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    filterVariant: "date-range",
    Cell: ({ cell }) => {
      const value = cell.getValue<string>();
      return localTime(value);
    },
  },
  {
    accessorKey: "organization.name",
    header: "Organization",
  },
];
