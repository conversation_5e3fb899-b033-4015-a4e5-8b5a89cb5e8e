"use client";

import { billgatewaysColumns, BillGatewaysResponse } from "./types/bill-gateways.types"
import { useBillGateways } from "./query/bill-gateways.query";
import { useDeleteBillGateway } from "./query/bill-gateways.query";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  <PERSON>alogActions,
  Button,
} from "@mui/material";
import { useListRolePermission } from "@/app/organization/role-permission/query/role-permission.query";
import { RolePermissionFilters } from "@/app/organization/role-permission/types/role-permission.types";
import { CommonTable } from "@/app/inventory/common/table/common-table";

function BillGatewaysPage() {
  const router = useRouter();
  const deleteMutation = useDeleteBillGateway();

  const [openDeleteConfirm, setOpenDeleteConfirm] = useState(false);
  const [selectedRow, setSelectedRow] = useState<BillGatewaysResponse | null>(null);
  const [rolePermissionFilters, setRolePermissionFilters] = useState<RolePermissionFilters>({
    moduleName: "bill",
    featureName: "BillGateway",
    roleId: 0,
  });
  const { data: rolePermissionData } = useListRolePermission(
    1,
    10,
    rolePermissionFilters
  );

  useEffect(() => {
    const roleId = Number(localStorage.getItem("default_role_id") ?? 0);
    setRolePermissionFilters((prev) => ({
      ...prev,
      roleId,
    }));
  }, []);

  // Open modal and set row to delete
  const handleOpenDelete = (row: BillGatewaysResponse) => {
    setSelectedRow(row);
    setOpenDeleteConfirm(true);
  };

  // Close modal and clear selected row
  const handleCloseDelete = () => {
    setSelectedRow(null);
    setOpenDeleteConfirm(false);
  };

  // Confirm delete action
  const handleConfirmDelete = () => {
    if (selectedRow && selectedRow.id) {
      deleteMutation.mutate(selectedRow.id, {
        onSuccess: () => {
          handleCloseDelete();
        },
        onError: (error) => {
          console.error("Delete failed:", error);
          handleCloseDelete();
        },
      });
    }
  };

  return (
    <>
      <CommonTable<BillGatewaysResponse>
        title="BillGateway"
        columns={billgatewaysColumns}
        useDataQuery={useBillGateways}
        hiddenColumns={[
          "organization.name",
          "createdAt",
          "updatedAt",
          "createdBy",
          "updatedBy",
        ]}
        onCreate={() => router.push("/bill/bill-gateways/create")}
        onExport={() => console.log("Export BillGateway")}
        onEdit={(row) => router.push(`/bill/bill-gateways/edit/${row.id}`)}
        onDelete={(row) => handleOpenDelete(row)}
        isCreate={rolePermissionData?.data[0].create}
        isEdit={rolePermissionData?.data[0].update}
        isDelete={rolePermissionData?.data[0].delete}
      />

      <Dialog open={openDeleteConfirm} onClose={handleCloseDelete}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete BillGateway item{" "}
            <strong>{selectedRow?.name}</strong>? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDelete} variant="outlined">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            variant="contained"
            color="error"
            disabled={deleteMutation.isPending}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}

export default BillGatewaysPage;
