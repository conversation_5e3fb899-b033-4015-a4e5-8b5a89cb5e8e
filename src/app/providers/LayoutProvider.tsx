"use client";

import React, { useState, ReactNode } from "react";
import { usePathname } from "next/navigation";

import LeftSidebarMenu from "@/app/layouts/LeftSidebarMenu";
import TopNavbar from "@/app/layouts/TopNavbar";
import Footer from "@/app/layouts/Footer";
import ControlPanel from "@/app/layouts/ControlPanel";

interface LayoutProviderProps {
  children: ReactNode;
}

const LayoutProvider: React.FC<LayoutProviderProps> = ({ children }) => {
  const [active, setActive] = useState<boolean>(false);
  const pathname = usePathname();

  const toggleActive = () => setActive(!active);

  const isAuthPage = [
    "/authentication/sign-in/",
    "/authentication/sign-up/",
    "/authentication/forgot-password/",
    "/authentication/reset-password/",
    "/authentication/confirm-email/",
    "/authentication/lock-screen/",
    "/authentication/logout/",
    "/coming-soon/",
    "/",
    "/front-pages/features/",
    "/front-pages/team/",
    "/front-pages/faq/",
    "/front-pages/contact/",
  ].includes(pathname ?? "");

  // ✅ Render bare children only for auth pages
  if (isAuthPage) {
    return <>{children}</>;
  }

  // ✅ Full layout only for non-auth pages
  return (
    <>
      <div className={`main-wrapper-content ${active ? "active" : ""}`}>
        <TopNavbar toggleActive={toggleActive} />
        <LeftSidebarMenu toggleActive={toggleActive} />

        <div className="main-content">{children}</div>

        <Footer />
      </div>

      <div
        style={{
          position: "fixed",
          bottom: "15px",
          right: "15px",
          zIndex: "-5",
          opacity: 0,
          visibility: "hidden",
        }}
      >
        <ControlPanel />
      </div>
    </>
  );
};

export default LayoutProvider;
