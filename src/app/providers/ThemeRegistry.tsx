// app/ThemeRegistry.tsx
'use client';

import * as React from 'react';
import { CssBaseline, ThemeProvider } from '@mui/material';
// import { createTheme } from '@mui/material/styles';
import theme from "@/theme"; // Customize your theme here

export default function ThemeRegistry({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
}
