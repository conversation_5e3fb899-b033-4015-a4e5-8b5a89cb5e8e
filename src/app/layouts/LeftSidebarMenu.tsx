"use client";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { styled } from "@mui/material/styles";
import ArrowForwardIosSharpIcon from "@mui/icons-material/ArrowForwardIosSharp";
import MuiAccordion, { AccordionProps } from "@mui/material/Accordion";
import MuiAccordionSummary, {
  AccordionSummaryProps,
} from "@mui/material/AccordionSummary";
import MuiAccordionDetails from "@mui/material/AccordionDetails";
import { Box, Typography } from "@mui/material";

import Paths from "./paths";

const Accordion = styled((props: AccordionProps) => (
  <MuiAccordion disableGutters elevation={0} square {...props} />
))(({ theme }) => ({
  border: `1px solid ${theme.palette.divider}`,
  "&:not(:last-child)": {
    borderBottom: 0,
  },
  "&::before": {
    display: "none",
  },
}));

const AccordionSummary = styled((props: AccordionSummaryProps) => (
  <MuiAccordionSummary
    expandIcon={<ArrowForwardIosSharpIcon sx={{ fontSize: "0.9rem" }} />}
    {...props}
  />
))(({ theme }) => ({
  backgroundColor: theme.palette.mode === "dark" ? "#3a4252" : "#f6f7f9",
  flexDirection: "row-reverse",
  "& .MuiAccordionSummary-expandIconWrapper.Mui-expanded": {
    transform: "rotate(90deg)",
  },
}));

const AccordionDetails = styled(MuiAccordionDetails)(({ theme }) => ({
  padding: theme.spacing(2),
}));

interface LeftSidebarProps {
  toggleActive: () => void;
}

const LeftSidebarMenu: React.FC<LeftSidebarProps> = ({ toggleActive }) => {
  const pathname = usePathname();
  type ExpandedState = { [key: string]: boolean };

  const [expandedPanels, setExpandedPanels] = React.useState<ExpandedState>({});
  const handleChange =
    (panel: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedPanels((prev) => ({ ...prev, [panel]: isExpanded }));
    };

  return (
    <>
      <Box className="leftSidebarDark">
        <Box className="left-sidebar-menu">
          <Box className="logo">
            <Link href="/hospital/Reception/Dashbord/">
              <Image
                src="/images/logo-icon.svg"
                alt="logo-icon"
                width={20}
                height={20}
              />
              <Typography component={"span"} sx={{ fontSize: "small" }}>
                HIFI HMS
              </Typography>
            </Link>
          </Box>

          <Box className="burger-menu" onClick={toggleActive}>
            <Typography component={"span"} className="top-bar"></Typography>
            <Typography component={"span"} className="middle-bar"></Typography>
            <Typography component={"span"} className="bottom-bar"></Typography>
          </Box>

          <Box className="sidebar-inner">
            <Box className="sidebar-menu">
              {Paths.map((section) => (
                <div key={section.id}>
                  <Typography
                    className="sub-title"
                    sx={{
                      display: "block",
                      fontWeight: "500",
                      textTransform: "uppercase",
                      fontSize: "small",
                    }}
                  >
                    {section.menuname}
                  </Typography>

                  {section.menu?.map((menuItem) => (
                    <Accordion
                      key={`menu-${section.id}-${menuItem.id}`}
                      expanded={!!expandedPanels[menuItem.name]}
                      onChange={handleChange(menuItem.name)}
                      className="mat-accordion"
                    >
                      <AccordionSummary
                        className="mat-summary"
                        aria-controls="panel1d-content"
                        id="panel1d-header"
                      >
                        <Box
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          <i
                            className="material-symbols-outlined"
                            style={{
                              fontSize: "small",
                              display: "flex",
                              alignItems: "center",
                            }}
                          >
                            dashboard
                          </i>
                          <Typography
                            component="span"
                            className="title"
                            sx={{ fontSize: "small" }}
                          >
                            {menuItem.name}
                          </Typography>
                        </Box>
                      </AccordionSummary>
                      {menuItem.submenu?.map((submenuItem) => (
                        <AccordionDetails
                          key={`submenu-${section.id}-${menuItem.id}-${submenuItem.id}`}
                          className="mat-details"
                        >
                          <ul className="sidebar-sub-menu">
                            <li className="sidemenu-item">
                              <Link
                                href={submenuItem.path}
                                className={`sidemenu-link ${
                                  pathname === submenuItem.path ? "active" : ""
                                }`}
                                style={{ fontSize: "small" }}
                              >
                                {submenuItem.name}
                              </Link>
                            </li>
                          </ul>
                        </AccordionDetails>
                      ))}
                    </Accordion>
                  ))}
                </div>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default LeftSidebarMenu;
