// File path: /styles/top-navbar.scss

"use client";

import React, { useEffect } from "react";
import { AppBar, Toolbar, IconButton, Box } from "@mui/material";
import Tooltip from "@mui/material/Tooltip";
import SearchForm from "./SearchForm";
// import Notifications from "./Notifications";
import Profile from "./Profile";
import FullscreenButton from "./FullscreenButton";
// import AppsMenu from "./AppsMenu";
// import ChooseLanguage from "./ChooseLanguage/index";
// import ControlPanel from "../ControlPanel";
import DarkMode from "./DarkMode";
// import { useAuth } from "@/services/query/useAuth";

interface TopNavbarProps {
  toggleActive: () => void;
}

const TopNavbar: React.FC<TopNavbarProps> = ({ toggleActive }) => {
  // const { user, isOffline } = useAuth();
  useEffect(() => {
    const elementId = document.getElementById("navbar");
    document.addEventListener("scroll", () => {
      if (window.scrollY > 100) {
        elementId?.classList.add("sticky");
      } else {
        elementId?.classList.remove("sticky");
      }
    });
  });

  return (
    <>
      <div className="top-navbar-dark">
        <AppBar
          id="navbar"
          color="inherit"
          sx={{
            backgroundColor: "#fff",
            boxShadow: "initial",
            borderRadius: "0 0 15px 15px",
            width: "initial",
            zIndex: "489",
            minHeight: "48px", // optional
          }}
          className="top-navbar"
        >
          <Toolbar
            sx={{
              minHeight: '48px !important',
              display: { xs: "block", sm: "flex" },
              justifyContent: { xs: "center", sm: "space-between" },
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: { xs: "10px", sm: "5px", md: "10px" },
              }}
            >
              <Tooltip title="Hide/Show" arrow>
                <IconButton
                  size="small"
                  edge="start"
                  color="inherit"
                  onClick={toggleActive}
                  className="top-burger"
                  sx={{ fontSize: '18px', p: 0.5 }}
                >
                  <i className="material-symbols-outlined">menu</i>
                </IconButton>
              </Tooltip>

              {/* Search form */}
              <SearchForm />

              {/* AppsMenu */}
              {/* <AppsMenu /> */}
            </Box>

            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: { xs: "8px", sm: "8px", lg: "15px" },
                mt: { xs: "10px", sm: "0px" },
                fontSize: '18px', 
                p: 0.5
              }}
            >
              {/* DarkMode */}
              <DarkMode />

              {/* ChooseLanguage */}
              {/* <ChooseLanguage /> */}

              {/* FullscreenButton */}
              <FullscreenButton />

              {/* Notifications */}
              {/* <Notifications /> */}
              {/* {isOffline && <p style={{ color: "red" }}>🔴</p>} */}
              {/* {isOffline && <p style={{ color: "red" }}>🔴</p>} */}
              {/* Profile */}
              <Profile />

              {/* ControlPanel */}
              {/* <ControlPanel /> */}
            </Box>
          </Toolbar>
        </AppBar>
      </div>
    </>
  );
};

export default TopNavbar;
