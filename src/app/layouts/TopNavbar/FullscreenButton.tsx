"use client";

import React from "react";
import Button from "@mui/material/Button";

const FullscreenButton: React.FC = () => {
  const handleToggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.error(`Failed to enter fullscreen: ${err.message}`);
      });
    } else {
      document.exitFullscreen().catch((err) => {
        console.error(`Failed to exit fullscreen: ${err.message}`);
      });
    }
  };

  return (
    <Button
      variant="text"
      sx={{
        minWidth: "unset",
        padding: 0,
        width: "16px",
        height: "16px",
        minHeight: "unset",
        lineHeight: 1,
      }}
      className="fullscreen-btn text-body"
      onClick={handleToggleFullscreen}
    >
      <i
        className="material-symbols-outlined"
        style={{
          fontSize: "14px",
          lineHeight: "1",
        }}
      >
        fullscreen
      </i>
    </Button>
  );
};

export default FullscreenButton;
