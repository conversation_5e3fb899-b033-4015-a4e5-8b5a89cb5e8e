"use client";

import React, { useState, useEffect } from "react";
import { IconButton, Tooltip } from '@mui/material';
import Brightness7Icon from '@mui/icons-material/Brightness7'; // Sun
import Brightness4Icon from '@mui/icons-material/Brightness4'; // Moon

const DarkMode: React.FC = () => {
  // Light/Dark Mode state
  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);

  useEffect(() => {
    // Retrieve the user's preference from local storage
    const storedPreference = localStorage.getItem("theme");
    if (storedPreference === "dark") {
      setIsDarkMode(true);
    }
  }, []);

  const handleToggle = () => {
    setIsDarkMode(!isDarkMode);
  };

  useEffect(() => {
    // Update the user's preference in local storage
    localStorage.setItem("theme", isDarkMode ? "dark" : "light");

    // Update the class on the <html> element to apply the selected mode
    const htmlElement = document.querySelector("html");
    if (htmlElement) {
      if (isDarkMode) {
        htmlElement.classList.add("dark-theme");
      } else {
        htmlElement.classList.remove("dark-theme");
      }
    }
  }, [isDarkMode]);

  return (
    <IconButton
      onClick={handleToggle}
      sx={{
        fontSize: 20,
        padding: 1,
        color: 'inherit',
      }}
    >
      <Tooltip title={isDarkMode ? 'Light Mode' : 'Dark Mode'} arrow>
        {isDarkMode ? <Brightness7Icon fontSize="small" /> : <Brightness4Icon fontSize="small" />}
      </Tooltip>
    </IconButton>
  );
};

export default DarkMode;
