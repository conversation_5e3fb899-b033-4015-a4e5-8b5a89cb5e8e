export const DoctorPaths = [
    {
        id: 1,
        menuname: "Hospital",
        menu: [{
            id: 1,
            name: "Reception",
            submenu: [{
                id: 'Dashbord',
                name: "Dashbord",
                path: "/hospital/Reception/Dashbord"
            }, {
                id: 'registration',
                name: "Patient registration",
                path: "/hospital/Reception/Patientregistration"
            }, {
                id: 'List',
                name: "Patient List",
                path: "/hospital/Reception/PatientList"
            },]
        },]
    },]

export const LabPaths = [{
    id: 'Lab',
    menuname: "Lab",
    menu: [{
        id: 1,
        name: "Lab Reception",
        submenu: [{
            id: 1,
            name: "Report",
            path: "/Lab/Reception/Dashbord"
        }, {
            id: 2,
            name: "Lab Patient registration",
            path: "/Lab/Reception/Patientregistration"
        },]
    },]
},]

export const adminPaths = [
    {
        id: 3,
        menuname: "Admin",
        menu: [{
            id: 1,
            name: "Organization",
            submenu: [
                {
                    id: 1,
                    name: "Organization",
                    path: "/organization/organization"
                },
                {
                    id: 2,
                    name: "Role",
                    path: "/organization/role"
                },
                {
                    id: 3,
                    name: "Permission",
                    path: "/organization/permission"
                },
                {
                    id: 4,
                    name: "Title",
                    path: "/organization/title"
                },
                {
                    id: 5,
                    name: "User",
                    path: "/organization/users"
                },
            ]
        }]
    },
    {
        id: 'master',
        menuname: "Master",
        menu: [
            {
                id: 1,
                name: "Inventory Master",
                submenu: [
                    {
                        id: 'moleculars',
                        name: "Moleculars",
                        path: "/inventory/molecular"
                    },
                    {
                        id: 'route-of-administration',
                        name: "Route of Administration",
                        path: "/inventory/route-of-administration"
                    },
                    {
                        id: 'dosage-form',
                        name: "Dosage Form",
                        path: "/inventory/dosage-form"
                    },
                    {
                        id: 'category',
                        name: "Category",
                        path: "/inventory/category"
                    },
                    {
                        id: 'combination',
                        name: "Combination",
                        path: "/inventory/combination"
                    },
                    {
                        id: 'gst',
                        name: "GST",
                        path: "/inventory/gst"
                    },
                    {
                        id: 'gst-itc',
                        name: "GST ITC",
                        path: "/inventory/gst-itc"
                    },
                    {
                        id: 'offer-terms',
                        name: "Offer Terms",
                        path: "/inventory/offer-terms"
                    },
                    {
                        id: 'paymentTerm',
                        name: "Payment Term",
                        path: "/inventory/payment-term"
                    },
                    {
                        id: 'store-types',
                        name: "Store Types",
                        path: "/inventory/store-types"
                    },
                    {
                        id: 'UOM',
                        name: "UOM",
                        path: "/inventory/uom"
                    },
                    {
                        id: 'vendors',
                        name: "Vendors",
                        path: "/inventory/vendors"
                    },
                    {
                        id: 'items',
                        name: 'Items',
                        path: '/inventory/items'
                    }
                ]
            },
        ]
    },
     {
        id: 'stock',
        menuname: "Stock",
        menu: [
            {
                id: 1,
                name: "Inventory",
                submenu: [
                    {
                        id: 'stock-summary',
                        name: 'Stock Summary',
                        path: '/inventory/stock-summary'
                    },
                    {
                        id: 'purchase-order',
                        name: "Purchase Order",
                        path: "/inventory/purchase-order"
                    },
                    {
                        id: 'purchase-entry',
                        name: "Purchase Entry",
                        path: "/inventory/purchase-entry"
                    },
                    {
                        id: 'sales-order',
                        name: "Sales Order",
                        path: "/inventory/sales-order"
                    },
                    {
                        id: 'sales-return',
                        name: "Sales Return",
                        path: "/inventory/sales-return"
                    },
                    {
                        id: 'purchase-request',
                        name: "Purchase Request",
                        path: "/inventory/purchase-request"
                    }

                ]
            },
        ]
    }
]

export default adminPaths;