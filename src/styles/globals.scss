/* You can add global styles to this file, and also import other style files */
:root {
    --fontFamily: "Inter", sans-serif;
    --blackColor: #3A4252;
    --whiteColor: #ffffff;
    --bodyColor: #64748B;
    --borderColor: #ECEEF2;
    --transition: .5s;
    --fontSize: 14px;
    --primaryColor: #605DFF;
    --secondaryColor: #3584FC;
    --successColor: #25B003;
    --dangerColor: #FF4023;
    --warningColor: #ffc107;
    --infoColor: #0dcaf0;
    --lightColor: #f8f9fa;
    --darkColor: #212529;
    --purpleColor: #AD63F6;
    --orangeColor: #FD5812;
}

// Utilities CSS
@import "../styles/utilities";
@import "../styles/ui-kit";

body {
    margin: 0;
    padding: 0; 
}
img {
    max-width: 100%;
    height: auto;
}
h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    color: var(--blackColor);
}
p {
    margin-top: 0;
}
a {
    transition: var(--transition) !important;
    text-decoration: none;
}
.hover-text-color:hover {
    color: var(--primaryColor) !important;
}
.transition {
	transition: var(--transition) !important;
}
.cikTable {
    .bx-trending-up {
        color: var(--successColor);
    }
    .bx-trending-down {
        color: var(--dangerColor);
    }
}
.-mr-4 {
    margin-right: -4px;
}

.main-wrapper-content {
    overflow: hidden;
    min-height: 100vh;
    padding-top: 95px;
    padding-left: 285px;
    padding-right: 25px;
    transition: var(--transition);

    &.active {
        padding-left: 85px;
    }
}

textarea {
    font-family: var(--fontFamily);

    &:focus-visible {
        outline: 0;
    }
}
.lcbpm-none {
    &:last-child {
        padding-bottom: 0 !important;
        margin-bottom: 0 !important;
        border-bottom: none !important;
    } 
}
.lcm-0 {
    &:last-child {
        margin-bottom: 0 !important; 
    } 
}
.tlc-td-bp-none {
    &:last-child {
        td {
            padding-bottom: 0;
            border-bottom: none !important;
        }
    }
}

// RTL Style
[dir="rtl"] {

    .main-wrapper-content {
        padding-left: 25px;
        padding-right: 285px;

        &.active { 
            padding-right: 90px;
        }
    }
    
}

/* Max width 599px */
@media (max-width: 599px) {
    .main-wrapper-content {
        padding-top: 140px !important; 
    }
}

/* Max width 1199px */
@media (max-width: 1199px) {
    .main-wrapper-content {
        padding-top: 95px;
        padding-left: 15px;
        padding-right: 15px; 

        &.active {
            padding-left: 15px;
        }
    }

    // RTL Style
    [dir="rtl"] {
        .main-wrapper-content {
            padding-left: 15px;
            padding-right: 15px;
    
            &.active { 
                padding-right: 15px;
            }
        }
    }
}

.rmui-card, table {
    tfoot {
        p {
            margin-bottom: 0; 
        }
    }
}
.MuiTablePagination-root {
    p {
        margin-bottom: 0; 
    }
}
.rmui-ws-calendar {
    .MuiPickersCalendarHeader-root {
        padding-left: 0;
        padding-right: 0;
    }
    .MuiDayCalendar-header {
        justify-content: space-between;

        .MuiTypography-root {
            color: var(--blackColor);
            font-size: 14px;
            font-weight: 500;
        }
    }
    .MuiDayCalendar-weekContainer {
        justify-content: space-between;

        .MuiTypography-root { 
            font-size: 139px; 
        }
    }

    .mui-1kjxb89-MuiButtonBase-root-MuiPickersDay-root:not(.Mui-selected) {
        border: 1px solid var(--primaryColor);
        background-color: var(--primaryColor);
        color: #fff;
    }
}
.upcoming-events {
    .swiper-pagination {
        top: 0;
        right: 0;
        left: auto;
        text-align: end;

        .swiper-pagination-bullet {

            &.swiper-pagination-bullet-active {
                background-color: var(--primaryColor);
            }
        }
    }
}

.top-courses {
    .swiper-pagination {
        bottom: 0;
        left: 0;
        text-align: start;
        width: 50%;

        .swiper-pagination-bullet {
            background-color: #fff;
            width: 5px;
            height: 5px;
            opacity: 1;

            &.swiper-pagination-bullet-active {
                background-color: #ffaa72; 
            }
        }
    }
}

// Breadcrumb
.breadcrumb-card {
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    h5 {
        margin-bottom: 0;
        color: var(--blackColor);
        line-height: 1.2;
        font-size: 18px;
        font-weight: 700;
        margin-top: 0; 
    }

    .breadcrumb {
        list-style-type: none;
        margin: 0;
        padding: 0;
   
        li {
            font-size: 13px;
            margin: {
                right: 26px;
            };
            display: inline-block;
            position: relative;
 
            a {
                color: var(--bodyColor);
                padding-left: 22px;
                text-decoration: none;

                i {
                    left: 0;
                    top: 50%;
                    font-size: 18px;
                    margin-top: -1px;
                    position: absolute;
                    color: var(--primaryColor);
                    transform: translateY(-50%);
                }
                &:hover {
                    color: var(--primaryColor);
                }
            }
            &:before {
                top: 50%;
                right: -22px;
                margin-top: .6px;
                content: "\ea6e";
                position: absolute;
                transform: translateY(-50%);
                font: {
                    size: 18px;
                    weight: normal;
                    family: remixicon;
                };
            }
            &:last-child {
                margin-right: 0;

                &::before {
                    display: none;
                }
            }
        }
    }
}

// RTL Style
[dir="rtl"] {
    .breadcrumb-card {
        .breadcrumb {
            li {
                margin-right: 0;
                margin-left: 26px;

                a {
                    padding-left: 0;
                    padding-right: 22px;
    
                    i {
                        left: auto;
                        right: 0;
                    }
                }
                &:before {
                    right: auto;
                    left: -22px;
                }

                &:last-child {
                    margin-left: 0;
                }
            }
        }
    }
}


/* Max width 767px */
@media only screen and (max-width : 575px) {
    .breadcrumb-card { 
        display: block; 

        h5 {
            margin-bottom: 5px;
        }
    }
}

// rmui-email-table
.rmui-email-table {
    tbody {
        tr {
            td {
                &:first-child {
                    padding-left: 0;
                }
                &:last-child {
                    padding-right: 0;
                }
            }
        }
    }
    tfoot {
        .MuiTablePagination-selectLabel {
            display: none;
        }
        .MuiInputBase-root {
            display: none;
        }
    }
}

// email-content-details
.email-content-details, .rsw-editor {
    h1, h2, h3, h4, h5, h6 {
        font-weight: 500;
        margin-bottom: 15px;
        color: var(--blackColor);
    }
    h1 {
        font-size: 17px;
    }
    h2 {
        font-size: 16px;
    }
    h3 {
        font-size: 15px;
    }
    h4 {
        font-size: 14px;
    }
    h5 {
        font-size: 13px;
    }
    h5 {
        font-size: 12px;
    }
    h6 {
        font-size: 12px;
    }
    p {
        margin-bottom: 15px;
    }
}

// rsw-editor
.rsw-editor {
    h1 {
        font-size: 18px;
    }
    h2 {
        font-size: 16px;
    }
    h3 {
        font-size: 15px;
    }
    h4 {
        font-size: 14px;
    }
}

// share-to-socials
.share-to-socials {
    display: flex;
    align-items: center;
    gap: 5px;
 
    a {
        i {
            padding: 0;
            width: 23.844px;
            font-size: 13px;
            min-width: auto;
            height: 23.844px;
            text-align: center;
            border-radius: 50%;
            line-height: 23.844px;
            display: inline-block;
            color: var(--blackColor);
            background-color: #eceef2; 
        }
    }
}

// pd-description
.pd-description {
    h1, h2, h3, h4, h5, h6 {
        font-weight: 600;
        margin-bottom: 15px;
        color: var(--blackColor);
    }
    h1 {
        font-size: 17px;
    }
    h2 {
        font-size: 16px;
    }
    h3 {
        font-size: 15px;
    }
    h4 {
        font-size: 14px;
    }
    h5 {
        font-size: 13px;
    }
    h5 {
        font-size: 12px;
    }
    h6 {
        font-size: 12px;
    }
    p {
        margin-bottom: 15px;
        line-height: 1.8;

        &:last-child {
            margin-bottom: 0;
        }
    }
    ul {
        padding: 0;
        margin: 0 0 20px;
        border: 1px solid #eceef2;

        &:last-child {
            margin: 0;
        }

        li {
            list-style-type: none;
            border-bottom: 1px solid #eceef2;
            padding: 13px 20px;

            &:last-child {
                border-bottom: none;
            }
        }
    }
}

.rating-item {
    margin-bottom: 10px;

    &:last-child {
        margin-bottom: 0;
    }
}

// followers-table
.followers-table {
    tfoot {
        .MuiToolbar-root {
            .MuiTablePagination-selectLabel, .MuiInputBase-root {
                display: none;
            }
        }
    }
}

// table
table {
    tfoot {
        tr {
            p {
                margin: 0;
            }
        }
    }
}

// auth-main-wrapper
.main-wrapper-content:has(div.auth-main-wrapper) { 
    padding: 0 !important;
}

// fp-wrapper
.main-wrapper-content:has(div.fp-wrapper) { 
    padding: 0 !important;
}

// full-calendar-box
.full-calendar-box {
    .fc {
        .fc-toolbar-title {
            font-size: 18px;
        }
        .fc-button-primary {
            text-transform: capitalize;
        }
        table {
            border-color: var(--borderColor);

            thead {
                tr {
                    th {
                        
                        .fc-scrollgrid-sync-inner {
                            background-color: #ecf0ff;
                            padding: 15px;
                        }
                    }
                }
            }
        }
    }
    .fc-theme-standard td, .fc-theme-standard th {
        border: 1px solid var(--borderColor);
    }
}

.uc-content {
    .MuiGrid-item:nth-last-child(1), .MuiGrid-item:nth-last-child(2) {
        .border-bottom {
            border-bottom: none !important;
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }
    } 
}

// date-time-picker-box
.date-time-picker-box {
    .MuiStack-root {
        padding: 0;

        .MuiFormControl-root {
            min-width: 200px;
            
            .MuiInputBase-input {
                padding: 5px 14px;
                height: auto;
                font-size: 12px;
            }
            .MuiButtonBase-root {
                padding: 4px;
            }
        }
    }
}

// Create Campaigns
// campaign-goal
.campaign-goal {
    label {
        .MuiTypography-root {
            color: var(--blackColor);
            font-weight: 500;
            font-size: 15px;
        }
    }
}

// nft-slide
.nft-slide {
    direction: ltr;
    
    .swiper-button-prev {
        left: 0;
    }
    .swiper-button-next {
        right: 0;
    }
    .swiper-button-prev, .swiper-button-next {
        background-color: var(--blackColor);
        color: #fff;
        width: 30px;
        height: 30px;
        border-radius: 100%;

        &::after {
            font-size: 16px;
        }
    }
}

// table-th-td-lcpx-0
.table-th-td-lcpx-0 {
    thead {
        tr {
            th {
                &:first-child {
                    padding-left: 0;
                }
                &:last-child {
                    padding-right: 0;
                }
            }
        }
    }
    tbody {
        tr {
            td {
                &:first-child {
                    padding-left: 0;
                }
                &:last-child {
                    padding-right: 0;
                }
            }
        }
    }
}
  
/* RTL Style */
[dir="rtl"] {
    .table-th-td-lcpx-0 {
        thead {
            tr {
                th {
                    &:first-child {
                        padding-left: 20px;
                        padding-right: 0;
                    }
                    &:last-child {
                        padding-left: 0;
                        padding-right: 20px;
                    }
                }
            }
        }
        tbody {
            tr {
                td {
                    &:first-child {
                        padding-left: 20px;
                        padding-right: 0;
                    }
                    &:last-child {
                        padding-right: 20px;
                        padding-left: 0;
                    }
                }
            }
        }
    }
}

// tc-slide
.tc-slide {
    .swiper-pagination {
        position: initial;
        line-height: 1;
        text-align: left;
        margin-top: 35px;
    }
}
// rp-slide
.rp-slide {
    .swiper-pagination {
        position: initial;
        line-height: 1;
        text-align: left;
        margin-top: 20px;
    }
}
// sue-slide
.sue-slide {
    .swiper-pagination {
        position: initial;
        line-height: 1; 
        margin-top: 5px;

        .swiper-pagination-bullet {
            width: 5px;
            height: 5px;

            &.swiper-pagination-bullet-active {
                background-color: var(--primaryColor);
                width: 10px;
                border-radius: 20px;
            }
        }
    }
}
 
// date-time-picker-demo
.date-time-picker-demo {
    .react-datetime-picker__wrapper {
        border: 1px solid #eee;
        border-radius: 10px;
        padding: 4px 10px;
        background: #f5f5f5; 

        .react-datetime-picker__inputGroup {
            font-size: 13px;
        }
        .react-datetime-picker__button {
            padding: 0;
            margin-left: 5px;

            svg {
                display: inherit;
                width: 15px;
            }
        }
    }
    .react-calendar {
        border: 1px solid #eee;
        padding: 10px; 
    }
}

// overview-navs
.overview-navs {
    .bg-primary-50 {
        .white-icon {
            display: none;
        }
        &.active {
            background-color: var(--primaryColor) !important;
            color: #fff !important;

            .main-icon {
                display: none;
            }
            .white-icon {
                display: block;
            }
            h5, span, i {
                color: #fff !important;
            }
        }
    }
    .bg-purple-50 {
        .white-icon {
            display: none;
        }
        &.active {
            background-color: var(--purpleColor) !important;
            color: #fff !important;

            .main-icon {
                display: none;
            }
            .white-icon {
                display: block;
            }
            h5, span, i {
                color: #fff !important;
            }
        }
    }
    .bg-orange-50 {
        .white-icon {
            display: none;
        }
        &.active {
            background-color: var(--orangeColor) !important;
            color: #fff !important;

            .main-icon {
                display: none;
            }
            .white-icon {
                display: block;
            }
            h5, span, i {
                color: #fff !important;
            }
        }
    }
}

// nft-card
.nft-card {
    transition: var(--transition);
    
    .bid-btn {
        transform: scale(0);
        transition: var(--transition);
    }
    &:hover {
        .bid-btn {
            transform: scale(1);
        }
    }
}

// t-search-form
.t-search-form {
    position: relative;
    width: 239px; 

    i {
        top: 50%;
        left: 13px;
        font-size: 20px;
        position: absolute;
        transform: translateY(-50%);
    }
    .t-input {
        height: 36px;
        font-size: 12px; 
        background-color: #f6f7f9;
        border: 1px solid #F6F7F9;
        color: var(--blackColor);
        padding: 5px 10px 5px 38px;
        border-radius: 7px;
        display: block;
        width: 100%; 

        &:focus {
            outline: 0;
        }
    }
}
/* dark-theme Style */
.dark-theme {
    .t-search-form {
        i {
            color: #fff;
        }
        .t-input {
            background-color: var(--darkSecondaryColor);
            border: 1px solid var(--darkSecondaryColor);
            color: var(--whiteColor);
        }
    }
}
/* RTL Style */
[dir="rtl"] {
    .t-search-form {
        i {
            left: auto;
            right: 13px;
        }
        .t-input {
            padding: 5px 38px 5px 10px;
        }
    }
}
/* Max width 599px */
@media only screen and (max-width: 599px) {
    .t-search-form {
        width: 100%;
        margin-right: 0;
        margin-top: 15px;
        margin-bottom: 15px;
    }
} 

// rmui-card
.rmui-card {
    &.border-card {
        border: 1px solid var(--borderColor) !important;
    }
    &.card-radius-0 {
        border-radius: 0 !important;
    }
}

// Podcast Dashboard
.most-popular-tabs {
    .tab-btn {
        background-color: #eceef2;
        color: var(--bodyColor);

        &.active {
            background-color: var(--primaryColor);
            color: #fff;
        }
    }
}

.dark-theme {
    .tab-btn {
        background-color: #000;
        color: var(--whiteColor);
    }
}
.mastering-digital-marketing-slide {
    .swiper-pagination {
        text-align: right;
        top: 0; 
        right: 0;
        height: 30px;
    
        .swiper-pagination-bullet {
            background-color: #8695AA;
            width: 10px;
            height: 3px;
            border-radius: 4px;
            transition: var(--transition);
            opacity: 1;
    
            &.swiper-pagination-bullet-active {
                background-color: var(--borderColor); 
                width: 30px;
            }
        }
    }
}

/* RTL Style */
[dir="rtl"] {
    .mastering-digital-marketing-slide {
        .swiper-pagination {
            text-align: left;
            right: auto;
            left: 0;
        }
    }
}
// End Podcast Dashboard