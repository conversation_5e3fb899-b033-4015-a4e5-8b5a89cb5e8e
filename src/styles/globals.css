/* You can add global styles to this file, and also import other style files */
:root {
  --fontFamily: "Inter", sans-serif;
  --blackColor: #3A4252;
  --whiteColor: #ffffff;
  --bodyColor: #64748B;
  --borderColor: #ECEEF2;
  --transition: .5s;
  --fontSize: 14px;
  --primaryColor: #605DFF;
  --secondaryColor: #3584FC;
  --successColor: #25B003;
  --dangerColor: #FF4023;
  --warningColor: #ffc107;
  --infoColor: #0dcaf0;
  --lightColor: #f8f9fa;
  --darkColor: #212529;
  --purpleColor: #AD63F6;
  --orangeColor: #FD5812;
}

.text-start {
  text-align: start !important;
}

.text-center {
  text-align: center !important;
}

.text-end {
  text-align: end !important;
}

.d-none {
  display: none;
}

.left-0 {
  left: 0;
}

.-mb-5 {
  margin-bottom: -5px !important;
}

.po-right-minus-70 {
  right: -70px;
}

.left-auto {
  left: auto !important;
}

.po-right-20 {
  right: 20px;
}

.po-left-32 {
  left: 32%;
}

.po-left-15 {
  left: 15px;
}

.po-right-35 {
  right: 35%;
}

.-left-20 {
  left: -20px;
}

.-po-right-80 {
  right: -80px;
}

[dir=rtl] .left-0 {
  left: auto;
  right: 0;
}
[dir=rtl] .-mr-10 {
  margin-right: auto !important;
  margin-left: -10px !important;
}
[dir=rtl] .po-left-32 {
  left: auto;
  right: 32%;
}
[dir=rtl] .po-right-35 {
  right: auto;
  left: 35%;
}
[dir=rtl] .po-right-20 {
  right: auto;
  left: 20px;
}
[dir=rtl] .-left-20 {
  left: auto;
  right: -20px;
}
[dir=rtl] .-po-right-80 {
  right: auto;
  left: -80px;
}
[dir=rtl] .po-left-15 {
  left: auto;
  right: 15px;
}

.bg-eceef2 {
  background-color: #eceef2 !important;
}

.bg-white {
  background-color: var(--whiteColor) !important;
}

.bg-black {
  background-color: var(--blackColor) !important;
}

.bg-primary {
  background-color: var(--primaryColor) !important;
}

.bg-body {
  background-color: var(--bodyColor) !important;
}

.bg-transparent {
  background-color: transparent !important;
}

.bg-gray {
  background-color: #F5F7F8 !important;
}

.bg-gray-50 {
  background-color: #f6f7f9 !important;
}

.bg-secondary {
  background-color: var(--secondaryColor) !important;
}

.bg-success {
  background-color: var(--successColor) !important;
}

.bg-danger {
  background-color: var(--dangerColor) !important;
}

.bg-warning {
  background-color: var(--warningColor) !important;
}

.bg-info {
  background-color: var(--infoColor) !important;
}

.bg-light {
  background-color: var(--lightColor) !important;
}

.bg-dark {
  background-color: var(--darkColor) !important;
}

.bg-purple {
  background-color: var(--purpleColor) !important;
}

.bg-orange {
  background-color: var(--orangeColor) !important;
}

.bg-primary-50 {
  background-color: #ECF0FF !important;
}

.bg-danger-50 {
  background-color: #FFF2F0 !important;
}

.bg-success-50 {
  background-color: #EEFFE5 !important;
}

.bg-secondary-50 {
  background-color: #DAEBFF !important;
}

.bg-secondary-500 {
  background-color: rgb(53, 132, 252) !important;
}

.bg-purple-50 {
  background-color: #FAF5FF !important;
}

.bg-orange-50 {
  background-color: #FFF5ED !important;
}

.bg-purple-100 {
  background-color: #F3E8FF !important;
}

.bg-orange-100 {
  background-color: #FFE8D4 !important;
}

.bg-success-100 {
  background-color: #D8FFC8 !important;
}

.bg-success-500 {
  background-color: rgb(55, 216, 10) !important;
}

.bg-secondary-100 {
  background-color: #DAEBFF !important;
}

.bg-primary-100 {
  background-color: #DDE4FF !important;
}

.bg-danger-100 {
  background-color: #FFE1DD !important;
}

.bg-info-100 {
  background-color: rgb(179, 236, 252) !important;
}

.bg-danger-200 {
  background-color: #FFC8C0 !important;
}

.bg-primary-500 {
  background-color: #605DFF !important;
}

.bg-purple-700 {
  background-color: #605DFF !important;
}

.bg-f6f7f9 {
  background-color: #f6f7f9 !important;
}

.bg-ecf0ff {
  background-color: #ecf0ff !important;
}

.bg-f4f6fc {
  background-color: #f4f6fc !important;
}

.bg-purple-500 {
  background-color: rgb(173, 99, 246) !important;
}

.bg-purple-700 {
  background-color: rgb(124, 36, 204) !important;
}

.bg-orange-100 {
  background-color: rgb(255, 232, 212) !important;
}

.bg-orange-400 {
  background-color: rgb(254, 122, 54) !important;
}

.bg-orange-500 {
  background-color: rgb(253, 88, 18) !important;
}

.bg-grey-100 {
  background-color: #eceef2;
}

.text-body {
  color: var(--bodyColor) !important;
}

.text-white {
  color: var(--whiteColor) !important;
}

.text-black {
  color: var(--blackColor) !important;
}

.text-primary {
  color: var(--primaryColor) !important;
}

.text-primary-500 {
  color: #605dff !important;
}

.text-transparent {
  color: transparent !important;
}

.text-secondary {
  color: var(--secondaryColor) !important;
}

.text-secondary-500 {
  color: rgb(53, 132, 252) !important;
}

.text-success {
  color: var(--successColor) !important;
}

.text-success-600 {
  color: rgb(30, 131, 8) !important;
}

.text-success-700 {
  color: rgb(30, 131, 8) !important;
}

.text-danger {
  color: var(--dangerColor) !important;
}

.text-orange-500 {
  color: #fd5812 !important;
}

.text-orange-600 {
  color: rgb(238, 62, 8) !important;
}

.text-warning {
  color: var(--warningColor) !important;
}

.text-info {
  color: var(--infoColor) !important;
}

.text-light {
  color: var(--lightColor) !important;
}

.text-dark {
  color: var(--darkColor) !important;
}

.text-purple {
  color: var(--purpleColor) !important;
}

.text-orange {
  color: var(--orangeColor) !important;
}

.text-gray-400 {
  color: #8695aa;
}

.text-gray-300 {
  color: #b1bbc8;
}

.border {
  border: 1px solid #edeff5 !important;
}

.border-top {
  border-top: 1px solid #edeff5 !important;
}

.border-right {
  border-right: 1px solid #edeff5 !important;
}

.border-bottom {
  border-bottom: 1px solid #edeff5 !important;
}

.border-left {
  border-left: 1px solid #edeff5 !important;
}

.border-none {
  border: none !important;
}

.border-color-primary {
  border-color: var(--primaryColor) !important;
}

.border-radius {
  border-radius: 7px !important;
}

.border-top-radius {
  border-radius: 7px 7px 0 0 !important;
}

.border-bottom-radius {
  border-radius: 0 0 7px 7px !important;
}

.border-right-radius {
  border-radius: 0 7px 7px 0 !important;
}

.border-left-radius {
  border-radius: 7px 0 0 7px !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.border-radius-0 {
  border-radius: 0 !important;
}

.po-right-0 {
  right: 0 !important;
}

.po-left-0 {
  left: 0;
}

.po-right-28 {
  right: 28px;
}

.po-right-25 {
  right: 25px;
}

[dir=rtl] .po-right-0 {
  right: auto !important;
  left: 0 !important;
}
[dir=rtl] .po-left-0 {
  left: auto;
  right: 0;
}
[dir=rtl] .po-right-28 {
  right: auto;
  left: 28px;
}
[dir=rtl] .po-right-25 {
  right: auto;
  left: 25px;
}
[dir=rtl] .po-right-minus-70 {
  right: auto;
  left: -70px;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.trezo-badge {
  background-color: #ECF0FF;
  color: var(--primaryColor);
  display: inline-block;
  border-radius: 4px;
  padding: 3px 8px;
  font-weight: 500;
  font-size: 12px;
}
.trezo-badge.pending, .trezo-badge.Pending {
  background-color: rgba(255, 193, 7, 0.15);
  color: var(--warningColor);
}
.trezo-badge.rejected, .trezo-badge.Rejected, .trezo-badge.NotAvailable, .trezo-badge.notAvailable .trezo-badge.Not.Available, .trezo-badge.not.available, .trezo-badge.Not, .trezo-badge.not, .trezo-badge.past, .trezo-badge.Past {
  background-color: #FFE1DD;
  color: var(--dangerColor);
}
.trezo-badge.pInProgress, .trezo-badge.cancelled, .trezo-badge.Cancelled, .trezo-badge.draft, .trezo-badge.Draft, .trezo-badge.deactive, .trezo-badge.Deactive, .trezo-badge.Deactivate, .trezo-badge.deactivate, .trezo-badge.Unread, .trezo-badge.unread {
  background-color: #FFE8D4;
  color: #EE3E08;
}
.trezo-badge.inProgress, .trezo-badge.in.Progress, .trezo-badge.In.Progress, .trezo-badge.finished, .trezo-badge .Finished, .trezo-badge.shipped, .trezo-badge.Shipped, .trezo-badge.pFinished {
  background-color: #D8FFC8;
  color: var(--successColor);
}
.trezo-badge.pPending {
  background-color: #F3E8FF;
  color: #7C24CC;
}
.trezo-badge.rescheduled, .trezo-badge.Rescheduled {
  color: #c52b09;
  background-color: #ffe8d4;
}

.ml-1 {
  margin-left: 1px !important;
}

.ml-2 {
  margin-left: 2px !important;
}

.ml-3 {
  margin-left: 3px !important;
}

.ml-4 {
  margin-left: 4px !important;
}

.ml-5 {
  margin-left: 5px !important;
}

.ml-6 {
  margin-left: 6px !important;
}

.ml-7 {
  margin-left: 7px !important;
}

.ml-8 {
  margin-left: 8px !important;
}

.ml-9 {
  margin-left: 9px !important;
}

.ml-10 {
  margin-left: 10px !important;
}

.ml-11 {
  margin-left: 11px !important;
}

.ml-12 {
  margin-left: 12px !important;
}

.ml-13 {
  margin-left: 13px !important;
}

.ml-14 {
  margin-left: 14px !important;
}

.ml-15 {
  margin-left: 15px !important;
}

[dir=rtl] .ml-1 {
  margin-left: 0 !important;
  margin-right: 1px !important;
}
[dir=rtl] .ml-2 {
  margin-left: 0 !important;
  margin-right: 2px !important;
}
[dir=rtl] .ml-3 {
  margin-left: 0 !important;
  margin-right: 3px !important;
}
[dir=rtl] .ml-4 {
  margin-left: 0 !important;
  margin-right: 4px !important;
}
[dir=rtl] .ml-5 {
  margin-left: 0 !important;
  margin-right: 5px !important;
}
[dir=rtl] .ml-6 {
  margin-left: 0 !important;
  margin-right: 6px !important;
}
[dir=rtl] .ml-7 {
  margin-left: 0 !important;
  margin-right: 7px !important;
}
[dir=rtl] .ml-8 {
  margin-left: 0 !important;
  margin-right: 8px !important;
}
[dir=rtl] .ml-9 {
  margin-left: 0 !important;
  margin-right: 9px !important;
}
[dir=rtl] .ml-10 {
  margin-left: 0 !important;
  margin-right: 10px !important;
}
[dir=rtl] .ml-11 {
  margin-left: 0 !important;
  margin-right: 11px !important;
}
[dir=rtl] .ml-12 {
  margin-left: 0 !important;
  margin-right: 12px !important;
}
[dir=rtl] .ml-13 {
  margin-left: 0 !important;
  margin-right: 13px !important;
}
[dir=rtl] .ml-14 {
  margin-left: 0 !important;
  margin-right: 14px !important;
}
[dir=rtl] .ml-15 {
  margin-left: 0 !important;
  margin-right: 15px !important;
}

.mr-1 {
  margin-right: 1px !important;
}

.mr-2 {
  margin-right: 2px !important;
}

.mr-3 {
  margin-right: 3px !important;
}

.mr-4 {
  margin-right: 4px !important;
}

.mr-5 {
  margin-right: 5px !important;
}

.mr-6 {
  margin-right: 6px !important;
}

.mr-7 {
  margin-right: 7px !important;
}

.mr-8 {
  margin-right: 8px !important;
}

.mr-9 {
  margin-right: 9px !important;
}

.mr-10 {
  margin-right: 10px !important;
}

.mr-11 {
  margin-right: 11px !important;
}

.mr-12 {
  margin-right: 12px !important;
}

.mr-13 {
  margin-right: 13px !important;
}

.mr-14 {
  margin-right: 14px !important;
}

.mr-15 {
  margin-right: 15px !important;
}

.-mr-10 {
  margin-right: -10px !important;
}

.-mr-25 {
  margin-right: -25px !important;
}

[dir=rtl] .mr-1 {
  margin-right: 0 !important;
  margin-left: 1px !important;
}
[dir=rtl] .mr-2 {
  margin-right: 0 !important;
  margin-left: 2px !important;
}
[dir=rtl] .mr-3 {
  margin-right: 0 !important;
  margin-left: 3px !important;
}
[dir=rtl] .mr-4 {
  margin-right: 0 !important;
  margin-left: 4px !important;
}
[dir=rtl] .mr-5 {
  margin-right: 0 !important;
  margin-left: 5px !important;
}
[dir=rtl] .mr-6 {
  margin-right: 0 !important;
  margin-left: 6px !important;
}
[dir=rtl] .mr-7 {
  margin-right: 0 !important;
  margin-left: 7px !important;
}
[dir=rtl] .mr-8 {
  margin-right: 0 !important;
  margin-left: 8px !important;
}
[dir=rtl] .mr-9 {
  margin-right: 0 !important;
  margin-left: 9px !important;
}
[dir=rtl] .mr-10 {
  margin-right: 0 !important;
  margin-left: 10px !important;
}
[dir=rtl] .mr-11 {
  margin-right: 0 !important;
  margin-left: 11px !important;
}
[dir=rtl] .mr-12 {
  margin-right: 0 !important;
  margin-left: 12px !important;
}
[dir=rtl] .mr-13 {
  margin-right: 0 !important;
  margin-left: 13px !important;
}
[dir=rtl] .mr-14 {
  margin-right: 0 !important;
  margin-left: 14px !important;
}
[dir=rtl] .mr-15 {
  margin-right: 0 !important;
  margin-left: 15px !important;
}
[dir=rtl] .-mr-10 {
  margin-right: 0 !important;
  margin-left: -10px !important;
}
[dir=rtl] .-mr-25 {
  margin-right: 0 !important;
  margin-left: -25px !important;
}

.pl-0 {
  padding-left: 0 !important;
}

.pl-1 {
  padding-left: 1px !important;
}

.pl-2 {
  padding-left: 2px !important;
}

.pl-3 {
  padding-left: 3px !important;
}

.pl-4 {
  padding-left: 4px !important;
}

.pl-5 {
  padding-left: 5px !important;
}

.pl-6 {
  padding-left: 6px !important;
}

.pl-7 {
  padding-left: 7px !important;
}

.pl-8 {
  padding-left: 8px !important;
}

.pl-9 {
  padding-left: 9px !important;
}

.pl-10 {
  padding-left: 10px !important;
}

.pl-11 {
  padding-left: 11px !important;
}

.pl-12 {
  padding-left: 12px !important;
}

.pl-13 {
  padding-left: 13px !important;
}

.pl-14 {
  padding-left: 14px !important;
}

.pl-15 {
  padding-left: 15px !important;
}

[dir=rtl] .pl-0 {
  padding-right: 0 !important;
}
[dir=rtl] .pl-1 {
  padding-left: 0 !important;
  padding-right: 1px !important;
}
[dir=rtl] .pl-2 {
  padding-left: 0 !important;
  padding-right: 2px !important;
}
[dir=rtl] .pl-3 {
  padding-left: 0 !important;
  padding-right: 3px !important;
}
[dir=rtl] .pl-4 {
  padding-left: 0 !important;
  padding-right: 4px !important;
}
[dir=rtl] .pl-5 {
  padding-left: 0 !important;
  padding-right: 5px !important;
}
[dir=rtl] .pl-6 {
  padding-left: 0 !important;
  padding-right: 6px !important;
}
[dir=rtl] .pl-7 {
  padding-left: 0 !important;
  padding-right: 7px !important;
}
[dir=rtl] .pl-8 {
  padding-left: 0 !important;
  padding-right: 8px !important;
}
[dir=rtl] .pl-9 {
  padding-left: 0 !important;
  padding-right: 9px !important;
}
[dir=rtl] .pl-10 {
  padding-left: 0 !important;
  padding-right: 10px !important;
}
[dir=rtl] .pl-11 {
  padding-left: 0 !important;
  padding-right: 11px !important;
}
[dir=rtl] .pl-12 {
  padding-left: 0 !important;
  padding-right: 12px !important;
}
[dir=rtl] .pl-13 {
  padding-left: 0 !important;
  padding-right: 13px !important;
}
[dir=rtl] .pl-14 {
  padding-left: 0 !important;
  padding-right: 14px !important;
}
[dir=rtl] .pl-15 {
  padding-left: 0 !important;
  padding-right: 15px !important;
}

.pr-0 {
  padding-right: 0 !important;
}

.pr-1 {
  padding-right: 1px !important;
}

.pr-2 {
  padding-right: 2px !important;
}

.pr-3 {
  padding-right: 3px !important;
}

.pr-4 {
  padding-right: 4px !important;
}

.pr-5 {
  padding-right: 5px !important;
}

.pr-6 {
  padding-right: 6px !important;
}

.pr-7 {
  padding-right: 7px !important;
}

.pr-8 {
  padding-right: 8px !important;
}

.pr-9 {
  padding-right: 9px !important;
}

.pr-10 {
  padding-right: 10px !important;
}

.pr-11 {
  padding-right: 11px !important;
}

.pr-12 {
  padding-right: 12px !important;
}

.pr-13 {
  padding-right: 13px !important;
}

.pr-14 {
  padding-right: 14px !important;
}

.pr-15 {
  padding-right: 15px !important;
}

[dir=rtl] .pr-0 {
  padding-left: 0 !important;
}
[dir=rtl] .pr-1 {
  padding-right: 0 !important;
  padding-left: 1px !important;
}
[dir=rtl] .pr-2 {
  padding-right: 0 !important;
  padding-left: 2px !important;
}
[dir=rtl] .pr-3 {
  padding-right: 0 !important;
  padding-left: 3px !important;
}
[dir=rtl] .pr-4 {
  padding-right: 0 !important;
  padding-left: 4px !important;
}
[dir=rtl] .pr-5 {
  padding-right: 0 !important;
  padding-left: 5px !important;
}
[dir=rtl] .pr-6 {
  padding-right: 0 !important;
  padding-left: 6px !important;
}
[dir=rtl] .pr-7 {
  padding-right: 0 !important;
  padding-left: 7px !important;
}
[dir=rtl] .pr-8 {
  padding-right: 0 !important;
  padding-left: 8px !important;
}
[dir=rtl] .pr-9 {
  padding-right: 0 !important;
  padding-left: 9px !important;
}
[dir=rtl] .pr-10 {
  padding-right: 0 !important;
  padding-left: 10px !important;
}
[dir=rtl] .pr-11 {
  padding-right: 0 !important;
  padding-left: 11px !important;
}
[dir=rtl] .pr-12 {
  padding-right: 0 !important;
  padding-left: 12px !important;
}
[dir=rtl] .pr-13 {
  padding-right: 0 !important;
  padding-left: 13px !important;
}
[dir=rtl] .pr-14 {
  padding-right: 0 !important;
  padding-left: 14px !important;
}
[dir=rtl] .pr-15 {
  padding-right: 0 !important;
  padding-left: 15px !important;
}

.apexcharts-canvas .apexcharts-legend-text {
  font-family: var(--fontFamily) !important;
}
.apexcharts-canvas .apexcharts-tooltip-series-group {
  text-align: start;
  padding-left: 12px;
  padding-right: 12px;
}
.apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active {
  padding-bottom: 0;
}
.apexcharts-canvas .apexcharts-tooltip-series-group:last-child {
  padding-bottom: 4px;
}
.apexcharts-canvas .apexcharts-tooltip * {
  font-family: var(--fontFamily) !important;
}
.apexcharts-canvas .apexcharts-tooltip.apexcharts-theme-light {
  border-radius: 7px;
  border: 1px solid #ECEEF2;
  background: var(--whiteColor);
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}
.apexcharts-canvas .apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title {
  padding: 9px 12px;
  margin-bottom: 5px;
  background: #F6F7F9;
  color: var(--blackColor);
  border-radius: 7px 7px 0 0;
  border-bottom: 0 solid #EAECF0;
  font-size: 13px !important;
  font-weight: 600;
}
.apexcharts-canvas .apexcharts-tooltip.apexcharts-theme-dark .apexcharts-tooltip-series-group {
  background-color: var(--whiteColor) !important;
}
.apexcharts-canvas .apexcharts-tooltip-text-y-label, .apexcharts-canvas .apexcharts-tooltip-text-goals-value, .apexcharts-canvas .apexcharts-tooltip-text-y-value, .apexcharts-canvas .apexcharts-tooltip-text-z-value {
  color: var(--blackColor);
  margin-left: 0;
}
.apexcharts-canvas .apexcharts-tooltip-marker {
  width: 10px;
  height: 10px;
  margin-right: 6px;
}
.apexcharts-canvas .apexcharts-text {
  font-family: var(--fontFamily) !important;
}
.apexcharts-canvas .apexcharts-text tspan {
  font-family: var(--fontFamily) !important;
}
.apexcharts-canvas .apexcharts-xaxistooltip {
  border: 0;
  margin-top: -1px;
  color: var(--blackColor);
  background: var(--whiteColor);
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
}
.apexcharts-canvas .apexcharts-xaxistooltip .apexcharts-xaxistooltip-text {
  font-weight: 600;
  font-size: 13px !important;
  font-family: var(--fontFamily) !important;
}
.apexcharts-canvas .apexcharts-xaxistooltip::before, .apexcharts-canvas .apexcharts-xaxistooltip::after {
  display: none;
}
.apexcharts-canvas .apexcharts-toolbar {
  text-align: end;
  margin-top: 2px;
  padding: 0;
}
.apexcharts-canvas .apexcharts-menu {
  padding: 5px;
  min-width: 125px;
  text-align: start;
  border-radius: 7px;
  border: 1px solid #ECEEF2;
  background: var(--whiteColor);
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
}
.apexcharts-canvas .apexcharts-menu .apexcharts-menu-item {
  transition: var(--transition);
  border-radius: 3px;
  padding: 5px 8px;
  font-size: 13px;
}
.apexcharts-canvas .apexcharts-menu .apexcharts-menu-item:hover {
  color: var(--primaryColor);
}
.apexcharts-canvas .apexcharts-xcrosshairs, .apexcharts-canvas .apexcharts-ycrosshairs {
  fill: #DDE4FF;
  stroke-dasharray: 4px;
}

body {
  margin: 0;
  padding: 0;
}

img {
  max-width: 100%;
  height: auto;
}

h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  color: var(--blackColor);
}

p {
  margin-top: 0;
}

a {
  transition: var(--transition) !important;
  text-decoration: none;
}

.hover-text-color:hover {
  color: var(--primaryColor) !important;
}

.transition {
  transition: var(--transition) !important;
}

.cikTable .bx-trending-up {
  color: var(--successColor);
}
.cikTable .bx-trending-down {
  color: var(--dangerColor);
}

.-mr-4 {
  margin-right: -4px;
}

.main-wrapper-content {
  overflow: hidden;
  min-height: 100vh;
  padding-top: 95px;
  padding-left: 285px;
  padding-right: 25px;
  transition: var(--transition);
}
.main-wrapper-content.active {
  padding-left: 85px;
}

textarea {
  font-family: var(--fontFamily);
}
textarea:focus-visible {
  outline: 0;
}

.lcbpm-none:last-child {
  padding-bottom: 0 !important;
  margin-bottom: 0 !important;
  border-bottom: none !important;
}

.lcm-0:last-child {
  margin-bottom: 0 !important;
}

.tlc-td-bp-none:last-child td {
  padding-bottom: 0;
  border-bottom: none !important;
}

[dir=rtl] .main-wrapper-content {
  padding-left: 25px;
  padding-right: 285px;
}
[dir=rtl] .main-wrapper-content.active {
  padding-right: 90px;
}

/* Max width 599px */
@media (max-width: 599px) {
  .main-wrapper-content {
    padding-top: 140px !important;
  }
}
/* Max width 1199px */
@media (max-width: 1199px) {
  .main-wrapper-content {
    padding-top: 95px;
    padding-left: 15px;
    padding-right: 15px;
  }
  .main-wrapper-content.active {
    padding-left: 15px;
  }
  [dir=rtl] .main-wrapper-content {
    padding-left: 15px;
    padding-right: 15px;
  }
  [dir=rtl] .main-wrapper-content.active {
    padding-right: 15px;
  }
}
.rmui-card tfoot p, table tfoot p {
  margin-bottom: 0;
}

.MuiTablePagination-root p {
  margin-bottom: 0;
}

.rmui-ws-calendar .MuiPickersCalendarHeader-root {
  padding-left: 0;
  padding-right: 0;
}
.rmui-ws-calendar .MuiDayCalendar-header {
  justify-content: space-between;
}
.rmui-ws-calendar .MuiDayCalendar-header .MuiTypography-root {
  color: var(--blackColor);
  font-size: 14px;
  font-weight: 500;
}
.rmui-ws-calendar .MuiDayCalendar-weekContainer {
  justify-content: space-between;
}
.rmui-ws-calendar .MuiDayCalendar-weekContainer .MuiTypography-root {
  font-size: 139px;
}
.rmui-ws-calendar .mui-1kjxb89-MuiButtonBase-root-MuiPickersDay-root:not(.Mui-selected) {
  border: 1px solid var(--primaryColor);
  background-color: var(--primaryColor);
  color: #fff;
}

.upcoming-events .swiper-pagination {
  top: 0;
  right: 0;
  left: auto;
  text-align: end;
}
.upcoming-events .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: var(--primaryColor);
}

.top-courses .swiper-pagination {
  bottom: 0;
  left: 0;
  text-align: start;
  width: 50%;
}
.top-courses .swiper-pagination .swiper-pagination-bullet {
  background-color: #fff;
  width: 5px;
  height: 5px;
  opacity: 1;
}
.top-courses .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: #ffaa72;
}

.breadcrumb-card {
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.breadcrumb-card h5 {
  margin-bottom: 0;
  color: var(--blackColor);
  line-height: 1.2;
  font-size: 18px;
  font-weight: 700;
  margin-top: 0;
}
.breadcrumb-card .breadcrumb {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
.breadcrumb-card .breadcrumb li {
  font-size: 13px;
  margin-right: 26px;
  display: inline-block;
  position: relative;
}
.breadcrumb-card .breadcrumb li a {
  color: var(--bodyColor);
  padding-left: 22px;
  text-decoration: none;
}
.breadcrumb-card .breadcrumb li a i {
  left: 0;
  top: 50%;
  font-size: 18px;
  margin-top: -1px;
  position: absolute;
  color: var(--primaryColor);
  transform: translateY(-50%);
}
.breadcrumb-card .breadcrumb li a:hover {
  color: var(--primaryColor);
}
.breadcrumb-card .breadcrumb li:before {
  top: 50%;
  right: -22px;
  margin-top: 0.6px;
  content: "\ea6e";
  position: absolute;
  transform: translateY(-50%);
  font-size: 18px;
  font-weight: normal;
  font-family: remixicon;
}
.breadcrumb-card .breadcrumb li:last-child {
  margin-right: 0;
}
.breadcrumb-card .breadcrumb li:last-child::before {
  display: none;
}

[dir=rtl] .breadcrumb-card .breadcrumb li {
  margin-right: 0;
  margin-left: 26px;
}
[dir=rtl] .breadcrumb-card .breadcrumb li a {
  padding-left: 0;
  padding-right: 22px;
}
[dir=rtl] .breadcrumb-card .breadcrumb li a i {
  left: auto;
  right: 0;
}
[dir=rtl] .breadcrumb-card .breadcrumb li:before {
  right: auto;
  left: -22px;
}
[dir=rtl] .breadcrumb-card .breadcrumb li:last-child {
  margin-left: 0;
}

/* Max width 767px */
@media only screen and (max-width: 575px) {
  .breadcrumb-card {
    display: block;
  }
  .breadcrumb-card h5 {
    margin-bottom: 5px;
  }
}
.rmui-email-table tbody tr td:first-child {
  padding-left: 0;
}
.rmui-email-table tbody tr td:last-child {
  padding-right: 0;
}
.rmui-email-table tfoot .MuiTablePagination-selectLabel {
  display: none;
}
.rmui-email-table tfoot .MuiInputBase-root {
  display: none;
}

.email-content-details h1, .email-content-details h2, .email-content-details h3, .email-content-details h4, .email-content-details h5, .email-content-details h6, .rsw-editor h1, .rsw-editor h2, .rsw-editor h3, .rsw-editor h4, .rsw-editor h5, .rsw-editor h6 {
  font-weight: 500;
  margin-bottom: 15px;
  color: var(--blackColor);
}
.email-content-details h1, .rsw-editor h1 {
  font-size: 17px;
}
.email-content-details h2, .rsw-editor h2 {
  font-size: 16px;
}
.email-content-details h3, .rsw-editor h3 {
  font-size: 15px;
}
.email-content-details h4, .rsw-editor h4 {
  font-size: 14px;
}
.email-content-details h5, .rsw-editor h5 {
  font-size: 13px;
}
.email-content-details h5, .rsw-editor h5 {
  font-size: 12px;
}
.email-content-details h6, .rsw-editor h6 {
  font-size: 12px;
}
.email-content-details p, .rsw-editor p {
  margin-bottom: 15px;
}

.rsw-editor h1 {
  font-size: 18px;
}
.rsw-editor h2 {
  font-size: 16px;
}
.rsw-editor h3 {
  font-size: 15px;
}
.rsw-editor h4 {
  font-size: 14px;
}

.share-to-socials {
  display: flex;
  align-items: center;
  gap: 5px;
}
.share-to-socials a i {
  padding: 0;
  width: 23.844px;
  font-size: 13px;
  min-width: auto;
  height: 23.844px;
  text-align: center;
  border-radius: 50%;
  line-height: 23.844px;
  display: inline-block;
  color: var(--blackColor);
  background-color: #eceef2;
}

.pd-description h1, .pd-description h2, .pd-description h3, .pd-description h4, .pd-description h5, .pd-description h6 {
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--blackColor);
}
.pd-description h1 {
  font-size: 17px;
}
.pd-description h2 {
  font-size: 16px;
}
.pd-description h3 {
  font-size: 15px;
}
.pd-description h4 {
  font-size: 14px;
}
.pd-description h5 {
  font-size: 13px;
}
.pd-description h5 {
  font-size: 12px;
}
.pd-description h6 {
  font-size: 12px;
}
.pd-description p {
  margin-bottom: 15px;
  line-height: 1.8;
}
.pd-description p:last-child {
  margin-bottom: 0;
}
.pd-description ul {
  padding: 0;
  margin: 0 0 20px;
  border: 1px solid #eceef2;
}
.pd-description ul:last-child {
  margin: 0;
}
.pd-description ul li {
  list-style-type: none;
  border-bottom: 1px solid #eceef2;
  padding: 13px 20px;
}
.pd-description ul li:last-child {
  border-bottom: none;
}

.rating-item {
  margin-bottom: 10px;
}
.rating-item:last-child {
  margin-bottom: 0;
}

.followers-table tfoot .MuiToolbar-root .MuiTablePagination-selectLabel, .followers-table tfoot .MuiToolbar-root .MuiInputBase-root {
  display: none;
}

table tfoot tr p {
  margin: 0;
}

.main-wrapper-content:has(div.auth-main-wrapper) {
  padding: 0 !important;
}

.main-wrapper-content:has(div.fp-wrapper) {
  padding: 0 !important;
}

.full-calendar-box .fc .fc-toolbar-title {
  font-size: 18px;
}
.full-calendar-box .fc .fc-button-primary {
  text-transform: capitalize;
}
.full-calendar-box .fc table {
  border-color: var(--borderColor);
}
.full-calendar-box .fc table thead tr th .fc-scrollgrid-sync-inner {
  background-color: #ecf0ff;
  padding: 15px;
}
.full-calendar-box .fc-theme-standard td, .full-calendar-box .fc-theme-standard th {
  border: 1px solid var(--borderColor);
}

.uc-content .MuiGrid-item:nth-last-child(1) .border-bottom, .uc-content .MuiGrid-item:nth-last-child(2) .border-bottom {
  border-bottom: none !important;
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.date-time-picker-box .MuiStack-root {
  padding: 0;
}
.date-time-picker-box .MuiStack-root .MuiFormControl-root {
  min-width: 200px;
}
.date-time-picker-box .MuiStack-root .MuiFormControl-root .MuiInputBase-input {
  padding: 5px 14px;
  height: auto;
  font-size: 12px;
}
.date-time-picker-box .MuiStack-root .MuiFormControl-root .MuiButtonBase-root {
  padding: 4px;
}

.campaign-goal label .MuiTypography-root {
  color: var(--blackColor);
  font-weight: 500;
  font-size: 15px;
}

.nft-slide {
  direction: ltr;
}
.nft-slide .swiper-button-prev {
  left: 0;
}
.nft-slide .swiper-button-next {
  right: 0;
}
.nft-slide .swiper-button-prev, .nft-slide .swiper-button-next {
  background-color: var(--blackColor);
  color: #fff;
  width: 30px;
  height: 30px;
  border-radius: 100%;
}
.nft-slide .swiper-button-prev::after, .nft-slide .swiper-button-next::after {
  font-size: 16px;
}

.table-th-td-lcpx-0 thead tr th:first-child {
  padding-left: 0;
}
.table-th-td-lcpx-0 thead tr th:last-child {
  padding-right: 0;
}
.table-th-td-lcpx-0 tbody tr td:first-child {
  padding-left: 0;
}
.table-th-td-lcpx-0 tbody tr td:last-child {
  padding-right: 0;
}

/* RTL Style */
[dir=rtl] .table-th-td-lcpx-0 thead tr th:first-child {
  padding-left: 20px;
  padding-right: 0;
}
[dir=rtl] .table-th-td-lcpx-0 thead tr th:last-child {
  padding-left: 0;
  padding-right: 20px;
}
[dir=rtl] .table-th-td-lcpx-0 tbody tr td:first-child {
  padding-left: 20px;
  padding-right: 0;
}
[dir=rtl] .table-th-td-lcpx-0 tbody tr td:last-child {
  padding-right: 20px;
  padding-left: 0;
}

.tc-slide .swiper-pagination {
  position: initial;
  line-height: 1;
  text-align: left;
  margin-top: 35px;
}

.rp-slide .swiper-pagination {
  position: initial;
  line-height: 1;
  text-align: left;
  margin-top: 20px;
}

.sue-slide .swiper-pagination {
  position: initial;
  line-height: 1;
  margin-top: 5px;
}
.sue-slide .swiper-pagination .swiper-pagination-bullet {
  width: 5px;
  height: 5px;
}
.sue-slide .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: var(--primaryColor);
  width: 10px;
  border-radius: 20px;
}

.date-time-picker-demo .react-datetime-picker__wrapper {
  border: 1px solid #eee;
  border-radius: 10px;
  padding: 4px 10px;
  background: #f5f5f5;
}
.date-time-picker-demo .react-datetime-picker__wrapper .react-datetime-picker__inputGroup {
  font-size: 13px;
}
.date-time-picker-demo .react-datetime-picker__wrapper .react-datetime-picker__button {
  padding: 0;
  margin-left: 5px;
}
.date-time-picker-demo .react-datetime-picker__wrapper .react-datetime-picker__button svg {
  display: inherit;
  width: 15px;
}
.date-time-picker-demo .react-calendar {
  border: 1px solid #eee;
  padding: 10px;
}

.overview-navs .bg-primary-50 .white-icon {
  display: none;
}
.overview-navs .bg-primary-50.active {
  background-color: var(--primaryColor) !important;
  color: #fff !important;
}
.overview-navs .bg-primary-50.active .main-icon {
  display: none;
}
.overview-navs .bg-primary-50.active .white-icon {
  display: block;
}
.overview-navs .bg-primary-50.active h5, .overview-navs .bg-primary-50.active span, .overview-navs .bg-primary-50.active i {
  color: #fff !important;
}
.overview-navs .bg-purple-50 .white-icon {
  display: none;
}
.overview-navs .bg-purple-50.active {
  background-color: var(--purpleColor) !important;
  color: #fff !important;
}
.overview-navs .bg-purple-50.active .main-icon {
  display: none;
}
.overview-navs .bg-purple-50.active .white-icon {
  display: block;
}
.overview-navs .bg-purple-50.active h5, .overview-navs .bg-purple-50.active span, .overview-navs .bg-purple-50.active i {
  color: #fff !important;
}
.overview-navs .bg-orange-50 .white-icon {
  display: none;
}
.overview-navs .bg-orange-50.active {
  background-color: var(--orangeColor) !important;
  color: #fff !important;
}
.overview-navs .bg-orange-50.active .main-icon {
  display: none;
}
.overview-navs .bg-orange-50.active .white-icon {
  display: block;
}
.overview-navs .bg-orange-50.active h5, .overview-navs .bg-orange-50.active span, .overview-navs .bg-orange-50.active i {
  color: #fff !important;
}

.nft-card {
  transition: var(--transition);
}
.nft-card .bid-btn {
  transform: scale(0);
  transition: var(--transition);
}
.nft-card:hover .bid-btn {
  transform: scale(1);
}

.t-search-form {
  position: relative;
  width: 239px;
}
.t-search-form i {
  top: 50%;
  left: 13px;
  font-size: 20px;
  position: absolute;
  transform: translateY(-50%);
}
.t-search-form .t-input {
  height: 36px;
  font-size: 12px;
  background-color: #f6f7f9;
  border: 1px solid #F6F7F9;
  color: var(--blackColor);
  padding: 5px 10px 5px 38px;
  border-radius: 7px;
  display: block;
  width: 100%;
}
.t-search-form .t-input:focus {
  outline: 0;
}

/* dark-theme Style */
.dark-theme .t-search-form i {
  color: #fff;
}
.dark-theme .t-search-form .t-input {
  background-color: var(--darkSecondaryColor);
  border: 1px solid var(--darkSecondaryColor);
  color: var(--whiteColor);
}

/* RTL Style */
[dir=rtl] .t-search-form i {
  left: auto;
  right: 13px;
}
[dir=rtl] .t-search-form .t-input {
  padding: 5px 38px 5px 10px;
}

/* Max width 599px */
@media only screen and (max-width: 599px) {
  .t-search-form {
    width: 100%;
    margin-right: 0;
    margin-top: 15px;
    margin-bottom: 15px;
  }
}
.rmui-card.border-card {
  border: 1px solid var(--borderColor) !important;
}
.rmui-card.card-radius-0 {
  border-radius: 0 !important;
}

.most-popular-tabs .tab-btn {
  background-color: #eceef2;
  color: var(--bodyColor);
}
.most-popular-tabs .tab-btn.active {
  background-color: var(--primaryColor);
  color: #fff;
}

.dark-theme .tab-btn {
  background-color: #000;
  color: var(--whiteColor);
}

.mastering-digital-marketing-slide .swiper-pagination {
  text-align: right;
  top: 0;
  right: 0;
  height: 30px;
}
.mastering-digital-marketing-slide .swiper-pagination .swiper-pagination-bullet {
  background-color: #8695AA;
  width: 10px;
  height: 3px;
  border-radius: 4px;
  transition: var(--transition);
  opacity: 1;
}
.mastering-digital-marketing-slide .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: var(--borderColor);
  width: 30px;
}

/* RTL Style */
[dir=rtl] .mastering-digital-marketing-slide .swiper-pagination {
  text-align: left;
  right: auto;
  left: 0;
}/*# sourceMappingURL=globals.css.map */