.top-navbar {
    left: 285px;
    right: 25px;
    transition: var(--transition);

    &.sticky {
        box-shadow: #959da51f 0 8px 24px;
    }
}


// dark-theme style
.dark-theme {
    .top-navbar {
        background-color: var(--darkCardBg);

        .search-form {
            .MuiInputBase-input {
                background-color: #15203c;

                &::placeholder {
                    color: var(--darkBodyColor);
                    opacity: 1; /* Firefox */
                }
                &::-ms-input-placeholder { /* Edge 12-18 */
                    color: var(--darkBodyColor);
                }
            }
        }

        .for-dark-notification {
            color: var(--darkBodyColor);
            
            svg {
                color: var(--darkBodyColor);
            }
        }
    }
}
// RTL Style
[dir="rtl"] {
    .top-navbar {
        left: 25px;
        right: 285px; 
    }
}

/* Max width 767px */
@media only screen and (max-width : 767px) {

}

/* Min width 576px to Max width 767px */
@media only screen and (min-width : 576px) and (max-width : 767px) {}

/* Max width 1199px */
@media (max-width: 1199px) {
    .top-navbar {
        left: 15px;
        right: 15px; 
    }

    // RTL Style
    [dir="rtl"] {
        .top-navbar {
            left: 15px;
            right: 15px; 
        }
    }
}

/* Min width 1200px */
@media (min-width: 1200px) {

    .main-wrapper-content {
        &.active {
            .top-navbar {
                left: 90px; 
            }
        }
    }

    // RTL Style
    [dir="rtl"] {
        .main-wrapper-content {
            &.active {
                .top-navbar {
                    left: 25px; 
                    right: 90px; 
                }
            }
        }
    }

}