<svg width="31" height="30" viewBox="0 0 31 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_41_2086)">
<path d="M26.75 0H4.25C2.17893 0 0.5 1.67893 0.5 3.75V26.25C0.5 28.3211 2.17893 30 4.25 30H26.75C28.8211 30 30.5 28.3211 30.5 26.25V3.75C30.5 1.67893 28.8211 0 26.75 0Z" fill="url(#paint0_linear_41_2086)"/>
<path d="M24.8002 3.89999H19.2502C18.2561 3.89999 17.4502 4.70588 17.4502 5.69999V15.225C17.4502 16.2191 18.2561 17.025 19.2502 17.025H24.8002C25.7943 17.025 26.6002 16.2191 26.6002 15.225V5.69999C26.6002 4.70588 25.7943 3.89999 24.8002 3.89999Z" fill="white"/>
<path d="M11.7499 3.89999H6.1999C5.20579 3.89999 4.3999 4.70588 4.3999 5.69999V22.725C4.3999 23.7191 5.20579 24.525 6.1999 24.525H11.7499C12.744 24.525 13.5499 23.7191 13.5499 22.725V5.69999C13.5499 4.70588 12.744 3.89999 11.7499 3.89999Z" fill="white"/>
</g>
<defs>
<linearGradient id="paint0_linear_41_2086" x1="15.5" y1="0" x2="15.5" y2="30" gradientUnits="userSpaceOnUse">
<stop stop-color="#0079BF"/>
<stop offset="1" stop-color="#0079BF"/>
</linearGradient>
<clipPath id="clip0_41_2086">
<rect width="30" height="30" fill="white" transform="translate(0.5)"/>
</clipPath>
</defs>
</svg>
